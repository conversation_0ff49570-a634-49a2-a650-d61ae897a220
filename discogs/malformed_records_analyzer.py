#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
格式错误记录深度分析工具

功能：
1. 深入分析XML文件中被标记为"格式错误"的记录
2. 区分真正的错误和解析逻辑的误判
3. 提取代表性样本并分析错误类型
4. 评估数据修复需求和策略
5. 验证这些记录是否包含有效的release数据

作者：AI Assistant
创建时间：2025-07-28
"""

import gzip
import time
import re
import os
from datetime import datetime
import xml.etree.ElementTree as ET

# 输出文件路径
REPORT_FILE = 'malformed_analysis_report.txt'
SAMPLES_FILE = 'malformed_samples.xml'

# 清理旧文件
for file_path in [REPORT_FILE, SAMPLES_FILE]:
    if os.path.exists(file_path):
        os.remove(file_path)

def write_output(message, print_to_console=True):
    """将消息写入报告文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('[%Y-%m-%d %H:%M:%S]')
    formatted_message = f"{timestamp} {message}"
    
    with open(REPORT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def write_sample(content):
    """将样本内容写入样本文件"""
    with open(SAMPLES_FILE, 'a', encoding='utf-8') as f:
        f.write(content + '\n' + '='*80 + '\n')

class MalformedRecordAnalyzer:
    def __init__(self, xml_file):
        self.xml_file = xml_file
        self.error_types = {
            'nested_release_tags': 0,
            'incomplete_records': 0,
            'missing_end_tags': 0,
            'invalid_xml_structure': 0,
            'encoding_issues': 0,
            'false_positives': 0
        }
        self.samples = []
        self.line_positions = []
        
    def analyze_xml_structure(self, max_records=50000):
        """分析XML结构，识别真正的格式错误（限制处理数量以提高效率）"""
        write_output(f"🔍 开始深度分析XML结构（限制处理{max_records:,}条记录）...")
        start_time = time.time()

        total_lines = 0
        valid_records = 0
        problematic_records = 0
        processed_records = 0
        current_record = ""
        in_release = False
        release_start_line = 0

        try:
            with gzip.open(self.xml_file, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    total_lines = line_num

                    # 检测release记录开始
                    if '<release ' in line and 'id=' in line:
                        if in_release:
                            # 这里可能是真正的嵌套问题，或者是误判
                            self._analyze_potential_nesting(current_record, line, release_start_line, line_num)

                        in_release = True
                        release_start_line = line_num
                        current_record = line

                    elif '</release>' in line and in_release:
                        current_record += line
                        in_release = False
                        processed_records += 1

                        # 验证记录完整性
                        if self._validate_record(current_record, release_start_line, line_num):
                            valid_records += 1
                        else:
                            problematic_records += 1

                        current_record = ""

                        # 限制处理数量
                        if processed_records >= max_records:
                            write_output(f"   🔄 已达到处理限制 {max_records:,} 条记录，停止分析")
                            break

                    elif in_release:
                        current_record += line

                    # 显示进度
                    if processed_records > 0 and processed_records % 10000 == 0:
                        elapsed = time.time() - start_time
                        write_output(f"   📈 已处理 {processed_records:,} 条记录，发现 {problematic_records:,} 个问题记录")
        
        except Exception as e:
            write_output(f"❌ 分析过程中出错: {e}")
            return False
        
        # 处理文件末尾未完成的记录
        if in_release and current_record:
            write_output(f"⚠️ 文件末尾发现未完成的记录，起始行: {release_start_line}")
            self.error_types['incomplete_records'] += 1
            self._add_sample('incomplete_end_of_file', current_record, release_start_line, total_lines)
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ XML结构分析完成")
        write_output(f"   📊 总行数: {total_lines:,}")
        write_output(f"   📊 有效记录: {valid_records:,}")
        write_output(f"   📊 问题记录: {problematic_records:,}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
        
        return True
    
    def _analyze_potential_nesting(self, current_record, new_line, start_line, current_line):
        """分析潜在的嵌套问题"""
        # 检查是否是真正的嵌套问题
        if self._is_false_positive_nesting(current_record, new_line):
            self.error_types['false_positives'] += 1
            write_output(f"   🔍 第{current_line}行：检测到误判的嵌套（可能是子元素包含release关键词）")
        else:
            self.error_types['nested_release_tags'] += 1
            write_output(f"   ⚠️ 第{current_line}行：发现真正的嵌套release标签")
            self._add_sample('nested_release', current_record + new_line, start_line, current_line)
    
    def _is_false_positive_nesting(self, current_record, new_line):
        """判断是否是误判的嵌套"""
        # 检查新行是否真的是一个新的release记录开始
        # 如果新行包含完整的release标签结构，可能是真正的嵌套
        # 如果只是包含"release"关键词的子元素，则是误判
        
        # 简单的启发式检查
        if re.search(r'<release\s+id="\d+"', new_line):
            # 包含完整的release开始标签，可能是真正的嵌套
            return False
        elif 'release' in new_line.lower() and '<release ' not in new_line:
            # 只是包含release关键词，可能是子元素
            return True
        
        return False
    
    def _validate_record(self, record, start_line, end_line):
        """验证记录的完整性和有效性"""
        try:
            # 基本结构检查
            if not record.strip():
                self.error_types['incomplete_records'] += 1
                return False
            
            # 检查是否有开始和结束标签
            if '<release ' not in record or '</release>' not in record:
                self.error_types['missing_end_tags'] += 1
                self._add_sample('missing_tags', record, start_line, end_line)
                return False
            
            # 尝试提取release ID
            id_match = re.search(r'<release\s+id="(\d+)"', record)
            if not id_match:
                self.error_types['invalid_xml_structure'] += 1
                self._add_sample('invalid_structure', record, start_line, end_line)
                return False
            
            # 检查XML结构的基本有效性
            if not self._check_xml_wellformed(record):
                self.error_types['invalid_xml_structure'] += 1
                self._add_sample('malformed_xml', record, start_line, end_line)
                return False
            
            return True
            
        except Exception as e:
            self.error_types['encoding_issues'] += 1
            write_output(f"   ⚠️ 记录验证出错 (行{start_line}-{end_line}): {e}")
            return False
    
    def _check_xml_wellformed(self, record):
        """检查XML记录是否格式良好"""
        try:
            # 简单的标签匹配检查
            open_tags = re.findall(r'<(\w+)', record)
            close_tags = re.findall(r'</(\w+)>', record)
            
            # 基本的标签平衡检查（简化版）
            tag_balance = {}
            for tag in open_tags:
                tag_balance[tag] = tag_balance.get(tag, 0) + 1
            for tag in close_tags:
                tag_balance[tag] = tag_balance.get(tag, 0) - 1
            
            # 检查主要标签是否平衡
            if tag_balance.get('release', 0) != 0:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _add_sample(self, error_type, content, start_line, end_line):
        """添加错误样本"""
        if len(self.samples) < 20:  # 限制样本数量
            sample = {
                'type': error_type,
                'content': content,
                'start_line': start_line,
                'end_line': end_line,
                'length': len(content)
            }
            self.samples.append(sample)
            
            # 写入样本文件
            sample_header = f"错误类型: {error_type}\n位置: 第{start_line}-{end_line}行\n长度: {len(content)}字符\n"
            write_sample(sample_header + content)
    
    def analyze_existing_database_overlap(self):
        """分析这些记录与现有数据库的重叠情况"""
        write_output("🔍 分析与现有数据库的重叠情况...")
        
        # 从样本中提取ID
        sample_ids = []
        for sample in self.samples:
            id_match = re.search(r'<release\s+id="(\d+)"', sample['content'])
            if id_match:
                sample_ids.append(id_match.group(1))
        
        write_output(f"   📊 从样本中提取到 {len(sample_ids)} 个ID")
        write_output(f"   📊 样本ID: {sample_ids[:10]}")
        
        # 注意：这里不直接连接数据库，而是提供分析建议
        write_output("   💡 建议：使用这些ID与数据库进行比对，确认是否已存在")
        
        return sample_ids
    
    def generate_repair_strategy(self):
        """生成数据修复策略"""
        write_output("\n🔧 数据修复策略分析")
        write_output("="*50)
        
        total_errors = sum(self.error_types.values())
        
        if total_errors == 0:
            write_output("✅ 未发现真正的格式错误，之前的统计可能是误判")
            return
        
        write_output(f"📊 错误类型统计:")
        for error_type, count in self.error_types.items():
            if count > 0:
                percentage = (count / total_errors) * 100
                write_output(f"   {error_type}: {count:,} ({percentage:.1f}%)")
        
        # 修复建议
        write_output(f"\n💡 修复建议:")
        
        if self.error_types['false_positives'] > total_errors * 0.8:
            write_output("✅ 大部分'错误'是解析逻辑的误判，建议修正解析算法")
            write_output("   - 改进嵌套检测逻辑")
            write_output("   - 使用更精确的XML解析方法")
        
        if self.error_types['incomplete_records'] > 0:
            write_output(f"⚠️ 发现 {self.error_types['incomplete_records']} 条不完整记录")
            write_output("   - 检查文件完整性")
            write_output("   - 考虑从备份恢复")
        
        if self.error_types['nested_release_tags'] > 0:
            write_output(f"⚠️ 发现 {self.error_types['nested_release_tags']} 条真正的嵌套问题")
            write_output("   - 需要手动检查和修复")
            write_output("   - 可能需要重新获取源数据")
        
        if self.error_types['invalid_xml_structure'] > 0:
            write_output(f"⚠️ 发现 {self.error_types['invalid_xml_structure']} 条XML结构错误")
            write_output("   - 需要XML修复工具")
            write_output("   - 考虑数据清理")
    
    def generate_final_report(self):
        """生成最终分析报告"""
        write_output("\n" + "="*60)
        write_output("📋 格式错误记录分析报告")
        write_output("="*60)
        
        total_errors = sum(self.error_types.values())
        write_output(f"📊 总错误数量: {total_errors:,}")
        write_output(f"📊 样本数量: {len(self.samples)}")
        
        # 错误分布
        write_output(f"\n📊 错误类型分布:")
        for error_type, count in self.error_types.items():
            if count > 0:
                write_output(f"   {error_type}: {count:,}")
        
        # 样本信息
        if self.samples:
            write_output(f"\n📋 样本记录信息:")
            for i, sample in enumerate(self.samples[:5], 1):
                write_output(f"   样本{i}: {sample['type']}, 行{sample['start_line']}-{sample['end_line']}, {sample['length']}字符")
        
        # 结论
        write_output(f"\n🎯 分析结论:")
        if self.error_types['false_positives'] > total_errors * 0.5:
            write_output("✅ 大部分'错误'记录实际上是解析逻辑的误判")
            write_output("✅ XML文件结构基本正常，无需大规模数据修复")
            write_output("✅ 建议改进解析算法而非修复数据")
        else:
            write_output("⚠️ 发现真正的数据质量问题，需要进一步处理")
            write_output("⚠️ 建议详细检查样本记录并制定修复计划")
        
        write_output(f"\n📄 详细样本已保存到: {SAMPLES_FILE}")
        write_output("="*60)

def main():
    """主函数"""
    print("🔍 格式错误记录深度分析工具")
    print("="*60)
    
    xml_file = 'discogs_20250701_releases.xml.gz'
    if not os.path.exists(xml_file):
        write_output(f"❌ 找不到XML文件: {xml_file}")
        return
    
    start_time = time.time()
    
    # 创建分析器
    analyzer = MalformedRecordAnalyzer(xml_file)
    
    # 执行分析
    if analyzer.analyze_xml_structure():
        # 分析数据库重叠
        sample_ids = analyzer.analyze_existing_database_overlap()
        
        # 生成修复策略
        analyzer.generate_repair_strategy()
        
        # 生成最终报告
        analyzer.generate_final_report()
    
    total_time = time.time() - start_time
    write_output(f"\n⏱️ 总分析时间: {total_time:.2f} 秒")
    write_output(f"📄 分析报告已保存到: {REPORT_FILE}")

if __name__ == "__main__":
    main()
