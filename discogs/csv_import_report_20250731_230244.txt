CSV到MongoDB导入处理报告
==================================================

处理时间: 2025-07-31 23:02:44.987398 - 2025-07-31 23:02:44.995100
处理耗时: 0:00:00.007702
CSV文件: api_releases_补全_20250729_153950.csv
测试模式: 是

处理统计:
  总行数: 16
  成功插入: 0
  处理失败: 16
  跳过重复: 0

错误详情:
  JSON解析错误: 16

失败行详情:
  1. 行号 1: JSON字段 extra_artists 解析失败: [{"artist_id": 1040750, "name": "<PERSON><PERSON>", "role": "Baritone Vocals [Ричард]", "anv": "Уго Сава...
  2. 行号 2: JSON字段 extra_artists 解析失败: [{"artist_id": 7254479, "name": "<PERSON><PERSON><PERSON> (2)", "role": "Conductor [Orchestra d'Archi diretta d...
  3. 行号 3: JSON字段 identifiers 解析失败: [{"type": "Barcode", "value": "3 481574 036478", "description": ""}, {"type": "Rights Society", "val...
  4. 行号 4: JSON字段 extra_artists 解析失败: [{"artist_id": 920721, "name": "David Dexter", "role": "Composed By", "anv": ""}, {"artist_id": 3233...
  5. 行号 5: JSON字段 extra_artists 解析失败: [{"artist_id": 530121, "name": "Dennis Crouch", "role": "Acoustic Bass", "anv": ""}, {"artist_id": 1...
  6. 行号 6: JSON字段 extra_artists 解析失败: [{"artist_id": 156316, "name": "The Memphis Horns", "role": "Arranged By [Horns]", "anv": ""}, {"art...
  7. 行号 7: JSON字段 extra_artists 解析失败: [{"artist_id": 1026184, "name": "Eduardo Alfieri", "role": "Conductor [Orchestra Diretta da]", "anv"...
  8. 行号 8: JSON字段 extra_artists 解析失败: [{"artist_id": 1177076, "name": "Mauro Negri", "role": "Clarinet, Tenor Saxophone", "anv": ""}, {"ar...
  9. 行号 9: JSON字段 formats 解析失败: [{"name": "Vinyl", "qty": "2", "text": "Viola [Solid]", "descriptions": ["LP", "Album", "Limited Edi...
  10. 行号 10: JSON字段 extra_artists 解析失败: [{"artist_id": 253208, "name": "John Patitucci", "role": "Acoustic Bass", "anv": ""}, {"artist_id": ...
  ... 还有 6 行失败数据

报告生成时间: 2025-07-31 23:02:44.997292
