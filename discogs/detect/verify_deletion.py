#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证重复数据删除结果
"""

from pymongo import MongoClient
import os

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'
KNOWN_DUPLICATE_RANGE = (1, 3594)

def main():
    print("🔍 验证重复数据删除结果...")
    
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    collection = db[COLLECTION_NAME]
    
    # 获取总记录数
    total_count = collection.count_documents({})
    print(f"📊 当前总记录数: {total_count:,}")
    
    # 检查已知范围内的记录数
    known_range_count = collection.count_documents({
        'id': {'$gte': KNOWN_DUPLICATE_RANGE[0], '$lte': KNOWN_DUPLICATE_RANGE[1]}
    })
    print(f"📊 已知范围内记录数: {known_range_count:,}")
    
    # 简单检查已知范围内是否还有重复（抽样检查）
    sample_duplicates = []
    for test_id in [1, 2, 3, 100, 500, 1000, 2000, 3000, 3594]:
        count = collection.count_documents({'id': test_id})
        if count > 1:
            sample_duplicates.append((test_id, count))
    
    if sample_duplicates:
        print(f"⚠️  发现抽样重复记录:")
        for id_val, count in sample_duplicates:
            print(f"   - ID {id_val}: {count} 条记录")
    else:
        print("✅ 抽样检查：已知范围内无重复记录")
    
    # 检查删除前后的记录数变化
    expected_after_deletion = 3083425 - 3169  # 删除前总数 - 删除的记录数
    print(f"📊 预期删除后记录数: {expected_after_deletion:,}")
    print(f"📊 实际当前记录数: {total_count:,}")
    
    if total_count == expected_after_deletion:
        print("✅ 记录数变化符合预期")
    else:
        diff = total_count - expected_after_deletion
        print(f"⚠️  记录数差异: {diff:+,}")
    
    client.close()
    print("✅ 验证完成")

if __name__ == "__main__":
    main()
