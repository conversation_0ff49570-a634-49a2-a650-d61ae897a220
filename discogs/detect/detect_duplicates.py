#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs Release 重复数据检测脚本

功能：
1. 验证已知重复范围（id 1-3594）的数据重复情况
2. 全表重复检测，发现其他潜在重复数据
3. 支持多种重复判断策略（ID重复、内容重复）
4. 生成详细的检测报告和CSV导出

作者：AI Assistant
创建时间：2025-07-10
"""

import os
import sys
import time
import csv
import json
from collections import defaultdict, Counter
from datetime import datetime, timezone
from pymongo import MongoClient
import hashlib

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# 输出文件配置
OUTPUT_DIR = 'duplicate_detection_results'
LOG_FILE = os.path.join(OUTPUT_DIR, 'duplicate_detection.log')
DUPLICATE_RECORDS_CSV = os.path.join(OUTPUT_DIR, 'duplicate_records.csv')
SUMMARY_REPORT_CSV = os.path.join(OUTPUT_DIR, 'duplicate_summary.csv')

# 检测配置
KNOWN_DUPLICATE_RANGE = (1, 3594)  # 已知重复范围
BATCH_SIZE = 1000  # 批处理大小
MEMORY_LIMIT_MB = 500  # 内存使用限制（MB）

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 清理旧的日志文件
if os.path.exists(LOG_FILE):
    os.remove(LOG_FILE)

def write_log(message, print_to_console=True, level="INFO"):
    """写入日志文件并可选择打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] [{level}] {message}"
    
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_log(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_log(f"❌ MongoDB连接失败: {e}", level="ERROR")
        raise

def get_collection_stats(db):
    """获取集合基本统计信息"""
    try:
        collection = db[COLLECTION_NAME]
        total_count = collection.count_documents({})
        
        # 获取ID范围
        min_id_doc = list(collection.find({}, {'id': 1}).sort('id', 1).limit(1))
        max_id_doc = list(collection.find({}, {'id': 1}).sort('id', -1).limit(1))
        
        min_id = min_id_doc[0]['id'] if min_id_doc else None
        max_id = max_id_doc[0]['id'] if max_id_doc else None
        
        stats = {
            'total_records': total_count,
            'min_id': min_id,
            'max_id': max_id
        }
        
        write_log(f"📊 集合统计信息:")
        write_log(f"   - 总记录数: {total_count:,}")
        write_log(f"   - ID范围: {min_id} - {max_id}")
        
        return stats
    except Exception as e:
        write_log(f"❌ 获取集合统计信息失败: {e}", level="ERROR")
        return None

def create_content_hash(record):
    """为记录创建内容哈希值，用于检测内容重复"""
    # 选择关键业务字段进行哈希
    key_fields = {
        'title': record.get('title', ''),
        'artists': json.dumps(record.get('artists', []), sort_keys=True),
        'labels': json.dumps(record.get('labels', []), sort_keys=True),
        'country': record.get('country', ''),
        'master_id': record.get('master_id', '')
    }
    
    # 创建标准化的字符串
    content_str = '|'.join([f"{k}:{v}" for k, v in sorted(key_fields.items())])
    
    # 生成MD5哈希
    return hashlib.md5(content_str.encode('utf-8')).hexdigest()

class DuplicateDetector:
    """重复数据检测器"""
    
    def __init__(self, db):
        self.db = db
        self.collection = db[COLLECTION_NAME]
        self.stats = {
            'total_processed': 0,
            'id_duplicates': 0,
            'content_duplicates': 0,
            'known_range_duplicates': 0,
            'processing_time': 0
        }
        self.duplicate_groups = {
            'by_id': defaultdict(list),
            'by_content': defaultdict(list)
        }
    
    def detect_id_duplicates(self):
        """检测基于ID的重复记录"""
        write_log("🔍 开始检测基于ID的重复记录...")

        try:
            # 使用聚合管道查找重复ID，启用磁盘使用以处理大数据集
            pipeline = [
                {
                    '$group': {
                        '_id': '$id',
                        'count': {'$sum': 1},
                        'records': {'$push': {
                            '_id': '$_id',
                            'y_id': '$y_id',
                            'title': '$title'
                        }}
                    }
                },
                {
                    '$match': {
                        'count': {'$gt': 1}
                    }
                },
                {
                    '$sort': {'_id': 1}
                }
            ]

            # 启用磁盘使用以处理大数据集
            write_log("   使用磁盘缓存处理大数据集...")
            duplicate_groups = list(self.collection.aggregate(pipeline, allowDiskUse=True))

            write_log(f"📊 发现 {len(duplicate_groups)} 个ID重复组")

            for group in duplicate_groups:
                original_id = group['_id']
                records = group['records']
                count = group['count']

                self.duplicate_groups['by_id'][original_id] = records
                self.stats['id_duplicates'] += count - 1  # 减去1个原始记录

                write_log(f"   - ID {original_id}: {count} 条重复记录",
                         print_to_console=False)

            return duplicate_groups

        except Exception as e:
            write_log(f"❌ ID重复检测失败，尝试分批处理: {e}", level="WARN")
            return self._detect_id_duplicates_batch()

    def _detect_id_duplicates_batch(self):
        """分批检测ID重复记录（备用方案）"""
        write_log("🔄 使用分批处理策略检测ID重复...")

        # 获取ID范围
        min_id_doc = list(self.collection.find({}, {'id': 1}).sort('id', 1).limit(1))
        max_id_doc = list(self.collection.find({}, {'id': 1}).sort('id', -1).limit(1))

        min_id = min_id_doc[0]['id'] if min_id_doc else 1
        max_id = max_id_doc[0]['id'] if max_id_doc else 1

        write_log(f"   ID范围: {min_id} - {max_id}")

        # 分批处理
        batch_size = 100000  # 每批处理10万个ID
        duplicate_groups = []

        for start_id in range(min_id, max_id + 1, batch_size):
            end_id = min(start_id + batch_size - 1, max_id)
            write_log(f"   处理ID范围: {start_id} - {end_id}")

            # 查询当前批次的重复记录
            pipeline = [
                {
                    '$match': {
                        'id': {'$gte': start_id, '$lte': end_id}
                    }
                },
                {
                    '$group': {
                        '_id': '$id',
                        'count': {'$sum': 1},
                        'records': {'$push': {
                            '_id': '$_id',
                            'y_id': '$y_id',
                            'title': '$title'
                        }}
                    }
                },
                {
                    '$match': {
                        'count': {'$gt': 1}
                    }
                }
            ]

            try:
                batch_duplicates = list(self.collection.aggregate(pipeline, allowDiskUse=True))
                duplicate_groups.extend(batch_duplicates)

                # 处理当前批次的重复记录
                for group in batch_duplicates:
                    original_id = group['_id']
                    records = group['records']
                    count = group['count']

                    self.duplicate_groups['by_id'][original_id] = records
                    self.stats['id_duplicates'] += count - 1

                    write_log(f"   - ID {original_id}: {count} 条重复记录",
                             print_to_console=False)

            except Exception as batch_error:
                write_log(f"❌ 批次 {start_id}-{end_id} 处理失败: {batch_error}", level="ERROR")
                continue

        write_log(f"📊 分批处理完成，发现 {len(duplicate_groups)} 个ID重复组")
        return duplicate_groups

    def detect_content_duplicates(self):
        """检测基于内容的重复记录"""
        write_log("🔍 开始检测基于内容的重复记录...")

        content_hash_map = defaultdict(list)
        processed_count = 0

        try:
            # 分批处理记录以控制内存使用
            cursor = self.collection.find({}, {
                'id': 1, 'y_id': 1, 'title': 1, 'artists': 1,
                'labels': 1, 'country': 1, 'master_id': 1
            }).batch_size(BATCH_SIZE)

            write_log(f"   使用批处理大小: {BATCH_SIZE}")

            for record in cursor:
                try:
                    content_hash = create_content_hash(record)
                    title = record.get('title', '')
                    short_title = title[:50] + '...' if len(title) > 50 else title

                    content_hash_map[content_hash].append({
                        'id': record.get('id'),
                        'y_id': record.get('y_id'),
                        'title': short_title
                    })

                    processed_count += 1
                    if processed_count % 10000 == 0:
                        write_log(f"   已处理 {processed_count:,} 条记录...")

                except Exception as record_error:
                    write_log(f"❌ 处理记录失败 (ID: {record.get('id', 'unknown')}): {record_error}",
                             level="WARN", print_to_console=False)
                    continue

            # 找出内容重复的组
            content_duplicates = {k: v for k, v in content_hash_map.items()
                                if len(v) > 1}

            write_log(f"📊 发现 {len(content_duplicates)} 个内容重复组")

            for content_hash, records in content_duplicates.items():
                self.duplicate_groups['by_content'][content_hash] = records
                self.stats['content_duplicates'] += len(records) - 1

                write_log(f"   - 内容哈希 {content_hash[:8]}...: "
                         f"{len(records)} 条重复记录", print_to_console=False)

            self.stats['total_processed'] = processed_count
            return content_duplicates

        except Exception as e:
            write_log(f"❌ 内容重复检测失败: {e}", level="ERROR")
            self.stats['total_processed'] = processed_count
            return {}

    def verify_known_range_duplicates(self):
        """验证已知重复范围（id 1-3594）的重复情况"""
        write_log(f"🔍 验证已知重复范围 {KNOWN_DUPLICATE_RANGE[0]}-{KNOWN_DUPLICATE_RANGE[1]}...")

        start_id, end_id = KNOWN_DUPLICATE_RANGE

        # 查询已知范围内的记录
        query = {'id': {'$gte': start_id, '$lte': end_id}}
        records = list(self.collection.find(query, {'id': 1, 'y_id': 1, 'title': 1}))

        write_log(f"📊 已知范围内共有 {len(records)} 条记录")

        # 统计ID重复情况
        id_counter = Counter(record['id'] for record in records)
        duplicates_in_range = {id_val: count for id_val, count in id_counter.items() if count > 1}

        self.stats['known_range_duplicates'] = sum(count - 1 for count in duplicates_in_range.values())

        write_log(f"📊 已知范围内发现 {len(duplicates_in_range)} 个重复ID")
        write_log(f"📊 已知范围内重复记录总数: {self.stats['known_range_duplicates']}")

        # 详细记录重复情况
        for id_val, count in sorted(duplicates_in_range.items()):
            write_log(f"   - ID {id_val}: {count} 条记录", print_to_console=False)

        return duplicates_in_range

    def export_duplicate_records_to_csv(self):
        """导出重复记录到CSV文件"""
        write_log("📄 导出重复记录到CSV文件...")

        with open(DUPLICATE_RECORDS_CSV, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'duplicate_type', 'group_id', 'original_id', 'y_id',
                'title', 'duplicate_count', 'is_in_known_range'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            # 导出ID重复记录
            for original_id, records in self.duplicate_groups['by_id'].items():
                is_in_known_range = (KNOWN_DUPLICATE_RANGE[0] <= original_id <=
                                   KNOWN_DUPLICATE_RANGE[1])

                for record in records:
                    writer.writerow({
                        'duplicate_type': 'ID_DUPLICATE',
                        'group_id': original_id,
                        'original_id': record.get('id', original_id),
                        'y_id': record.get('y_id', ''),
                        'title': record.get('title', ''),
                        'duplicate_count': len(records),
                        'is_in_known_range': is_in_known_range
                    })

            # 导出内容重复记录
            for content_hash, records in self.duplicate_groups['by_content'].items():
                for record in records:
                    original_id = record.get('id', 0)
                    is_in_known_range = (KNOWN_DUPLICATE_RANGE[0] <= original_id <=
                                       KNOWN_DUPLICATE_RANGE[1])

                    writer.writerow({
                        'duplicate_type': 'CONTENT_DUPLICATE',
                        'group_id': content_hash[:16],
                        'original_id': original_id,
                        'y_id': record.get('y_id', ''),
                        'title': record.get('title', ''),
                        'duplicate_count': len(records),
                        'is_in_known_range': is_in_known_range
                    })

        write_log(f"✅ 重复记录已导出到: {DUPLICATE_RECORDS_CSV}")

    def generate_summary_report(self):
        """生成汇总报告"""
        write_log("📊 生成汇总报告...")

        # 计算统计信息
        total_id_duplicate_groups = len(self.duplicate_groups['by_id'])
        total_content_duplicate_groups = len(self.duplicate_groups['by_content'])

        # 已知范围内的重复统计
        known_range_id_duplicates = sum(
            1 for original_id in self.duplicate_groups['by_id'].keys()
            if KNOWN_DUPLICATE_RANGE[0] <= original_id <= KNOWN_DUPLICATE_RANGE[1]
        )

        summary_data = [
            ['检测项目', '数量', '说明'],
            ['总处理记录数', self.stats['total_processed'], '实际处理的记录总数'],
            ['ID重复组数', total_id_duplicate_groups, '具有相同ID的记录组数'],
            ['ID重复记录数', self.stats['id_duplicates'], '重复的记录总数（不含原始记录）'],
            ['内容重复组数', total_content_duplicate_groups, '具有相同内容的记录组数'],
            ['内容重复记录数', self.stats['content_duplicates'], '内容重复的记录总数（不含原始记录）'],
            ['已知范围重复组数', known_range_id_duplicates, f'ID在{KNOWN_DUPLICATE_RANGE[0]}-{KNOWN_DUPLICATE_RANGE[1]}范围内的重复组'],
            ['已知范围重复记录数', self.stats['known_range_duplicates'], '已知范围内的重复记录总数'],
            ['处理耗时（秒）', round(self.stats['processing_time'], 2), '总处理时间']
        ]

        # 写入CSV文件
        with open(SUMMARY_REPORT_CSV, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(summary_data)

        # 打印汇总信息
        write_log("=" * 60)
        write_log("📊 重复数据检测汇总报告")
        write_log("=" * 60)

        for row in summary_data[1:]:  # 跳过表头
            write_log(f"{row[0]}: {row[1]:,} ({row[2]})")

        write_log("=" * 60)
        write_log(f"✅ 汇总报告已保存到: {SUMMARY_REPORT_CSV}")

        return summary_data

    def run_full_detection(self):
        """运行完整的重复数据检测流程"""
        start_time = time.time()

        write_log("🚀 开始重复数据检测...")
        write_log("=" * 60)

        try:
            # 1. 检测ID重复
            self.detect_id_duplicates()

            # 2. 检测内容重复
            self.detect_content_duplicates()

            # 3. 验证已知范围重复
            self.verify_known_range_duplicates()

            # 4. 导出重复记录
            self.export_duplicate_records_to_csv()

            # 5. 生成汇总报告
            self.stats['processing_time'] = time.time() - start_time
            self.generate_summary_report()

            write_log("✅ 重复数据检测完成！")

        except Exception as e:
            write_log(f"❌ 检测过程中发生错误: {e}", level="ERROR")
            raise

def main():
    """主函数"""
    write_log("🔍 Discogs Release 重复数据检测工具")
    write_log("=" * 60)

    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 获取集合统计信息
        stats = get_collection_stats(db)
        if not stats:
            write_log("❌ 无法获取集合统计信息，退出程序", level="ERROR")
            return

        # 创建检测器并运行检测
        detector = DuplicateDetector(db)
        detector.run_full_detection()

        # 关闭数据库连接
        client.close()
        write_log("🔌 数据库连接已关闭")

    except KeyboardInterrupt:
        write_log("⚠️ 用户中断了程序执行", level="WARN")
    except Exception as e:
        write_log(f"❌ 程序执行失败: {e}", level="ERROR")
        sys.exit(1)

def print_usage():
    """打印使用说明"""
    usage_text = """
🔍 Discogs Release 重复数据检测工具

功能说明：
1. 验证已知重复范围（id 1-3594）的数据重复情况
2. 全表重复检测，发现其他潜在重复数据
3. 支持多种重复判断策略（ID重复、内容重复）
4. 生成详细的检测报告和CSV导出

使用方法：
    python detect_duplicates.py

输出文件：
    - duplicate_detection_results/duplicate_detection.log     # 详细日志
    - duplicate_detection_results/duplicate_records.csv      # 重复记录详情
    - duplicate_detection_results/duplicate_summary.csv      # 汇总报告

环境变量配置：
    MONGO_URI    - MongoDB连接字符串（默认：**********************************************************）
    DB_NAME      - 数据库名称（默认：music_test）

注意事项：
    - 确保MongoDB服务正在运行
    - 确保有足够的磁盘空间存储输出文件
    - 大数据量检测可能需要较长时间，请耐心等待
    """
    print(usage_text)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
    else:
        main()
