# Release 重复数据检测工具

## 概述

这是一个专门用于检测 `release_new` 表中重复数据的 Python 脚本。该工具能够全面分析数据库中的重复记录，并生成详细的检测报告。

## 功能特性

### 🔍 多维度重复检测
- **ID重复检测**：检测具有相同 `id` 字段的记录
- **内容重复检测**：基于业务字段（title、artists、labels等）检测语义重复
- **已知范围验证**：专门验证 id 1-3594 范围内的已知重复问题

### 📊 详细报告生成
- **实时日志**：处理过程的详细日志记录
- **CSV导出**：重复记录的结构化数据导出
- **统计摘要**：全面的检测结果统计报告

### ⚡ 性能优化
- **内存控制**：分批处理大数据集，避免内存溢出
- **进度跟踪**：实时显示处理进度
- **错误处理**：完善的异常处理和恢复机制

## 安装依赖

```bash
# 确保已安装必要的Python包
pip install pymongo
```

## 使用方法

### 基本使用

```bash
cd discogs/release
python detect_duplicates.py
```

### 查看帮助

```bash
python detect_duplicates.py --help
```

### 环境变量配置

可以通过环境变量自定义配置：

```bash
# 设置MongoDB连接
export MONGO_URI="********************************:port/database"
export DB_NAME="your_database_name"

# 运行检测
python detect_duplicates.py
```

## 输出文件

脚本运行后会在 `duplicate_detection_results/` 目录下生成以下文件：

### 1. duplicate_detection.log
详细的处理日志，包含：
- 连接状态和集合统计信息
- 各阶段检测进度
- 发现的重复记录详情
- 错误和警告信息

### 2. duplicate_records.csv
重复记录的详细列表，包含字段：
- `duplicate_type`: 重复类型（ID_DUPLICATE 或 CONTENT_DUPLICATE）
- `group_id`: 重复组标识符
- `original_id`: 原始记录ID
- `y_id`: 系统生成的唯一标识符
- `title`: 记录标题
- `duplicate_count`: 该组重复记录总数
- `is_in_known_range`: 是否在已知重复范围内

### 3. duplicate_summary.csv
检测结果汇总报告，包含：
- 总处理记录数
- ID重复组数和记录数
- 内容重复组数和记录数
- 已知范围内重复统计
- 处理耗时

## 检测策略说明

### ID重复检测
使用MongoDB聚合管道查找具有相同 `id` 字段的记录组。这种重复通常表示数据导入过程中的问题。

### 内容重复检测
基于以下关键业务字段创建内容哈希：
- `title`: 发行标题
- `artists`: 艺术家信息
- `labels`: 唱片公司信息
- `country`: 国家
- `master_id`: 主版本ID

相同哈希值的记录被认为是内容重复。

### 已知范围验证
专门针对 id 1-3594 范围进行重复验证，确认已知问题的具体情况。

## 性能考虑

### 内存使用
- 使用批处理（默认1000条记录/批）控制内存使用
- 大数据集处理时内存使用量控制在500MB以下

### 处理时间
根据数据量大小，处理时间可能从几分钟到几小时不等：
- 小数据集（< 10万记录）：几分钟
- 中等数据集（10-100万记录）：10-30分钟
- 大数据集（> 100万记录）：1小时以上

### 优化建议
1. 确保MongoDB有适当的索引（特别是 `id` 字段）
2. 在非高峰时段运行检测
3. 监控系统资源使用情况

## 故障排除

### 常见问题

**1. 连接失败**
```
❌ MongoDB连接失败: [Errno 111] Connection refused
```
解决方案：检查MongoDB服务是否运行，连接字符串是否正确

**2. 内存不足**
```
MemoryError: Unable to allocate array
```
解决方案：减小 `BATCH_SIZE` 参数或增加系统内存

**3. 权限问题**
```
PermissionError: [Errno 13] Permission denied
```
解决方案：确保对输出目录有写权限

### 调试模式

可以修改脚本中的日志级别来获取更详细的调试信息：

```python
# 在脚本开头添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 配置参数

可以在脚本中修改以下参数：

```python
# 检测配置
KNOWN_DUPLICATE_RANGE = (1, 3594)  # 已知重复范围
BATCH_SIZE = 1000                  # 批处理大小
MEMORY_LIMIT_MB = 500              # 内存使用限制
```

## 注意事项

1. **数据安全**：脚本只读取数据，不会修改原始数据
2. **磁盘空间**：确保有足够空间存储输出文件
3. **网络稳定**：长时间运行需要稳定的数据库连接
4. **备份建议**：运行前建议备份重要数据

## 技术支持

如遇到问题，请检查：
1. 日志文件中的详细错误信息
2. MongoDB连接和权限设置
3. 系统资源使用情况
4. Python环境和依赖包版本
