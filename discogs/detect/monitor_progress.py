#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重复数据检测进度监控脚本

功能：
- 实时监控检测进度
- 显示处理统计信息
- 估算剩余时间

使用方法：
    python monitor_progress.py
"""

import os
import time
import re
from datetime import datetime

LOG_FILE = 'duplicate_detection_results/duplicate_detection.log'

def parse_log_file():
    """解析日志文件获取进度信息"""
    if not os.path.exists(LOG_FILE):
        return None
    
    progress_info = {
        'start_time': None,
        'total_records': 0,
        'processed_records': 0,
        'id_duplicates_found': 0,
        'current_stage': 'Unknown',
        'last_update': None,
        'errors': []
    }
    
    try:
        with open(LOG_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            
            # 解析开始时间
            if '🔍 Discogs Release 重复数据检测工具' in line:
                timestamp_match = re.match(r'\[([^\]]+)\]', line)
                if timestamp_match:
                    progress_info['start_time'] = timestamp_match.group(1)
            
            # 解析总记录数
            elif '总记录数:' in line:
                match = re.search(r'总记录数: ([\d,]+)', line)
                if match:
                    progress_info['total_records'] = int(match.group(1).replace(',', ''))
            
            # 解析ID重复检测结果
            elif '发现' in line and 'ID重复组' in line:
                match = re.search(r'发现 (\d+) 个ID重复组', line)
                if match:
                    progress_info['id_duplicates_found'] = int(match.group(1))
                    progress_info['current_stage'] = 'ID重复检测完成'
            
            # 解析内容重复检测进度
            elif '已处理' in line and '条记录' in line:
                match = re.search(r'已处理 ([\d,]+) 条记录', line)
                if match:
                    progress_info['processed_records'] = int(match.group(1).replace(',', ''))
                    progress_info['current_stage'] = '内容重复检测中'
                    timestamp_match = re.match(r'\[([^\]]+)\]', line)
                    if timestamp_match:
                        progress_info['last_update'] = timestamp_match.group(1)
            
            # 解析错误信息
            elif '[ERROR]' in line:
                progress_info['errors'].append(line)
        
        return progress_info
        
    except Exception as e:
        print(f"❌ 解析日志文件失败: {e}")
        return None

def calculate_eta(start_time_str, processed, total):
    """计算预估完成时间"""
    if not start_time_str or processed == 0 or total == 0:
        return "未知"
    
    try:
        start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
        current_time = datetime.now()
        elapsed = (current_time - start_time).total_seconds()
        
        if elapsed > 0:
            rate = processed / elapsed  # 记录/秒
            remaining = total - processed
            eta_seconds = remaining / rate if rate > 0 else 0
            
            if eta_seconds > 3600:
                return f"{eta_seconds/3600:.1f} 小时"
            elif eta_seconds > 60:
                return f"{eta_seconds/60:.1f} 分钟"
            else:
                return f"{eta_seconds:.0f} 秒"
        
        return "计算中..."
        
    except Exception:
        return "未知"

def format_number(num):
    """格式化数字显示"""
    return f"{num:,}"

def display_progress(progress_info):
    """显示进度信息"""
    if not progress_info:
        print("❌ 无法获取进度信息")
        return
    
    print("\033[2J\033[H")  # 清屏并移动光标到顶部
    print("=" * 60)
    print("🔍 重复数据检测进度监控")
    print("=" * 60)
    
    # 基本信息
    print(f"📊 总记录数: {format_number(progress_info['total_records'])}")
    print(f"🎯 当前阶段: {progress_info['current_stage']}")
    
    if progress_info['start_time']:
        print(f"⏰ 开始时间: {progress_info['start_time']}")
    
    if progress_info['last_update']:
        print(f"🔄 最后更新: {progress_info['last_update']}")
    
    # 进度信息
    if progress_info['processed_records'] > 0:
        total = progress_info['total_records']
        processed = progress_info['processed_records']
        percentage = (processed / total * 100) if total > 0 else 0
        
        print(f"📈 处理进度: {format_number(processed)} / {format_number(total)} ({percentage:.1f}%)")
        
        # 进度条
        bar_length = 40
        filled_length = int(bar_length * percentage / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        print(f"▶️  [{bar}] {percentage:.1f}%")
        
        # 预估完成时间
        eta = calculate_eta(progress_info['start_time'], processed, total)
        print(f"⏳ 预估剩余时间: {eta}")
    
    # ID重复检测结果
    if progress_info['id_duplicates_found'] > 0:
        print(f"🔍 ID重复组数: {format_number(progress_info['id_duplicates_found'])}")
    
    # 错误信息
    if progress_info['errors']:
        print(f"\n⚠️  发现 {len(progress_info['errors'])} 个错误:")
        for error in progress_info['errors'][-3:]:  # 只显示最近3个错误
            print(f"   {error}")
    
    print("\n" + "=" * 60)
    print("按 Ctrl+C 退出监控")

def main():
    """主函数"""
    print("🔍 启动重复数据检测进度监控...")
    
    if not os.path.exists(LOG_FILE):
        print(f"❌ 日志文件不存在: {LOG_FILE}")
        print("请确保重复数据检测脚本正在运行")
        return
    
    try:
        while True:
            progress_info = parse_log_file()
            display_progress(progress_info)
            time.sleep(5)  # 每5秒更新一次
            
    except KeyboardInterrupt:
        print("\n\n👋 监控已停止")
    except Exception as e:
        print(f"\n❌ 监控过程中发生错误: {e}")

if __name__ == "__main__":
    main()
