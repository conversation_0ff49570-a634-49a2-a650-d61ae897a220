#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs Release 重复数据删除脚本

功能：
1. 基于重复检测结果安全删除重复记录
2. 保留最早插入的原始记录，删除较新的重复记录
3. 提供预览模式和分批删除功能
4. 生成详细的删除日志和统计报告

安全策略：
- 对于每个重复组，保留 created_at 最早的记录
- 如果 created_at 相同，则保留 _id 最小的记录
- 提供预览模式，用户确认后才执行删除

作者：AI Assistant
创建时间：2025-07-10
"""

import os
import sys
import time
import csv
from collections import defaultdict
from datetime import datetime, timezone
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# 输出文件配置
OUTPUT_DIR = 'duplicate_removal_results'
LOG_FILE = os.path.join(OUTPUT_DIR, 'duplicate_removal.log')
DELETED_RECORDS_CSV = os.path.join(OUTPUT_DIR, 'deleted_records.csv')
REMOVAL_SUMMARY_CSV = os.path.join(OUTPUT_DIR, 'removal_summary.csv')

# 删除配置
KNOWN_DUPLICATE_RANGE = (1, 3594)  # 已知重复范围
BATCH_SIZE = 100  # 批删除大小
DRY_RUN = True  # 默认预览模式，不实际删除

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 清理旧的日志文件
if os.path.exists(LOG_FILE):
    os.remove(LOG_FILE)

def write_log(message, print_to_console=True, level="INFO"):
    """写入日志文件并可选择打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] [{level}] {message}"
    
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_log(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_log(f"❌ MongoDB连接失败: {e}", level="ERROR")
        raise

def get_duplicate_groups(db):
    """获取重复记录组"""
    write_log("🔍 查找重复记录组...")
    
    collection = db[COLLECTION_NAME]
    
    # 使用聚合管道查找重复ID
    pipeline = [
        {
            '$group': {
                '_id': '$id',
                'count': {'$sum': 1},
                'records': {'$push': {
                    'mongo_id': '$_id',
                    'y_id': '$y_id',
                    'title': '$title',
                    'created_at': '$created_at'
                }}
            }
        },
        {
            '$match': {
                'count': {'$gt': 1}
            }
        },
        {
            '$sort': {'_id': 1}
        }
    ]
    
    duplicate_groups = list(collection.aggregate(pipeline, allowDiskUse=True))
    write_log(f"📊 发现 {len(duplicate_groups)} 个重复组")
    
    return duplicate_groups

def identify_records_to_delete(duplicate_groups, only_known_range=False):
    """识别需要删除的记录"""
    write_log("🎯 识别需要删除的记录...")
    
    records_to_delete = []
    total_duplicates = 0
    
    for group in duplicate_groups:
        original_id = group['_id']
        records = group['records']
        
        # 如果只处理已知范围，跳过范围外的记录
        if only_known_range:
            if not (KNOWN_DUPLICATE_RANGE[0] <= original_id <= KNOWN_DUPLICATE_RANGE[1]):
                continue
        
        total_duplicates += len(records)
        
        # 按创建时间排序，如果创建时间相同则按MongoDB _id排序
        sorted_records = sorted(records, key=lambda x: (
            x.get('created_at', datetime.min.replace(tzinfo=timezone.utc)),
            x.get('mongo_id', '')
        ))
        
        # 保留第一个（最早的）记录，删除其余记录
        records_to_keep = sorted_records[0]
        records_to_remove = sorted_records[1:]
        
        for record in records_to_remove:
            records_to_delete.append({
                'original_id': original_id,
                'mongo_id': record['mongo_id'],
                'y_id': record.get('y_id', ''),
                'title': record.get('title', ''),
                'created_at': record.get('created_at', ''),
                'keep_record_y_id': records_to_keep.get('y_id', ''),
                'keep_record_created_at': records_to_keep.get('created_at', '')
            })
    
    write_log(f"📊 总重复记录数: {total_duplicates}")
    write_log(f"📊 需要删除的记录数: {len(records_to_delete)}")
    write_log(f"📊 将保留的记录数: {total_duplicates - len(records_to_delete)}")
    
    return records_to_delete

def preview_deletion(records_to_delete):
    """预览将要删除的记录"""
    write_log("👀 预览将要删除的记录...")
    
    if not records_to_delete:
        write_log("✅ 没有需要删除的记录")
        return
    
    # 显示前10个将要删除的记录作为示例
    write_log("📋 删除预览（前10条）:")
    for i, record in enumerate(records_to_delete[:10]):
        write_log(f"   {i+1}. ID={record['original_id']}, "
                 f"y_id={record['y_id']}, "
                 f"title={record['title'][:30]}...")
    
    if len(records_to_delete) > 10:
        write_log(f"   ... 还有 {len(records_to_delete) - 10} 条记录")
    
    # 按原始ID统计
    id_stats = defaultdict(int)
    for record in records_to_delete:
        id_stats[record['original_id']] += 1
    
    write_log(f"📊 涉及的重复ID数量: {len(id_stats)}")
    write_log(f"📊 平均每个ID的重复数: {len(records_to_delete) / len(id_stats):.1f}")

def export_deletion_plan(records_to_delete):
    """导出删除计划到CSV文件"""
    write_log("📄 导出删除计划到CSV文件...")
    
    with open(DELETED_RECORDS_CSV, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'original_id', 'mongo_id', 'y_id', 'title', 'created_at',
            'keep_record_y_id', 'keep_record_created_at', 'deletion_status'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for record in records_to_delete:
            record_copy = record.copy()
            record_copy['deletion_status'] = 'PLANNED' if DRY_RUN else 'DELETED'
            writer.writerow(record_copy)
    
    write_log(f"✅ 删除计划已导出到: {DELETED_RECORDS_CSV}")

class DuplicateRemover:
    """重复数据删除器"""
    
    def __init__(self, db):
        self.db = db
        self.collection = db[COLLECTION_NAME]
        self.stats = {
            'total_duplicates_found': 0,
            'records_to_delete': 0,
            'records_deleted': 0,
            'deletion_errors': 0,
            'processing_time': 0
        }
    
    def remove_duplicates(self, records_to_delete, dry_run=True):
        """删除重复记录"""
        if dry_run:
            write_log("🔍 预览模式：不会实际删除记录")
            self.stats['records_to_delete'] = len(records_to_delete)
            return
        
        write_log("🗑️ 开始删除重复记录...")
        write_log(f"⚠️  警告：即将删除 {len(records_to_delete)} 条记录")
        
        deleted_count = 0
        error_count = 0
        
        # 分批删除
        for i in range(0, len(records_to_delete), BATCH_SIZE):
            batch = records_to_delete[i:i + BATCH_SIZE]
            batch_ids = [record['mongo_id'] for record in batch]
            
            try:
                # 执行批量删除
                result = self.collection.delete_many({'_id': {'$in': batch_ids}})
                deleted_count += result.deleted_count
                
                write_log(f"   批次 {i//BATCH_SIZE + 1}: 删除了 {result.deleted_count} 条记录")
                
            except Exception as e:
                error_count += len(batch)
                write_log(f"❌ 批次 {i//BATCH_SIZE + 1} 删除失败: {e}", level="ERROR")
        
        self.stats['records_deleted'] = deleted_count
        self.stats['deletion_errors'] = error_count
        
        write_log(f"✅ 删除完成：成功删除 {deleted_count} 条记录")
        if error_count > 0:
            write_log(f"⚠️  删除错误：{error_count} 条记录删除失败")
    
    def generate_summary_report(self):
        """生成删除汇总报告"""
        write_log("📊 生成删除汇总报告...")
        
        summary_data = [
            ['删除项目', '数量', '说明'],
            ['发现重复记录数', self.stats['total_duplicates_found'], '检测到的重复记录总数'],
            ['计划删除记录数', self.stats['records_to_delete'], '计划删除的记录数'],
            ['实际删除记录数', self.stats['records_deleted'], '成功删除的记录数'],
            ['删除错误数', self.stats['deletion_errors'], '删除失败的记录数'],
            ['处理耗时（秒）', round(self.stats['processing_time'], 2), '总处理时间']
        ]
        
        # 写入CSV文件
        with open(REMOVAL_SUMMARY_CSV, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerows(summary_data)
        
        # 打印汇总信息
        write_log("=" * 60)
        write_log("📊 重复数据删除汇总报告")
        write_log("=" * 60)
        
        for row in summary_data[1:]:  # 跳过表头
            write_log(f"{row[0]}: {row[1]:,} ({row[2]})")
        
        write_log("=" * 60)
        write_log(f"✅ 汇总报告已保存到: {REMOVAL_SUMMARY_CSV}")
        
        return summary_data

    def run_removal_process(self, only_known_range=False, dry_run=True):
        """运行完整的重复数据删除流程"""
        start_time = time.time()

        write_log("🚀 开始重复数据删除流程...")
        write_log("=" * 60)

        try:
            # 1. 获取重复记录组
            duplicate_groups = get_duplicate_groups(self.db)

            # 2. 识别需要删除的记录
            records_to_delete = identify_records_to_delete(duplicate_groups, only_known_range)

            self.stats['total_duplicates_found'] = sum(group['count'] for group in duplicate_groups)
            self.stats['records_to_delete'] = len(records_to_delete)

            # 3. 预览删除计划
            preview_deletion(records_to_delete)

            # 4. 导出删除计划
            export_deletion_plan(records_to_delete)

            # 5. 执行删除（如果不是预览模式）
            self.remove_duplicates(records_to_delete, dry_run)

            # 6. 生成汇总报告
            self.stats['processing_time'] = time.time() - start_time
            self.generate_summary_report()

            write_log("✅ 重复数据删除流程完成！")

        except Exception as e:
            write_log(f"❌ 删除过程中发生错误: {e}", level="ERROR")
            raise

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Discogs Release 重复数据删除工具')
    parser.add_argument('--execute', action='store_true',
                       help='执行实际删除（默认为预览模式）')
    parser.add_argument('--known-range-only', action='store_true',
                       help='仅删除已知范围（id 1-3594）内的重复记录')
    parser.add_argument('--all', action='store_true',
                       help='删除所有检测到的重复记录')

    args = parser.parse_args()

    # 设置删除模式
    dry_run = not args.execute
    only_known_range = args.known_range_only

    write_log("🗑️ Discogs Release 重复数据删除工具")
    write_log("=" * 60)

    if dry_run:
        write_log("🔍 运行模式：预览模式（不会实际删除记录）")
    else:
        write_log("⚠️  运行模式：执行模式（将实际删除记录）")

    if only_known_range:
        write_log(f"🎯 删除范围：仅已知范围（ID {KNOWN_DUPLICATE_RANGE[0]}-{KNOWN_DUPLICATE_RANGE[1]}）")
    else:
        write_log("🎯 删除范围：所有检测到的重复记录")

    # 安全确认
    if not dry_run:
        write_log("⚠️  警告：您即将执行实际的数据删除操作！")
        write_log("⚠️  建议在执行前备份数据库！")

        confirmation = input("\n确认要继续吗？输入 'YES' 继续，其他任何输入将取消操作: ")
        if confirmation != 'YES':
            write_log("❌ 操作已取消")
            return

    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 创建删除器并运行删除流程
        remover = DuplicateRemover(db)
        remover.run_removal_process(only_known_range, dry_run)

        # 关闭数据库连接
        client.close()
        write_log("🔌 数据库连接已关闭")

    except KeyboardInterrupt:
        write_log("⚠️ 用户中断了程序执行", level="WARN")
    except Exception as e:
        write_log(f"❌ 程序执行失败: {e}", level="ERROR")
        sys.exit(1)

def print_usage():
    """打印使用说明"""
    usage_text = """
🗑️ Discogs Release 重复数据删除工具

功能说明：
1. 基于重复检测结果安全删除重复记录
2. 保留最早插入的原始记录，删除较新的重复记录
3. 提供预览模式和分批删除功能
4. 生成详细的删除日志和统计报告

使用方法：
    # 预览模式（默认，不实际删除）
    python remove_duplicates.py

    # 仅预览已知范围内的重复记录
    python remove_duplicates.py --known-range-only

    # 执行实际删除（所有重复记录）
    python remove_duplicates.py --execute --all

    # 执行实际删除（仅已知范围）
    python remove_duplicates.py --execute --known-range-only

安全策略：
    - 对于每个重复组，保留 created_at 最早的记录
    - 如果 created_at 相同，则保留 _id 最小的记录
    - 默认预览模式，需要明确指定 --execute 才会实际删除
    - 执行删除前会要求用户确认

输出文件：
    - duplicate_removal_results/duplicate_removal.log        # 详细日志
    - duplicate_removal_results/deleted_records.csv         # 删除记录详情
    - duplicate_removal_results/removal_summary.csv         # 删除汇总报告

注意事项：
    - 强烈建议在执行删除前备份数据库
    - 先使用预览模式检查删除计划
    - 删除操作不可逆，请谨慎操作
    """
    print(usage_text)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
    else:
        main()
