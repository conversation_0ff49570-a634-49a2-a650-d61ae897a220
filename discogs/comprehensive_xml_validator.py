#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
综合XML文件验证工具

功能：
1. 使用多种方法验证XML文件中的release记录数量
2. 检查文件完整性和结构正确性
3. 分析可能的数据来源差异
4. 生成详细的验证报告

验证方法：
- 方法1：流式XML解析统计
- 方法2：正则表达式匹配
- 方法3：缓冲区完整性验证
- 方法4：文件结构分析
- 方法5：抽样验证和推算

作者：AI Assistant
创建时间：2025-07-28
"""

import gzip
import time
import re
import os
import glob
import sys
from datetime import datetime
import hashlib

# 输出文件路径
OUTPUT_FILE = 'xml_validation_report.txt'
DETAILED_FILE = 'xml_detailed_analysis.txt'

# 确保输出文件不存在
for file_path in [OUTPUT_FILE, DETAILED_FILE]:
    if os.path.exists(file_path):
        os.remove(file_path)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('[%Y-%m-%d %H:%M:%S]')
    formatted_message = f"{timestamp} {message}"
    
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def write_detailed(message):
    """将详细信息写入详细分析文件"""
    timestamp = datetime.now().strftime('[%Y-%m-%d %H:%M:%S]')
    formatted_message = f"{timestamp} {message}"
    
    with open(DETAILED_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

def find_xml_file():
    """查找releases XML文件"""
    patterns = [
        'discogs_*_releases.xml.gz',
        '*_releases.xml.gz',
        'discogs_*_releases.xml',
        '*_releases.xml'
    ]
    
    for pattern in patterns:
        found_files = glob.glob(pattern)
        if found_files:
            found_files.sort()
            selected_file = found_files[-1]
            write_output(f"✅ 找到XML文件: {selected_file}")
            return selected_file
    
    write_output("❌ 未找到releases XML文件")
    return None

def get_file_info(file_path):
    """获取文件基本信息"""
    try:
        file_size = os.path.getsize(file_path)
        file_size_gb = file_size / (1024**3)
        
        # 计算文件MD5哈希（前1MB用于快速验证）
        with open(file_path, 'rb') as f:
            first_mb = f.read(1024*1024)
            md5_hash = hashlib.md5(first_mb).hexdigest()
        
        write_output(f"📊 文件大小: {file_size_gb:.2f} GB ({file_size:,} 字节)")
        write_output(f"🔐 文件MD5 (前1MB): {md5_hash}")
        
        return {
            'size': file_size,
            'size_gb': file_size_gb,
            'md5_partial': md5_hash
        }
    except Exception as e:
        write_output(f"❌ 获取文件信息失败: {e}")
        return None

def method1_stream_parsing(xml_file):
    """方法1：流式XML解析统计"""
    write_output("🔍 方法1：流式XML解析统计")
    start_time = time.time()
    
    release_count = 0
    line_count = 0
    release_start_count = 0
    release_end_count = 0
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            
            for line in f:
                line_count += 1
                
                if '<release ' in line:
                    release_start_count += 1
                    in_release = True
                    buffer = line
                elif '</release>' in line and in_release:
                    release_end_count += 1
                    buffer += line
                    in_release = False
                    release_count += 1
                    
                    # 显示进度
                    if release_count % 100000 == 0:
                        elapsed = time.time() - start_time
                        speed = release_count / elapsed if elapsed > 0 else 0
                        write_output(f"   📈 已统计 {release_count:,} 条记录，速度: {speed:.0f} 记录/秒")
                    
                    buffer = ""
                elif in_release:
                    buffer += line
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ 方法1完成 - 记录数: {release_count:,}")
        write_output(f"   📊 总行数: {line_count:,}")
        write_output(f"   📊 <release>标签数: {release_start_count:,}")
        write_output(f"   📊 </release>标签数: {release_end_count:,}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
        
        write_detailed(f"方法1详细结果: release_count={release_count}, line_count={line_count}, start_tags={release_start_count}, end_tags={release_end_count}")
        
        return {
            'method': 'stream_parsing',
            'count': release_count,
            'line_count': line_count,
            'start_tags': release_start_count,
            'end_tags': release_end_count,
            'elapsed_time': elapsed_time
        }
        
    except Exception as e:
        write_output(f"❌ 方法1失败: {e}")
        return None

def method2_regex_matching(xml_file):
    """方法2：正则表达式匹配统计"""
    write_output("🔍 方法2：正则表达式匹配统计")
    start_time = time.time()
    
    release_pattern = re.compile(r'<release\s+id=')
    release_end_pattern = re.compile(r'</release>')
    
    release_starts = 0
    release_ends = 0
    chunk_count = 0
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            while True:
                chunk = f.read(1024*1024)  # 1MB chunks
                if not chunk:
                    break
                
                chunk_count += 1
                release_starts += len(release_pattern.findall(chunk))
                release_ends += len(release_end_pattern.findall(chunk))
                
                if chunk_count % 100 == 0:
                    elapsed = time.time() - start_time
                    write_output(f"   📈 已处理 {chunk_count} 个块，找到 {release_starts:,} 个开始标签")
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ 方法2完成")
        write_output(f"   📊 <release>标签数: {release_starts:,}")
        write_output(f"   📊 </release>标签数: {release_ends:,}")
        write_output(f"   📊 处理块数: {chunk_count:,}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
        
        write_detailed(f"方法2详细结果: release_starts={release_starts}, release_ends={release_ends}, chunks={chunk_count}")
        
        return {
            'method': 'regex_matching',
            'start_tags': release_starts,
            'end_tags': release_ends,
            'chunk_count': chunk_count,
            'elapsed_time': elapsed_time
        }
        
    except Exception as e:
        write_output(f"❌ 方法2失败: {e}")
        return None

def method3_buffer_validation(xml_file):
    """方法3：缓冲区完整性验证"""
    write_output("🔍 方法3：缓冲区完整性验证")
    start_time = time.time()
    
    complete_records = 0
    incomplete_records = 0
    malformed_records = 0
    sample_ids = []
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            
            for line in f:
                if '<release ' in line:
                    if in_release:
                        malformed_records += 1
                        write_detailed(f"发现格式错误：嵌套的release标签")
                    
                    in_release = True
                    buffer = line
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 验证记录完整性
                    if '<release ' in buffer and '</release>' in buffer:
                        complete_records += 1
                        
                        # 提取ID作为样本
                        if len(sample_ids) < 10:
                            id_match = re.search(r'<release\s+id="(\d+)"', buffer)
                            if id_match:
                                sample_ids.append(id_match.group(1))
                    else:
                        incomplete_records += 1
                        write_detailed(f"发现不完整记录，长度: {len(buffer)}")
                    
                    if complete_records % 100000 == 0:
                        elapsed = time.time() - start_time
                        speed = complete_records / elapsed if elapsed > 0 else 0
                        write_output(f"   📈 已验证 {complete_records:,} 条完整记录，速度: {speed:.0f} 记录/秒")
                    
                    buffer = ""
                elif in_release:
                    buffer += line
        
        # 检查是否有未完成的记录
        if in_release:
            incomplete_records += 1
            write_detailed(f"文件末尾发现未完成的记录")
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ 方法3完成")
        write_output(f"   📊 完整记录数: {complete_records:,}")
        write_output(f"   📊 不完整记录数: {incomplete_records:,}")
        write_output(f"   📊 格式错误记录数: {malformed_records:,}")
        write_output(f"   📊 样本ID: {sample_ids[:5]}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
        
        write_detailed(f"方法3详细结果: complete={complete_records}, incomplete={incomplete_records}, malformed={malformed_records}")
        write_detailed(f"所有样本ID: {sample_ids}")
        
        return {
            'method': 'buffer_validation',
            'complete_records': complete_records,
            'incomplete_records': incomplete_records,
            'malformed_records': malformed_records,
            'sample_ids': sample_ids,
            'elapsed_time': elapsed_time
        }
        
    except Exception as e:
        write_output(f"❌ 方法3失败: {e}")
        return None

def method4_structure_analysis(xml_file):
    """方法4：文件结构分析"""
    write_output("🔍 方法4：文件结构分析")
    start_time = time.time()

    try:
        # 检查gzip文件头
        with open(xml_file, 'rb') as f:
            header = f.read(10)
            if header[:2] != b'\x1f\x8b':
                write_output("⚠️ 文件不是标准gzip格式")
            else:
                write_output("✅ gzip文件头验证通过")

        # 检查XML结构
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            # 读取前1000行检查XML头部
            xml_header_found = False
            releases_tag_found = False
            first_release_found = False

            for i, line in enumerate(f):
                if i >= 1000:
                    break

                if '<?xml' in line:
                    xml_header_found = True
                    write_detailed(f"XML头部: {line.strip()}")
                elif '<releases>' in line:
                    releases_tag_found = True
                    write_detailed(f"根标签: {line.strip()}")
                elif '<release ' in line and not first_release_found:
                    first_release_found = True
                    write_detailed(f"第一个release: {line.strip()[:100]}...")
                    break

        # 检查文件尾部
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            # 移动到文件末尾附近
            f.seek(0, 2)  # 移动到文件末尾
            file_size = f.tell()

            # 读取最后几KB
            read_size = min(8192, file_size)
            f.seek(file_size - read_size)
            tail_content = f.read()

            releases_end_found = '</releases>' in tail_content

        elapsed_time = time.time() - start_time
        write_output(f"✅ 方法4完成")
        write_output(f"   📊 XML头部: {'✅' if xml_header_found else '❌'}")
        write_output(f"   📊 根标签<releases>: {'✅' if releases_tag_found else '❌'}")
        write_output(f"   📊 第一个release: {'✅' if first_release_found else '❌'}")
        write_output(f"   📊 结束标签</releases>: {'✅' if releases_end_found else '❌'}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")

        return {
            'method': 'structure_analysis',
            'xml_header': xml_header_found,
            'releases_tag': releases_tag_found,
            'first_release': first_release_found,
            'releases_end': releases_end_found,
            'elapsed_time': elapsed_time
        }

    except Exception as e:
        write_output(f"❌ 方法4失败: {e}")
        return None

def method5_sampling_estimation(xml_file):
    """方法5：抽样验证和推算"""
    write_output("🔍 方法5：抽样验证和推算")
    start_time = time.time()

    sample_size = 1000000  # 抽样1MB
    total_samples = 10     # 总共10个样本

    try:
        file_size = os.path.getsize(xml_file)
        sample_results = []

        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            for sample_num in range(total_samples):
                # 计算抽样位置
                position_ratio = sample_num / total_samples

                # 重置文件指针并跳过一些内容
                f.seek(0)
                skip_bytes = int(file_size * position_ratio * 0.1)  # 粗略估算

                # 读取样本
                sample_content = f.read(sample_size)
                if not sample_content:
                    break

                # 统计样本中的release数量
                release_count = sample_content.count('<release ')
                sample_results.append(release_count)

                write_detailed(f"样本{sample_num+1}: 位置比例{position_ratio:.1f}, release数量: {release_count}")

        # 计算统计信息
        if sample_results:
            avg_per_sample = sum(sample_results) / len(sample_results)
            max_per_sample = max(sample_results)
            min_per_sample = min(sample_results)

            # 粗略估算总数（这只是一个参考）
            estimated_total = avg_per_sample * (file_size / sample_size)
        else:
            avg_per_sample = max_per_sample = min_per_sample = estimated_total = 0

        elapsed_time = time.time() - start_time
        write_output(f"✅ 方法5完成")
        write_output(f"   📊 样本数量: {len(sample_results)}")
        write_output(f"   📊 平均每样本: {avg_per_sample:.1f}")
        write_output(f"   📊 最大每样本: {max_per_sample}")
        write_output(f"   📊 最小每样本: {min_per_sample}")
        write_output(f"   📊 粗略估算总数: {estimated_total:,.0f}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")

        return {
            'method': 'sampling_estimation',
            'sample_count': len(sample_results),
            'avg_per_sample': avg_per_sample,
            'max_per_sample': max_per_sample,
            'min_per_sample': min_per_sample,
            'estimated_total': estimated_total,
            'sample_results': sample_results,
            'elapsed_time': elapsed_time
        }

    except Exception as e:
        write_output(f"❌ 方法5失败: {e}")
        return None

def generate_comprehensive_report(file_info, results):
    """生成综合验证报告"""
    write_output("\n" + "="*60)
    write_output("📋 综合验证报告")
    write_output("="*60)

    # 文件信息
    if file_info:
        write_output(f"📁 文件大小: {file_info['size_gb']:.2f} GB")
        write_output(f"🔐 文件MD5: {file_info['md5_partial']}")

    # 统计各方法的结果
    record_counts = []

    for result in results:
        if result:
            method_name = result['method']
            write_output(f"\n🔍 {method_name}:")

            if method_name == 'stream_parsing':
                count = result['count']
                record_counts.append(count)
                write_output(f"   记录数: {count:,}")
                write_output(f"   标签匹配: {result['start_tags']} 开始, {result['end_tags']} 结束")

            elif method_name == 'regex_matching':
                write_output(f"   开始标签: {result['start_tags']:,}")
                write_output(f"   结束标签: {result['end_tags']:,}")
                if result['start_tags'] == result['end_tags']:
                    record_counts.append(result['start_tags'])

            elif method_name == 'buffer_validation':
                count = result['complete_records']
                record_counts.append(count)
                write_output(f"   完整记录: {count:,}")
                write_output(f"   不完整记录: {result['incomplete_records']:,}")
                write_output(f"   格式错误: {result['malformed_records']:,}")

            elif method_name == 'structure_analysis':
                write_output(f"   XML结构: {'✅ 正常' if all([result['xml_header'], result['releases_tag'], result['releases_end']]) else '⚠️ 异常'}")

            elif method_name == 'sampling_estimation':
                write_output(f"   估算总数: {result['estimated_total']:,.0f}")

    # 一致性分析
    write_output(f"\n📊 一致性分析:")
    if record_counts:
        unique_counts = list(set(record_counts))
        if len(unique_counts) == 1:
            write_output(f"✅ 所有方法结果一致: {unique_counts[0]:,} 条记录")
        else:
            write_output(f"⚠️ 方法结果不一致:")
            for i, count in enumerate(record_counts):
                write_output(f"   方法{i+1}: {count:,}")

            # 计算统计信息
            avg_count = sum(record_counts) / len(record_counts)
            max_count = max(record_counts)
            min_count = min(record_counts)

            write_output(f"   平均值: {avg_count:,.0f}")
            write_output(f"   最大值: {max_count:,}")
            write_output(f"   最小值: {min_count:,}")
            write_output(f"   差异: {max_count - min_count:,}")

    # 最终结论
    write_output(f"\n🎯 最终结论:")
    if record_counts and len(set(record_counts)) == 1:
        final_count = record_counts[0]
        write_output(f"✅ XML文件包含 {final_count:,} 条release记录")
        write_output(f"✅ 数据一致性验证通过")

        # 与之前的数据库统计对比
        db_count = 7935059
        if final_count == db_count:
            write_output(f"✅ 与数据库记录数完全一致 ({db_count:,})")
        else:
            diff = final_count - db_count
            write_output(f"⚠️ 与数据库记录数存在差异: {diff:+,}")
    else:
        write_output(f"⚠️ 验证结果不一致，需要进一步调查")

    write_output("="*60)

def main():
    """主函数"""
    print("🔍 综合XML文件验证工具")
    print("="*60)

    start_time = time.time()

    # 1. 查找XML文件
    xml_file = find_xml_file()
    if not xml_file:
        write_output("❌ 未找到XML文件，程序退出")
        return

    # 2. 获取文件信息
    file_info = get_file_info(xml_file)

    # 3. 执行各种验证方法
    results = []

    # 方法1：流式解析
    result1 = method1_stream_parsing(xml_file)
    results.append(result1)

    # 方法2：正则匹配
    result2 = method2_regex_matching(xml_file)
    results.append(result2)

    # 方法3：缓冲区验证
    result3 = method3_buffer_validation(xml_file)
    results.append(result3)

    # 方法4：结构分析
    result4 = method4_structure_analysis(xml_file)
    results.append(result4)

    # 方法5：抽样估算
    result5 = method5_sampling_estimation(xml_file)
    results.append(result5)

    # 4. 生成综合报告
    generate_comprehensive_report(file_info, results)

    total_time = time.time() - start_time
    write_output(f"\n⏱️ 总验证时间: {total_time:.2f} 秒")
    write_output(f"📄 详细报告已保存到: {OUTPUT_FILE}")
    write_output(f"📄 详细分析已保存到: {DETAILED_FILE}")

if __name__ == "__main__":
    main()
