#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
改进的XML验证器
修复了原始comprehensive_xml_validator.py中的逻辑缺陷

主要修复：
1. 正确处理连续的独立release记录，避免误判为"嵌套"
2. 改进的XML结构验证机制
3. 更精确的错误分类和报告

作者：AI Assistant
创建时间：2025-07-28
"""

import gzip
import time
import re
import os
import hashlib
from datetime import datetime

# 输出文件路径
OUTPUT_FILE = 'improved_validation_output.txt'
DETAILED_FILE = 'improved_validation_detailed.txt'

def write_output(message, to_console=True):
    """写入输出文件并可选择打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"

    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

    if to_console:
        print(formatted_message)

def write_detailed(message):
    """写入详细分析文件"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"
    
    with open(DETAILED_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

def get_file_info(file_path):
    """获取文件基本信息"""
    try:
        file_size = os.path.getsize(file_path)
        file_size_gb = file_size / (1024**3)
        
        # 计算文件MD5哈希（前1MB用于快速验证）
        with open(file_path, 'rb') as f:
            first_mb = f.read(1024*1024)
            md5_hash = hashlib.md5(first_mb).hexdigest()
        
        write_output(f"📊 文件大小: {file_size_gb:.2f} GB ({file_size:,} 字节)")
        write_output(f"🔐 文件MD5 (前1MB): {md5_hash}")
        
        return {
            'size': file_size,
            'size_gb': file_size_gb,
            'md5_partial': md5_hash
        }
    except Exception as e:
        write_output(f"❌ 获取文件信息失败: {e}")
        return None

def improved_buffer_validation(xml_file):
    """改进的缓冲区完整性验证（修复版）"""
    write_output("🔍 改进的缓冲区完整性验证")
    start_time = time.time()
    
    complete_records = 0
    incomplete_records = 0
    true_malformed_records = 0  # 真正的格式错误
    consecutive_records = 0     # 连续的独立记录（之前被误判的）
    sample_ids = []
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            current_release_id = None
            line_number = 0
            
            for line in f:
                line_number += 1
                
                # 检测release记录开始
                if '<release ' in line and 'id=' in line:
                    # 如果前一个记录还没结束，需要仔细分析
                    if in_release:
                        # 检查前一个记录是否实际上已经完整
                        if '</release>' in buffer:
                            # 前一个记录实际上是完整的，这是连续的独立记录
                            consecutive_records += 1
                            write_detailed(f"行 {line_number}: 检测到连续的独立release记录，前一个记录ID: {current_release_id}")
                            
                            # 处理前一个完整记录
                            if '<release ' in buffer and '</release>' in buffer:
                                complete_records += 1
                                if len(sample_ids) < 10:
                                    id_match = re.search(r'<release\s+id="(\d+)"', buffer)
                                    if id_match:
                                        sample_ids.append(id_match.group(1))
                        else:
                            # 真正的嵌套或不完整记录
                            true_malformed_records += 1
                            write_detailed(f"行 {line_number}: 发现真正的格式错误：嵌套或不完整的release标签，当前ID: {current_release_id}")
                    
                    # 开始新的记录
                    in_release = True
                    buffer = line
                    # 提取当前记录的ID
                    id_match = re.search(r'<release\s+id="(\d+)"', line)
                    current_release_id = id_match.group(1) if id_match else "unknown"
                    
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 验证记录完整性
                    if '<release ' in buffer and '</release>' in buffer:
                        complete_records += 1
                        
                        # 提取ID作为样本
                        if len(sample_ids) < 10:
                            id_match = re.search(r'<release\s+id="(\d+)"', buffer)
                            if id_match:
                                sample_ids.append(id_match.group(1))
                    else:
                        incomplete_records += 1
                        write_detailed(f"行 {line_number}: 发现不完整记录，ID: {current_release_id}，长度: {len(buffer)}")
                    
                    if complete_records % 100000 == 0:
                        elapsed = time.time() - start_time
                        speed = complete_records / elapsed if elapsed > 0 else 0
                        write_output(f"   📈 已验证 {complete_records:,} 条完整记录，速度: {speed:.0f} 记录/秒")
                    
                    buffer = ""
                elif in_release:
                    buffer += line
        
        # 检查是否有未完成的记录
        if in_release:
            incomplete_records += 1
            write_detailed(f"文件末尾发现未完成的记录，ID: {current_release_id}")
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ 改进验证完成")
        write_output(f"   📊 完整记录数: {complete_records:,}")
        write_output(f"   📊 不完整记录数: {incomplete_records:,}")
        write_output(f"   📊 真正的格式错误: {true_malformed_records:,}")
        write_output(f"   📊 连续独立记录（之前误判）: {consecutive_records:,}")
        write_output(f"   📊 样本ID: {sample_ids[:5]}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
        
        write_detailed(f"改进验证详细结果: complete={complete_records}, incomplete={incomplete_records}, true_malformed={true_malformed_records}, consecutive={consecutive_records}")
        write_detailed(f"所有样本ID: {sample_ids}")
        
        return {
            'method': 'improved_buffer_validation',
            'complete_records': complete_records,
            'incomplete_records': incomplete_records,
            'true_malformed_records': true_malformed_records,
            'consecutive_records': consecutive_records,
            'sample_ids': sample_ids,
            'elapsed_time': elapsed_time
        }
        
    except Exception as e:
        write_output(f"❌ 改进验证失败: {e}")
        return None

def compare_with_original(xml_file):
    """与原始方法进行对比分析"""
    write_output("\n🔄 开始对比分析...")
    
    # 运行改进的验证
    improved_result = improved_buffer_validation(xml_file)
    
    if improved_result:
        write_output("\n📊 对比分析结果:")
        write_output(f"✅ 完整记录数: {improved_result['complete_records']:,}")
        write_output(f"⚠️ 真正的格式错误: {improved_result['true_malformed_records']:,}")
        write_output(f"🔄 连续独立记录（原误判）: {improved_result['consecutive_records']:,}")
        
        # 计算原始方法的"错误"统计
        original_malformed = improved_result['true_malformed_records'] + improved_result['consecutive_records']
        write_output(f"\n📈 原始方法统计对比:")
        write_output(f"   原始方法报告的'格式错误': {original_malformed:,}")
        write_output(f"   其中真正的错误: {improved_result['true_malformed_records']:,}")
        write_output(f"   其中误判的连续记录: {improved_result['consecutive_records']:,}")
        
        if improved_result['consecutive_records'] > 0:
            error_rate = (improved_result['consecutive_records'] / original_malformed) * 100
            write_output(f"   误判率: {error_rate:.1f}%")
        
        return improved_result
    
    return None

def find_xml_file(module_type):
    """查找XML文件"""
    import glob
    
    # 首先在当前目录查找
    pattern = f'*_{module_type}.xml.gz'
    found_files = glob.glob(pattern)
    
    if found_files:
        if len(found_files) > 1:
            found_files.sort()
            selected_file = found_files[-1]
            write_output(f"🔍 在当前目录找到多个文件，选择最新的: {selected_file}")
        else:
            selected_file = found_files[0]
            write_output(f"✅ 在当前目录检测到文件: {selected_file}")
        return selected_file
    
    # 如果当前目录没找到，搜索 data 目录
    data_dir = 'data'
    pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    found_files = glob.glob(pattern)
    
    if not found_files:
        write_output(f"❌ 未找到 {module_type} 模块的XML文件")
        return None
    
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        write_output(f"🔍 在data目录找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        write_output(f"✅ 在data目录检测到文件: {selected_file}")
    
    return selected_file

def main():
    """主函数"""
    # 清空输出文件
    for file_path in [OUTPUT_FILE, DETAILED_FILE]:
        if os.path.exists(file_path):
            os.remove(file_path)
    
    write_output("🚀 改进的XML验证器启动")
    write_output("=" * 60)
    start_time = time.time()
    
    # 1. 查找XML文件
    xml_file = find_xml_file('releases')
    if not xml_file:
        write_output("❌ 未找到releases XML文件，程序退出")
        return
    
    # 2. 获取文件信息
    file_info = get_file_info(xml_file)
    
    # 3. 执行改进的验证和对比分析
    result = compare_with_original(xml_file)
    
    total_time = time.time() - start_time
    write_output(f"\n⏱️ 总验证时间: {total_time:.2f} 秒")
    write_output(f"📄 详细报告已保存到: {OUTPUT_FILE}")
    write_output(f"📄 详细分析已保存到: {DETAILED_FILE}")

if __name__ == "__main__":
    main()
