#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增量获取器脚本

功能：
1. 测试脚本导入
2. 测试数据库连接
3. 测试API客户端
4. 测试基本功能

作者：AI Assistant
创建时间：2025-07-29
"""

import os
import sys

# 设置测试环境变量
os.environ['MAX_RECORDS'] = '5'  # 限制测试记录数
os.environ['MAX_CONSECUTIVE_404'] = '3'  # 降低404阈值用于测试

def test_imports():
    """测试模块导入"""
    print("🧪 测试1: 模块导入测试")
    print("-" * 40)
    
    try:
        import api_incremental_fetcher
        print("✅ api_incremental_fetcher 导入成功")
        
        # 测试核心组件导入
        from api_incremental_fetcher import (
            connect_to_mongodb,
            get_max_id_from_database,
            IncrementalFetcher,
            validate_environment
        )
        print("✅ 核心组件导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_environment():
    """测试环境验证"""
    print("\n🧪 测试2: 环境验证测试")
    print("-" * 40)
    
    try:
        from api_incremental_fetcher import validate_environment
        result = validate_environment()
        
        if result:
            print("✅ 环境验证通过")
        else:
            print("❌ 环境验证失败")
        
        return result
        
    except Exception as e:
        print(f"❌ 环境验证异常: {e}")
        return False


def test_database_connection():
    """测试数据库连接和最大ID查询"""
    print("\n🧪 测试3: 数据库连接测试")
    print("-" * 40)
    
    try:
        from api_incremental_fetcher import connect_to_mongodb, get_max_id_from_database
        
        # 连接数据库
        client, db = connect_to_mongodb()
        print("✅ 数据库连接成功")
        
        # 测试最大ID查询
        max_id = get_max_id_from_database(db)
        print(f"✅ 最大ID查询成功: {max_id}")
        
        # 关闭连接
        client.close()
        print("✅ 数据库连接已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_fetcher_initialization():
    """测试获取器初始化"""
    print("\n🧪 测试4: 获取器初始化测试")
    print("-" * 40)
    
    try:
        from api_incremental_fetcher import connect_to_mongodb, IncrementalFetcher
        
        # 连接数据库
        client, db = connect_to_mongodb()
        
        # 创建获取器
        fetcher = IncrementalFetcher(db)
        print("✅ 获取器初始化成功")
        print(f"   下一个YRD编号: YRD{fetcher.yid_counter}")
        
        # 测试进度加载
        has_progress = fetcher.load_existing_progress()
        print(f"✅ 进度加载测试: {'有现有进度' if has_progress else '无现有进度'}")
        
        # 测试从最大ID开始
        fetcher.start_from_max_id()
        print(f"✅ 起始ID设置: {fetcher.progress['current_id']}")
        
        # 关闭连接
        client.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 获取器初始化失败: {e}")
        return False


def test_api_client():
    """测试API客户端"""
    print("\n🧪 测试5: API客户端测试")
    print("-" * 40)
    
    try:
        from api_release_补全器 import DiscogsAPIClient
        
        # 创建API客户端
        api_client = DiscogsAPIClient()
        print("✅ API客户端创建成功")
        
        # 测试一个已知存在的ID
        test_id = 49  # 这是一个通常存在的ID
        print(f"🔍 测试API调用 ID: {test_id}")
        
        api_data = api_client.get_release(test_id)
        
        if api_data and api_data is not False:
            print(f"✅ API调用成功: {api_data.get('title', 'N/A')}")
            return True
        elif api_data is None:
            print(f"⚠️ ID {test_id} 不存在 (404)")
            return True  # 404也是正常的响应
        else:
            print(f"❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 增量获取器测试套件")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_environment,
        test_database_connection,
        test_fetcher_initialization,
        test_api_client
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！增量获取器可以正常使用")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
