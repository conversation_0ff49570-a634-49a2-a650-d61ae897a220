#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CSV导入工具运行脚本

提供简单的接口来运行CSV导入工具
"""

import os
import sys
import subprocess

def main():
    """主函数"""
    print("🚀 CSV到MongoDB批量导入工具")
    print("=" * 50)
    
    # 检查CSV文件是否存在
    csv_file = "api_releases_补全_20250729_153950.csv"
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        print("请确保CSV文件在当前目录下")
        return
    
    print(f"📁 找到CSV文件: {csv_file}")
    
    # 询问用户选择
    print("\n请选择运行模式:")
    print("1. 测试模式 (只处理前100行)")
    print("2. 完整导入 (处理所有数据)")
    print("3. 自定义参数")
    print("4. 查看帮助")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        # 测试模式
        cmd = ["python3", "csv_to_mongodb_importer.py", "--test-mode"]
        print("\n🧪 启动测试模式...")
        
    elif choice == "2":
        # 完整导入
        confirm = input("\n⚠️  确认要导入所有数据吗？这可能需要较长时间 (y/N): ").strip().lower()
        if confirm != 'y':
            print("取消导入")
            return
        cmd = ["python3", "csv_to_mongodb_importer.py"]
        print("\n🚀 启动完整导入...")
        
    elif choice == "3":
        # 自定义参数
        print("\n自定义参数设置:")
        batch_size = input("批量大小 (默认100): ").strip() or "100"
        test_mode = input("测试模式 (y/N): ").strip().lower() == 'y'
        allow_duplicates = input("允许重复数据 (y/N): ").strip().lower() == 'y'
        
        cmd = ["python3", "csv_to_mongodb_importer.py", "--batch-size", batch_size]
        if test_mode:
            cmd.append("--test-mode")
        if allow_duplicates:
            cmd.append("--allow-duplicates")
        
        print(f"\n🔧 启动自定义导入: {' '.join(cmd[2:])}")
        
    elif choice == "4":
        # 显示帮助
        cmd = ["python3", "csv_to_mongodb_importer.py", "--help"]
        
    else:
        print("❌ 无效选择")
        return
    
    # 执行命令
    try:
        result = subprocess.run(cmd, check=True)
        if choice != "4":
            print("\n✅ 导入完成！")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 导入失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断导入")

if __name__ == "__main__":
    main()
