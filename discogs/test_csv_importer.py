#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CSV导入工具测试脚本

用于测试csv_to_mongodb_importer.py的基本功能
"""

import os
import sys
import json
import tempfile
import csv
from datetime import datetime, timezone

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from csv_to_mongodb_importer import CSVToMongoImporter

def create_test_csv():
    """创建测试用的CSV文件"""
    test_data = [
        {
            'id': '12345',
            'y_id': 'YRD12345',
            'title': 'Test Album',
            'artists': '[{"artist_id": 123, "name": "Test Artist", "role": "", "anv": ""}]',
            'extra_artists': '[]',
            'labels': '[{"name": "Test Label", "catno": "TL001", "id": "456"}]',
            'companies': '[]',
            'country': 'US',
            'formats': '[{"name": "CD", "qty": "1", "text": "", "descriptions": ["Album"]}]',
            'genres': '["Rock"]',
            'styles': '["Alternative Rock"]',
            'identifiers': '[]',
            'tracklist': '[{"position": "1", "title": "Test Track", "duration": "3:30"}]',
            'master_id': '789',
            'discogs_status': 'Accepted',
            'images': '[]',
            'notes': 'Test notes',
            'year': '2023',
            'images_permissions': '1',
            'permissions': '1',
            'source': '1',
            'created_at': '2025-07-29T08:57:52.109116+00:00',
            'updated_at': '2025-07-29T08:57:52.109127+00:00'
        },
        {
            'id': '12346',
            'y_id': 'YRD12346',
            'title': 'Another Test Album',
            'artists': '[{"artist_id": 124, "name": "Another Artist", "role": "", "anv": ""}]',
            'extra_artists': '[]',
            'labels': '[]',
            'companies': '[]',
            'country': 'UK',
            'formats': '[]',
            'genres': '[]',
            'styles': '[]',
            'identifiers': '[]',
            'tracklist': '[]',
            'master_id': '',
            'discogs_status': '',
            'images': '[]',
            'notes': '',
            'year': '',
            'images_permissions': '1',
            'permissions': '1',
            'source': '1',
            'created_at': '2025-07-29T08:58:00.335184+00:00',
            'updated_at': '2025-07-29T08:58:00.335200+00:00'
        }
    ]
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    
    fieldnames = list(test_data[0].keys())
    writer = csv.DictWriter(temp_file, fieldnames=fieldnames)
    
    writer.writeheader()
    for row in test_data:
        writer.writerow(row)
    
    temp_file.close()
    return temp_file.name

def test_json_parsing():
    """测试JSON解析功能"""
    print("🧪 测试JSON解析功能...")
    
    importer = CSVToMongoImporter("dummy.csv", test_mode=True)
    
    # 测试正常JSON
    test_json = '[{"name": "Test", "id": 123}]'
    result = importer.parse_json_field(test_json)
    assert result == [{"name": "Test", "id": 123}], f"JSON解析失败: {result}"
    
    # 测试CSV转义的JSON
    test_json_escaped = '"[{""name"": ""Test"", ""id"": 123}]"'
    result = importer.parse_json_field(test_json_escaped)
    assert result == [{"name": "Test", "id": 123}], f"转义JSON解析失败: {result}"
    
    # 测试空字符串
    result = importer.parse_json_field("")
    assert result == [], f"空字符串解析失败: {result}"
    
    # 测试无效JSON
    result = importer.parse_json_field("invalid json")
    assert result == [], f"无效JSON解析失败: {result}"
    
    print("✅ JSON解析测试通过")

def test_csv_row_conversion():
    """测试CSV行转换功能"""
    print("🧪 测试CSV行转换功能...")
    
    importer = CSVToMongoImporter("dummy.csv", test_mode=True)
    
    test_row = {
        'id': '12345',
        'y_id': 'YRD12345',
        'title': 'Test Album',
        'artists': '[{"artist_id": 123, "name": "Test Artist"}]',
        'year': '2023',
        'master_id': '789',
        'images_permissions': '1',
        'permissions': '1',
        'source': '1',
        'created_at': '2025-07-29T08:57:52.109116+00:00'
    }
    
    doc = importer.convert_csv_row_to_document(test_row)
    
    assert doc is not None, "文档转换失败"
    assert doc['id'] == 12345, f"ID转换失败: {doc['id']}"
    assert doc['y_id'] == 'YRD12345', f"Y_ID转换失败: {doc['y_id']}"
    assert doc['year'] == 2023, f"年份转换失败: {doc['year']}"
    assert doc['master_id'] == 789, f"Master ID转换失败: {doc['master_id']}"
    assert isinstance(doc['artists'], list), f"Artists字段类型错误: {type(doc['artists'])}"
    assert isinstance(doc['created_at'], datetime), f"创建时间类型错误: {type(doc['created_at'])}"
    
    print("✅ CSV行转换测试通过")

def test_document_validation():
    """测试文档验证功能"""
    print("🧪 测试文档验证功能...")
    
    importer = CSVToMongoImporter("dummy.csv", test_mode=True)
    
    # 测试有效文档
    valid_doc = {'id': 12345, 'y_id': 'YRD12345', 'title': 'Test'}
    assert importer.validate_document(valid_doc), "有效文档验证失败"
    
    # 测试缺少ID的文档
    invalid_doc1 = {'y_id': 'YRD12345', 'title': 'Test'}
    assert not importer.validate_document(invalid_doc1), "缺少ID的文档应该验证失败"
    
    # 测试缺少Y_ID的文档
    invalid_doc2 = {'id': 12345, 'title': 'Test'}
    assert not importer.validate_document(invalid_doc2), "缺少Y_ID的文档应该验证失败"
    
    print("✅ 文档验证测试通过")

def test_csv_file_processing():
    """测试CSV文件处理功能（不连接数据库）"""
    print("🧪 测试CSV文件处理功能...")
    
    # 创建测试CSV文件
    test_csv_file = create_test_csv()
    
    try:
        importer = CSVToMongoImporter(test_csv_file, test_mode=True)
        
        # 测试获取行数
        row_count = importer.get_csv_row_count()
        assert row_count == 2, f"行数统计错误: {row_count}"
        
        print("✅ CSV文件处理测试通过")
        
    finally:
        # 清理临时文件
        if os.path.exists(test_csv_file):
            os.unlink(test_csv_file)

def main():
    """运行所有测试"""
    print("🚀 开始CSV导入工具测试")
    print("=" * 50)
    
    try:
        test_json_parsing()
        test_csv_row_conversion()
        test_document_validation()
        test_csv_file_processing()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！")
        print("✅ CSV导入工具功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
