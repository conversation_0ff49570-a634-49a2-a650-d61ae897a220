#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Release_new 重复数据检测和处理脚本

功能：
1. 识别release_new表中的重复记录（基于id字段）
2. 按created_at时间排序，保留最早的记录，标识需要删除的记录
3. 导出重复记录详情到CSV文件
4. 生成详细的统计报告和处理日志
5. 只进行查询和导出，不执行实际删除操作

重复判断标准：
- 基于 id 字段（int类型）进行重复识别
- 相同 id 的记录被认为是重复记录

保留策略：
- 保留 created_at 最早的记录
- 如果 created_at 相同，则保留 _id 最小的记录

安全策略：
- 只进行查询操作，不执行删除
- 生成详细的删除计划供人工审核
- 完整的日志记录和统计信息

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import sys
import csv
import time
from collections import defaultdict
from datetime import datetime, timezone
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '10000'))  # 批处理大小

# 输出目录和文件
RESULTS_DIR = 'release_new_duplicate_results'
LOG_FILE = os.path.join(RESULTS_DIR, 'release_new_duplicate_detection.log')
DUPLICATES_CSV = os.path.join(RESULTS_DIR, 'release_new_duplicates_to_delete.csv')
SUMMARY_CSV = os.path.join(RESULTS_DIR, 'release_new_duplicate_summary.csv')

# 确保输出目录存在
os.makedirs(RESULTS_DIR, exist_ok=True)

# 清理旧的日志文件
if os.path.exists(LOG_FILE):
    os.remove(LOG_FILE)

def write_log(message, print_to_console=True, level="INFO"):
    """写入日志文件并可选择打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] [{level}] {message}"
    
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_log(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_log(f"❌ MongoDB连接失败: {e}", level="ERROR")
        raise

def get_collection_stats(db):
    """获取集合基本统计信息"""
    try:
        collection = db[COLLECTION_NAME]
        
        # 检查集合是否存在
        if COLLECTION_NAME not in db.list_collection_names():
            write_log(f"❌ 集合 {COLLECTION_NAME} 不存在", level="ERROR")
            return None
        
        # 获取总记录数
        total_count = collection.count_documents({})
        write_log(f"📊 {COLLECTION_NAME} 集合总记录数: {total_count:,}")
        
        # 获取id字段的基本统计信息（避免内存溢出）
        try:
            # 获取ID范围
            min_id_doc = list(collection.find({}, {'id': 1}).sort('id', 1).limit(1))
            max_id_doc = list(collection.find({}, {'id': 1}).sort('id', -1).limit(1))

            min_id = min_id_doc[0]['id'] if min_id_doc else None
            max_id = max_id_doc[0]['id'] if max_id_doc else None

            if min_id is not None and max_id is not None:
                write_log(f"📊 ID范围: {min_id} - {max_id}")

            # 使用更高效的方式估算唯一ID数量
            unique_count_pipeline = [
                {"$group": {"_id": "$id"}},
                {"$count": "unique_count"}
            ]

            unique_result = list(collection.aggregate(unique_count_pipeline, allowDiskUse=True))
            if unique_result:
                unique_count = unique_result[0]['unique_count']
                write_log(f"📊 唯一ID数量: {unique_count:,}")

                # 计算潜在重复数量
                potential_duplicates = total_count - unique_count
                if potential_duplicates > 0:
                    write_log(f"⚠️  潜在重复记录数: {potential_duplicates:,}")
                else:
                    write_log("✅ 未发现潜在重复记录")
            else:
                write_log("📊 无法获取唯一ID统计，将直接进行重复检测")

        except Exception as stats_error:
            write_log(f"⚠️  ID统计获取失败，将直接进行重复检测: {stats_error}", level="WARN")
        
        return {
            'total_count': total_count,
            'collection': collection
        }
        
    except Exception as e:
        write_log(f"❌ 获取集合统计信息失败: {e}", level="ERROR")
        return None

def detect_duplicate_records(collection):
    """检测重复记录"""
    write_log("🔍 开始检测重复记录...")
    
    try:
        # 使用聚合管道查找重复ID
        pipeline = [
            {
                '$group': {
                    '_id': '$id',
                    'count': {'$sum': 1},
                    'records': {'$push': {
                        'mongo_id': '$_id',
                        'y_id': '$y_id',
                        'title': '$title',
                        'created_at': '$created_at',
                        'updated_at': '$updated_at'
                    }}
                }
            },
            {
                '$match': {
                    'count': {'$gt': 1}
                }
            },
            {
                '$sort': {'_id': 1}
            }
        ]
        
        write_log("   使用聚合管道查找重复记录...")
        duplicate_groups = list(collection.aggregate(pipeline, allowDiskUse=True))
        
        write_log(f"📊 发现 {len(duplicate_groups)} 个重复组")
        
        return duplicate_groups
        
    except Exception as e:
        write_log(f"❌ 重复记录检测失败: {e}", level="ERROR")
        return []

def identify_records_to_delete(duplicate_groups):
    """识别需要删除的记录"""
    write_log("🎯 分析重复记录，确定删除策略...")
    
    records_to_delete = []
    total_duplicates = 0
    
    for group in duplicate_groups:
        original_id = group['_id']
        records = group['records']
        count = group['count']
        
        total_duplicates += count
        
        # 按created_at升序排序，如果created_at相同则按mongo_id升序排序
        sorted_records = sorted(records, key=lambda x: (
            x.get('created_at') or datetime.min.replace(tzinfo=timezone.utc),
            x.get('mongo_id')
        ))
        
        # 保留第一个（最早的）记录，删除其余记录
        records_to_keep = sorted_records[0]
        records_to_remove = sorted_records[1:]
        
        write_log(f"   ID {original_id}: 保留1条，删除{len(records_to_remove)}条", 
                 print_to_console=False)
        
        for record in records_to_remove:
            title = record.get('title', '')
            short_title = title[:50] + '...' if len(title) > 50 else title
            
            records_to_delete.append({
                'original_id': original_id,
                'mongo_id': record['mongo_id'],
                'y_id': record.get('y_id', ''),
                'title': short_title,
                'created_at': record.get('created_at', ''),
                'updated_at': record.get('updated_at', ''),
                'keep_record_y_id': records_to_keep.get('y_id', ''),
                'keep_record_created_at': records_to_keep.get('created_at', ''),
                'duplicate_group_size': count
            })
    
    write_log(f"📊 总重复记录数: {total_duplicates}")
    write_log(f"📊 需要删除的记录数: {len(records_to_delete)}")
    write_log(f"📊 将保留的记录数: {total_duplicates - len(records_to_delete)}")
    
    return records_to_delete

def export_to_csv(records_to_delete, stats):
    """导出重复记录到CSV文件"""
    write_log("📄 导出重复记录到CSV文件...")
    
    try:
        # 导出需要删除的记录
        with open(DUPLICATES_CSV, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'original_id', 'mongo_id', 'y_id', 'title', 
                'created_at', 'updated_at', 'keep_record_y_id', 
                'keep_record_created_at', 'duplicate_group_size'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for record in records_to_delete:
                writer.writerow(record)
        
        write_log(f"✅ 重复记录详情已导出到: {DUPLICATES_CSV}")
        
        # 导出统计汇总
        with open(SUMMARY_CSV, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['metric', 'value', 'description']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            writer.writerow({
                'metric': 'total_records',
                'value': stats['total_records'],
                'description': 'release_new表总记录数'
            })
            writer.writerow({
                'metric': 'duplicate_groups',
                'value': stats['duplicate_groups'],
                'description': '重复组数量'
            })
            writer.writerow({
                'metric': 'total_duplicates',
                'value': stats['total_duplicates'],
                'description': '总重复记录数'
            })
            writer.writerow({
                'metric': 'records_to_delete',
                'value': stats['records_to_delete'],
                'description': '需要删除的记录数'
            })
            writer.writerow({
                'metric': 'records_to_keep',
                'value': stats['records_to_keep'],
                'description': '将保留的记录数'
            })
            writer.writerow({
                'metric': 'duplicate_rate',
                'value': f"{stats['duplicate_rate']:.2f}%",
                'description': '重复率'
            })
            writer.writerow({
                'metric': 'processing_time',
                'value': f"{stats['processing_time']:.2f}s",
                'description': '处理耗时'
            })
        
        write_log(f"✅ 统计汇总已导出到: {SUMMARY_CSV}")

    except Exception as e:
        write_log(f"❌ CSV导出失败: {e}", level="ERROR")
        raise

class ReleaseNewDuplicateProcessor:
    """Release_new重复数据处理器"""

    def __init__(self, db):
        self.db = db
        self.collection = db[COLLECTION_NAME]
        self.stats = {
            'total_records': 0,
            'duplicate_groups': 0,
            'total_duplicates': 0,
            'records_to_delete': 0,
            'records_to_keep': 0,
            'duplicate_rate': 0.0,
            'processing_time': 0.0
        }

    def run_duplicate_detection(self):
        """运行完整的重复数据检测流程"""
        start_time = time.time()

        write_log("🚀 开始Release_new重复数据检测...")
        write_log("=" * 60)

        try:
            # 1. 获取集合统计信息
            collection_stats = get_collection_stats(self.db)
            if not collection_stats:
                write_log("❌ 无法获取集合统计信息，退出处理", level="ERROR")
                return False

            self.stats['total_records'] = collection_stats['total_count']

            # 2. 检测重复记录
            duplicate_groups = detect_duplicate_records(self.collection)
            if not duplicate_groups:
                write_log("✅ 未发现重复记录，处理完成")
                self.stats['processing_time'] = time.time() - start_time
                self._generate_final_report()
                return True

            self.stats['duplicate_groups'] = len(duplicate_groups)

            # 3. 识别需要删除的记录
            records_to_delete = identify_records_to_delete(duplicate_groups)

            # 4. 更新统计信息
            self.stats['total_duplicates'] = sum(group['count'] for group in duplicate_groups)
            self.stats['records_to_delete'] = len(records_to_delete)
            self.stats['records_to_keep'] = (self.stats['total_duplicates'] -
                                             self.stats['records_to_delete'])
            if self.stats['total_records'] > 0:
                self.stats['duplicate_rate'] = (self.stats['total_duplicates'] /
                                               self.stats['total_records']) * 100
            else:
                self.stats['duplicate_rate'] = 0
            self.stats['processing_time'] = time.time() - start_time

            # 5. 导出结果到CSV
            export_to_csv(records_to_delete, self.stats)

            # 6. 生成最终报告
            self._generate_final_report()

            write_log("✅ Release_new重复数据检测完成！")
            return True

        except Exception as e:
            write_log(f"❌ 处理过程中发生错误: {e}", level="ERROR")
            self.stats['processing_time'] = time.time() - start_time
            return False

    def _generate_final_report(self):
        """生成最终统计报告"""
        write_log("\n" + "=" * 60)
        write_log("📈 Release_new重复数据检测完成统计")
        write_log("=" * 60)
        write_log(f"📊 总记录数: {self.stats['total_records']:,}")
        write_log(f"🔍 重复组数: {self.stats['duplicate_groups']:,}")
        write_log(f"📊 总重复记录数: {self.stats['total_duplicates']:,}")
        write_log(f"🗑️  需要删除的记录数: {self.stats['records_to_delete']:,}")
        write_log(f"✅ 将保留的记录数: {self.stats['records_to_keep']:,}")
        write_log(f"📈 重复率: {self.stats['duplicate_rate']:.2f}%")
        write_log(f"⏱️  总耗时: {self.stats['processing_time']:.2f} 秒")

        if self.stats['records_to_delete'] > 0:
            write_log(f"📄 重复记录详情: {DUPLICATES_CSV}")
            write_log(f"📊 统计汇总: {SUMMARY_CSV}")

        write_log("=" * 60)

def main():
    """主函数"""
    write_log("🗂️  Release_new重复数据检测工具")
    write_log("=" * 60)
    write_log("功能：检测并分析release_new表中的重复记录")
    write_log("策略：基于id字段识别重复，保留created_at最早的记录")
    write_log("安全：只进行查询和导出，不执行实际删除操作")
    write_log("=" * 60)

    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 创建处理器并运行检测流程
        processor = ReleaseNewDuplicateProcessor(db)
        success = processor.run_duplicate_detection()

        # 关闭数据库连接
        client.close()
        write_log("🔌 数据库连接已关闭")

        if success:
            write_log(f"\n📄 详细日志已保存到: {LOG_FILE}")
            print(f"\n✅ 重复数据检测完成！详细日志请查看: {LOG_FILE}")
            if processor.stats['records_to_delete'] > 0:
                print(f"📄 重复记录详情: {DUPLICATES_CSV}")
                print(f"📊 统计汇总: {SUMMARY_CSV}")
        else:
            print(f"\n❌ 重复数据检测失败！详细日志请查看: {LOG_FILE}")
            sys.exit(1)

    except KeyboardInterrupt:
        write_log("⚠️ 用户中断了程序执行", level="WARN")
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        write_log(f"❌ 程序执行失败: {e}", level="ERROR")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)

def print_usage():
    """打印使用说明"""
    usage_text = """
🗂️  Release_new重复数据检测工具

功能说明：
1. 检测release_new表中的重复记录（基于id字段）
2. 按created_at时间排序，确定保留和删除策略
3. 导出重复记录详情到CSV文件
4. 生成详细的统计报告和处理日志
5. 只进行查询和导出，不执行实际删除操作

使用方法：
    python process_release_new_duplicates.py

重复判断标准：
    - 基于 id 字段（int类型）进行重复识别
    - 相同 id 的记录被认为是重复记录

保留策略：
    - 保留 created_at 最早的记录
    - 如果 created_at 相同，则保留 _id 最小的记录

输出文件：
    - release_new_duplicate_results/release_new_duplicate_detection.log    # 详细日志
    - release_new_duplicate_results/release_new_duplicates_to_delete.csv   # 需要删除的记录
    - release_new_duplicate_results/release_new_duplicate_summary.csv      # 统计汇总

安全说明：
    - 本工具只进行查询和分析，不会修改数据库
    - 生成的CSV文件可用于人工审核和后续处理
    - 建议在执行任何删除操作前仔细审核输出结果
    """
    print(usage_text)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        print_usage()
    else:
        main()
