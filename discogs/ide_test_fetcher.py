#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
IDE测试版本 - Discogs API 增量获取器

这个版本专门为在IDE中直接运行而设计：
1. 内置所有依赖，无需外部模块
2. 预设测试参数
3. 详细的运行日志
4. 智能的429错误处理

直接在IDE中按F5运行即可！
"""

import os
import sys
import time
import json
import csv
import logging
from datetime import datetime
from pymongo import MongoClient

# 尝试导入requests
try:
    import requests
    print("✅ requests 模块可用")
except ImportError:
    print("❌ 缺少 requests 模块")
    print("请在终端运行: pip install requests")
    sys.exit(1)

# 配置
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'
API_BASE_URL = 'https://api.discogs.com/releases'
API_RATE_LIMIT = 1.0  # 1秒1次请求
MAX_CONSECUTIVE_404 = 20
MAX_RETRIES = 3

# 测试参数（可以修改这些值）
TEST_MAX_RECORDS = 5  # 测试时只获取5条记录
TEST_START_ID = 0     # 0表示从数据库最大ID+1开始

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ide_test_fetcher.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SimpleAPIClient:
    """简化的API客户端，内置智能429处理"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DiscogsIDETest/1.0 +http://example.com'
        })
        self.last_request_time = 0
        self.request_count = 0
    
    def get_release(self, release_id):
        """获取单个release数据，包含智能429处理"""
        # 频率控制
        elapsed = time.time() - self.last_request_time
        if elapsed < API_RATE_LIMIT:
            sleep_time = API_RATE_LIMIT - elapsed
            logger.info(f"⏱️ 频率控制：等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)
        
        url = f"{API_BASE_URL}/{release_id}"
        self.request_count += 1
        
        for attempt in range(MAX_RETRIES):
            try:
                self.last_request_time = time.time()
                logger.info(f"🌐 API请求 #{self.request_count}: ID {release_id} (尝试 {attempt + 1}/{MAX_RETRIES})")
                
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    logger.info(f"✅ 成功获取 ID {release_id}")
                    return response.json()
                elif response.status_code == 404:
                    logger.info(f"⏭️ ID {release_id} 不存在 (404)")
                    return None
                elif response.status_code == 429:
                    # 智能等待时间：5秒 → 15秒 → 60秒
                    wait_times = [5, 15, 60]
                    wait_time = wait_times[min(attempt, len(wait_times) - 1)]
                    logger.warning(f"⏳ API频率限制 (429)，智能等待 {wait_time} 秒... (尝试 {attempt + 1}/{MAX_RETRIES})")
                    logger.warning(f"🔍 当前请求间隔: {elapsed:.2f}秒")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.warning(f"⚠️ API返回状态码 {response.status_code} for ID {release_id}")
                    return None
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ 请求超时 for ID {release_id} (尝试 {attempt + 1}/{MAX_RETRIES})")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                logger.error(f"❌ 请求异常 for ID {release_id}: {e}")
                return None
        
        logger.error(f"❌ 达到最大重试次数 for ID {release_id}")
        return None

class IDETestFetcher:
    """IDE测试版增量获取器"""
    
    def __init__(self, db):
        self.db = db
        self.collection = db[COLLECTION_NAME]
        self.api_client = SimpleAPIClient()
        self.stats = {
            'processed': 0,
            'successful': 0,
            'not_found': 0,
            'errors': 0,
            'consecutive_404': 0,
            'start_time': time.time()
        }
        self.csv_data = []
    
    def get_max_id(self):
        """获取数据库中最大的ID"""
        try:
            result = self.collection.find().sort("id", -1).limit(1)
            max_doc = list(result)
            if max_doc:
                max_id = max_doc[0]['id']
                logger.info(f"📊 数据库最大ID: {max_id:,}")
                return max_id
            else:
                logger.info("📊 数据库为空，从ID 1开始")
                return 0
        except Exception as e:
            logger.error(f"❌ 获取最大ID失败: {e}")
            return 0
    
    def process_api_response(self, data):
        """处理API响应数据"""
        try:
            processed = {
                'id': data.get('id'),
                'title': data.get('title', ''),
                'year': data.get('year', ''),
                'country': data.get('country', ''),
                'released': data.get('released', ''),
                'notes': data.get('notes', ''),
                'genres': '|'.join(data.get('genres', [])),
                'styles': '|'.join(data.get('styles', [])),
                'artists': '|'.join([artist.get('name', '') for artist in data.get('artists', [])]),
                'labels': '|'.join([label.get('name', '') for label in data.get('labels', [])]),
                'formats': '|'.join([fmt.get('name', '') for fmt in data.get('formats', [])]),
                'created_at': datetime.now().isoformat()
            }
            return processed
        except Exception as e:
            logger.error(f"❌ 数据处理失败: {e}")
            return None
    
    def save_to_csv(self, filename):
        """保存数据到CSV"""
        if not self.csv_data:
            logger.info("📝 没有数据需要保存")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = self.csv_data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.csv_data)
            
            logger.info(f"💾 已保存 {len(self.csv_data)} 条记录到 {filename}")
        except Exception as e:
            logger.error(f"❌ 保存CSV失败: {e}")
    
    def show_progress(self):
        """显示进度信息"""
        elapsed = time.time() - self.stats['start_time']
        rate = self.stats['processed'] / elapsed if elapsed > 0 else 0
        
        logger.info("📊 当前进度:")
        logger.info(f"   已处理: {self.stats['processed']}")
        logger.info(f"   成功获取: {self.stats['successful']}")
        logger.info(f"   404跳过: {self.stats['not_found']}")
        logger.info(f"   错误: {self.stats['errors']}")
        logger.info(f"   连续404: {self.stats['consecutive_404']}")
        logger.info(f"   处理速度: {rate:.2f} ID/秒")
        logger.info(f"   运行时间: {elapsed:.1f} 秒")
    
    def run(self, start_id, max_records):
        """运行增量获取"""
        logger.info(f"🚀 开始从ID {start_id} 获取数据")
        logger.info(f"📊 最大记录数限制: {max_records}")
        logger.info(f"📊 期望API频率: {API_RATE_LIMIT} 请求/秒")
        logger.info(f"🔧 429错误处理: 5秒→15秒→60秒渐进等待")
        logger.info("=" * 50)
        
        current_id = start_id
        
        try:
            while True:
                # 检查记录数限制
                if max_records > 0 and self.stats['processed'] >= max_records:
                    logger.info(f"✅ 已达到最大记录数限制: {max_records}")
                    break
                
                # 检查连续404限制
                if self.stats['consecutive_404'] >= MAX_CONSECUTIVE_404:
                    logger.info(f"🛑 连续404达到限制 ({MAX_CONSECUTIVE_404})，停止处理")
                    break
                
                # 获取数据
                logger.info(f"🔍 正在获取ID: {current_id}")
                data = self.api_client.get_release(current_id)
                
                if data:
                    # 成功获取
                    processed = self.process_api_response(data)
                    if processed:
                        self.csv_data.append(processed)
                        self.stats['successful'] += 1
                        self.stats['consecutive_404'] = 0  # 重置连续404计数
                        logger.info(f"✅ 成功获取ID {current_id}: {processed['title'][:50]}...")
                    else:
                        self.stats['errors'] += 1
                elif data is None:
                    # 404错误
                    self.stats['not_found'] += 1
                    self.stats['consecutive_404'] += 1
                else:
                    # 其他错误
                    self.stats['errors'] += 1
                
                self.stats['processed'] += 1
                current_id += 1
                
                # 每处理3条记录显示一次进度
                if self.stats['processed'] % 3 == 0:
                    self.show_progress()
        
        except KeyboardInterrupt:
            logger.info("🛑 用户中断")
        except Exception as e:
            logger.error(f"❌ 运行错误: {e}")
        
        # 保存结果
        if self.csv_data:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"ide_test_fetcher_{timestamp}.csv"
            self.save_to_csv(filename)
        
        # 显示最终统计
        self.show_progress()
        logger.info("🎉 测试完成！")

def connect_to_mongodb():
    """连接MongoDB"""
    try:
        logger.info("🔍 正在连接MongoDB...")
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        # 测试连接
        db.command('ping')
        logger.info(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        logger.error("请检查数据库连接配置")
        sys.exit(1)

def main():
    """主函数 - IDE测试版本"""
    print("🎯 IDE测试版 - Discogs API 增量获取器")
    print("=" * 50)
    print(f"📊 测试参数:")
    print(f"   最大记录数: {TEST_MAX_RECORDS}")
    print(f"   起始ID: {TEST_START_ID} (0=从数据库最大ID+1开始)")
    print(f"   API频率: {API_RATE_LIMIT} 请求/秒")
    print(f"   429处理: 智能等待 (5秒→15秒→60秒)")
    print("=" * 50)
    
    # 连接数据库
    client, db = connect_to_mongodb()
    
    try:
        # 创建获取器
        fetcher = IDETestFetcher(db)
        
        # 确定起始ID
        if TEST_START_ID > 0:
            start_id = TEST_START_ID
            logger.info(f"🎯 使用指定起始ID: {start_id}")
        else:
            start_id = fetcher.get_max_id() + 1
            logger.info(f"🎯 从数据库最大ID+1开始: {start_id}")
        
        # 运行获取
        fetcher.run(start_id, TEST_MAX_RECORDS)
        
    finally:
        client.close()
        logger.info("📊 数据库连接已关闭")
    
    print("🎉 IDE测试完成！")
    print("💡 如需修改测试参数，请编辑脚本顶部的 TEST_MAX_RECORDS 和 TEST_START_ID 变量")

if __name__ == "__main__":
    main()
