#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json

def test_regex():
    """测试正则表达式"""
    
    # 测试字符串
    test_str = '[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]'
    print('原始:', test_str)
    
    # 测试正则表达式
    pattern = r'""(\d+)\\"""""'
    replacement = r'"\1\\""'

    # 也测试更简单的模式
    simple_pattern = r'""12\\"""""'
    simple_replacement = r'"12\\""'
    
    print(f'模式: {pattern}')
    print(f'替换: {replacement}')
    
    # 查找匹配
    matches = re.findall(pattern, test_str)
    print(f'匹配: {matches}')

    # 测试简单模式
    simple_matches = re.findall(simple_pattern, test_str)
    print(f'简单模式匹配: {simple_matches}')

    # 执行替换
    result = re.sub(pattern, replacement, test_str)
    if not matches:
        # 尝试简单替换
        result = test_str.replace(simple_pattern, simple_replacement)
    print(f'替换后: {result}')
    
    # 继续处理
    result = result.replace('""', '"')
    print(f'处理双引号后: {result}')
    
    # 测试JSON
    try:
        parsed = json.loads(result)
        print('✅ JSON解析成功:', parsed[0]['descriptions'])
    except Exception as e:
        print('❌ JSON解析失败:', e)

def test_manual_fix():
    """手动修复测试"""
    print('\n--- 手动修复测试 ---')
    
    # 原始字符串
    original = '[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]'
    print('原始:', original)
    
    # 手动构建正确的JSON
    # ""12\"" 应该变成 "12\""
    fixed = original.replace('""12\""""', '"12\\""')
    print('手动修复:', fixed)
    
    # 处理其他双引号
    fixed = fixed.replace('""', '"')
    print('处理双引号:', fixed)
    
    try:
        parsed = json.loads(fixed)
        print('✅ 手动修复成功:', parsed[0]['descriptions'])
    except Exception as e:
        print('❌ 手动修复失败:', e)

if __name__ == '__main__':
    test_regex()
    test_manual_fix()
