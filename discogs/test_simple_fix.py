#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def simple_fix_test():
    """简单的修复测试"""
    
    # 原始CSV字段
    csv_field = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    print(f'原始: {csv_field}')
    
    # 去掉外层引号
    field_value = csv_field[1:-1]
    print(f'去掉外层引号: {field_value}')
    
    # 直接构建正确的JSON
    # 我们知道 ""12\"" 应该变成 "12\""
    field_value = field_value.replace('""12\""""', '"12\\""')
    print(f'修复12英寸: {field_value}')
    
    # 处理其他双引号
    field_value = field_value.replace('""', '"')
    print(f'处理其他双引号: {field_value}')
    
    try:
        parsed = json.loads(field_value)
        print('✅ 成功:', parsed[0]['descriptions'])
        return True
    except Exception as e:
        print('❌ 失败:', e)
        return False

def test_with_importer():
    """使用导入器测试"""
    print('\n--- 使用导入器测试 ---')
    
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from csv_to_mongodb_importer import CSVToMongoImporter
    
    importer = CSVToMongoImporter('dummy.csv', test_mode=True)
    
    # 测试问题JSON
    test_json = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    
    result = importer.parse_json_field(test_json)
    
    if isinstance(result, list) and len(result) > 0:
        print('✅ 导入器解析成功:', result[0].get('descriptions', []))
        return True
    else:
        print('❌ 导入器解析失败:', result)
        return False

if __name__ == '__main__':
    print('🧪 简单修复测试')
    print('=' * 40)
    
    success1 = simple_fix_test()
    success2 = test_with_importer()
    
    if success1 or success2:
        print('\n✅ 至少一种方法成功')
    else:
        print('\n❌ 所有方法都失败')
