#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的增量获取器测试

功能：
1. 测试从数据库最大ID开始的增量获取
2. 只处理5个ID，观察API调用频率
3. 检查是否遇到429错误

作者：AI Assistant
创建时间：2025-07-29
"""

import os
import time
import logging

# 设置测试环境
os.environ['MAX_RECORDS'] = '5'
os.environ['MAX_CONSECUTIVE_404'] = '3'

# 设置日志级别为DEBUG以获取更多信息
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_incremental_fetcher():
    """测试增量获取器"""
    print("🧪 简化增量获取器测试")
    print("=" * 50)
    
    try:
        from api_incremental_fetcher import IncrementalFetcher, connect_to_mongodb
        
        # 连接数据库
        client, db = connect_to_mongodb()
        
        # 创建获取器
        fetcher = IncrementalFetcher(db)
        
        # 设置起始ID（从数据库最大ID开始）
        fetcher.start_from_max_id()
        
        print(f"🎯 起始ID: {fetcher.progress['current_id']}")
        print(f"📊 下一个YRD编号: YRD{fetcher.yid_counter}")
        
        # 手动处理几个ID来观察行为
        test_count = 5
        start_time = time.time()
        
        for i in range(test_count):
            current_id = fetcher.progress['current_id'] + i
            print(f"\n🔍 处理ID {i+1}/{test_count}: {current_id}")
            
            # 记录单次处理开始时间
            single_start = time.time()
            
            # 处理单个ID
            should_continue = fetcher.process_single_id(current_id)
            
            # 记录单次处理结束时间
            single_end = time.time()
            single_duration = single_end - single_start
            
            print(f"   处理耗时: {single_duration:.2f}秒")
            
            if not should_continue:
                print("   停止条件触发，结束测试")
                break
            
            # 显示当前统计
            print(f"   成功: {fetcher.progress['successful_count']}")
            print(f"   404: {fetcher.progress['skipped_404_count']}")
            print(f"   错误: {fetcher.progress['error_count']}")
            print(f"   连续404: {fetcher.progress['consecutive_404_count']}")
        
        # 计算总体统计
        total_time = time.time() - start_time
        processed_count = fetcher.progress['processed_count']
        
        print("\n" + "=" * 50)
        print("📊 测试结果")
        print("=" * 50)
        print(f"总处理数: {processed_count}")
        print(f"总耗时: {total_time:.2f}秒")
        if processed_count > 0:
            avg_time_per_request = total_time / processed_count
            print(f"平均每请求: {avg_time_per_request:.2f}秒")
            print(f"实际频率: {1/avg_time_per_request:.2f} 请求/秒")
        
        print(f"成功获取: {fetcher.progress['successful_count']}")
        print(f"404跳过: {fetcher.progress['skipped_404_count']}")
        print(f"错误数量: {fetcher.progress['error_count']}")
        
        # 关闭数据库连接
        client.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_incremental_fetcher()
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
