#!/usr/bin/env python3
"""
损坏CSV文件修复工具
修复由于换行符导致的CSV结构问题
"""

import csv
import json
import os
import sys
import re
from typing import List, Dict, Any

def analyze_csv_structure(csv_filename: str):
    """分析CSV文件结构，识别问题"""
    print(f"🔍 分析CSV文件结构: {csv_filename}")
    
    if not os.path.exists(csv_filename):
        print(f"❌ 文件不存在: {csv_filename}")
        return None
    
    with open(csv_filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 文件总行数: {len(lines)}")
    
    # 分析头部
    if lines:
        header = lines[0].strip()
        expected_columns = len(header.split(','))
        print(f"📋 期望列数: {expected_columns}")
        
        # 检查每行的列数
        problematic_lines = []
        for i, line in enumerate(lines[1:], 2):  # 从第2行开始
            if line.strip():  # 跳过空行
                # 简单的列数检查（不完全准确，但可以发现明显问题）
                comma_count = line.count(',')
                if comma_count != expected_columns - 1:
                    problematic_lines.append(i)
        
        if problematic_lines:
            print(f"⚠️ 发现 {len(problematic_lines)} 行可能有问题")
            print(f"问题行号: {problematic_lines[:10]}...")  # 只显示前10个
        else:
            print("✅ 基本结构检查通过")
    
    return {
        'total_lines': len(lines),
        'expected_columns': expected_columns if lines else 0,
        'problematic_lines': problematic_lines if lines else []
    }

def fix_csv_structure(input_filename: str, output_filename: str):
    """修复CSV文件结构"""
    print(f"🔧 开始修复CSV文件: {input_filename} -> {output_filename}")
    
    if not os.path.exists(input_filename):
        print(f"❌ 输入文件不存在: {input_filename}")
        return False
    
    try:
        # 读取原始文件内容
        with open(input_filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📖 读取文件内容，大小: {len(content)} 字符")
        
        # 尝试修复常见问题
        fixed_content = fix_common_csv_issues(content)
        
        # 写入修复后的文件
        with open(output_filename, 'w', encoding='utf-8', newline='') as f:
            f.write(fixed_content)
        
        print(f"✅ 修复完成，输出文件: {output_filename}")
        
        # 验证修复效果
        return verify_fixed_csv(output_filename)
        
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        return False

def fix_common_csv_issues(content: str) -> str:
    """修复常见的CSV问题"""
    print("🔧 修复常见CSV问题...")
    
    # 1. 修复JSON字段中的换行符问题
    # 这是一个复杂的问题，需要识别JSON字段并移除其中的换行符
    
    lines = content.split('\n')
    if not lines:
        return content
    
    header = lines[0]
    data_lines = lines[1:]
    
    print(f"📋 头部: {header}")
    print(f"📊 数据行数: {len(data_lines)}")
    
    # 重新组装CSV，合并被错误分割的行
    fixed_lines = [header]
    current_record = ""
    quote_count = 0
    
    for line in data_lines:
        if not line.strip():  # 跳过空行
            continue
            
        current_record += line
        
        # 计算引号数量来判断是否在引用字段内
        quote_count += line.count('"')
        
        # 如果引号数量是偶数，说明当前记录完整
        if quote_count % 2 == 0:
            # 清理记录中的换行符和其他控制字符
            cleaned_record = clean_csv_record(current_record)
            if cleaned_record.strip():
                fixed_lines.append(cleaned_record)
            current_record = ""
            quote_count = 0
    
    # 处理最后一个未完成的记录
    if current_record.strip():
        cleaned_record = clean_csv_record(current_record)
        if cleaned_record.strip():
            fixed_lines.append(cleaned_record)
    
    print(f"✅ 修复后行数: {len(fixed_lines)}")
    return '\n'.join(fixed_lines)

def clean_csv_record(record: str) -> str:
    """清理CSV记录中的问题字符"""
    # 移除记录中的换行符（但保留字段分隔符）
    # 这需要小心处理，因为换行符可能在引用字段内
    
    # 简单的方法：移除所有控制字符，但保留逗号和引号
    cleaned = re.sub(r'[\r\n\t]', ' ', record)
    
    # 清理多余的空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    
    return cleaned.strip()

def verify_fixed_csv(csv_filename: str) -> bool:
    """验证修复后的CSV文件"""
    print(f"✅ 验证修复后的CSV文件: {csv_filename}")
    
    try:
        import pandas as pd
        
        # 使用pandas读取
        df = pd.read_csv(csv_filename)
        print(f"📊 pandas读取成功: {len(df)} 行, {len(df.columns)} 列")
        
        # 验证JSON字段
        json_fields = ['artists', 'extra_artists', 'labels', 'companies', 
                      'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']
        
        total_json_tests = 0
        successful_json_tests = 0
        
        for index, row in df.iterrows():
            for field in json_fields:
                if field in row and pd.notna(row[field]) and str(row[field]).strip():
                    total_json_tests += 1
                    try:
                        json.loads(str(row[field]))
                        successful_json_tests += 1
                    except json.JSONDecodeError:
                        pass
        
        if total_json_tests > 0:
            success_rate = (successful_json_tests / total_json_tests) * 100
            print(f"📊 JSON解析成功率: {successful_json_tests}/{total_json_tests} ({success_rate:.2f}%)")
            
            if success_rate >= 95:
                print("✅ 修复效果良好")
                return True
            else:
                print("⚠️ 修复效果有限，可能需要进一步处理")
                return False
        else:
            print("⚠️ 没有JSON字段进行验证")
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 fix_damaged_csv.py <input_csv_file> [output_csv_file]")
        print("示例: python3 fix_damaged_csv.py batch_3_releases.csv batch_3_releases_fixed.csv")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else f"{input_file.rsplit('.', 1)[0]}_fixed.csv"
    
    print("🚀 CSV文件修复工具")
    print("=" * 60)
    
    # 分析原始文件
    analysis = analyze_csv_structure(input_file)
    if not analysis:
        return
    
    # 修复文件
    success = fix_csv_structure(input_file, output_file)
    
    if success:
        print("\n🎉 修复完成！")
        print(f"✅ 修复后的文件: {output_file}")
        print("建议使用csv_to_mongodb_importer.py测试导入效果")
    else:
        print("\n❌ 修复失败")
        print("建议检查原始文件格式或手动处理")

if __name__ == "__main__":
    main()
