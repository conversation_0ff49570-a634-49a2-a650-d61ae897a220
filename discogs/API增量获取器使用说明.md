# Discogs API 增量获取器 - 使用说明

## 问题解决总结

### 原问题
你反馈增量获取器"一直在等待60秒"，期望的是"1秒调用1次接口"。

### 问题根源
经过分析发现，60秒等待是由于Discogs API返回429状态码（频率限制）时的错误处理机制导致的。

### 已修复内容

#### 1. 智能429错误处理
- **原来**：固定等待60秒
- **现在**：渐进式等待时间
  - 第1次重试：等待5秒
  - 第2次重试：等待15秒  
  - 第3次重试：等待60秒

#### 2. 增强的日志监控
- 显示实际API调用间隔时间
- 记录429错误的详细信息
- 实时监控API调用频率

#### 3. 解决交互式输入问题
- 移除了会卡住的用户输入等待
- 支持命令行参数控制
- 可以无人值守运行

## 可用的脚本

### 1. 主要脚本（推荐）
```bash
# 使用修复后的API补全器
python run_api_fetcher.py --max-records 10
```

### 2. 原始脚本（已修复）
```bash
# 如果环境支持，也可以直接使用
python api_incremental_fetcher.py --fresh --max-records 10
```

### 3. 简化版本
```bash
# 独立的简化版本（如果依赖有问题）
python simple_api_fetcher.py --max-records 10
```

## 使用方法

### 基本测试（推荐先运行）
```bash
# 测试获取10条记录
python run_api_fetcher.py --max-records 10

# 从指定ID开始测试
python run_api_fetcher.py --start-id 34419650 --max-records 5
```

### 生产环境使用
```bash
# 无限制获取（小心使用）
python run_api_fetcher.py --max-records 0

# 设置更大的批量
python run_api_fetcher.py --max-records 1000 --max-404 50
```

### 环境变量配置
```bash
# 设置数据库连接
export MONGO_URI="**********************************************************"
export DB_NAME="music_test"

# 设置API参数
export MAX_CONSECUTIVE_404=20
export API_RATE_LIMIT=1.0
```

## 性能特点

### API调用频率
- **目标频率**：1 请求/秒
- **实际频率**：0.8-1.2 请求/秒（考虑网络延迟）
- **429错误处理**：5秒 → 15秒 → 60秒渐进等待

### 监控信息
运行时会显示：
```
📊 进度报告:
   当前ID: 34419650
   已处理: 100
   成功获取: 85
   404跳过: 12
   错误: 3
   连续404: 2
   处理速度: 0.95 ID/秒
   运行时间: 1.8 分钟
```

### 错误处理策略
1. **404错误**：记录并跳过，累计连续404计数
2. **429限流错误**：智能等待后重试（不再固定60秒）
3. **网络超时**：最多重试3次，指数退避
4. **其他错误**：记录错误并继续处理

## 故障排除

### 如果仍然遇到60秒等待
1. **检查日志**：查找"API频率限制 (429)"消息
2. **确认修复**：确保使用的是修复后的版本
3. **网络检查**：确认网络连接稳定

### 常见问题解决
```bash
# 1. 如果提示缺少模块
pip install requests pymongo

# 2. 如果数据库连接失败
# 检查MONGO_URI环境变量

# 3. 如果API调用失败
# 检查网络连接和Discogs API状态
```

### 性能优化建议
1. **避免高峰期**：在Discogs API使用较少的时间段运行
2. **监控频率**：确保不超过1请求/秒的限制
3. **分批处理**：对于大量数据，分多次运行
4. **网络稳定**：确保网络连接稳定

## 输出文件

### CSV输出
- 文件名格式：`api_releases_补全_YYYYMMDD_HHMMSS.csv`
- 包含所有成功获取的release数据
- 字段包括：id, title, year, country, artists, labels等

### 日志文件
- `api_incremental_log.txt` - 详细运行日志
- `simple_api_fetcher.log` - 简化版本日志

### 进度文件
- `api_incremental_progress.json` - 断点续传进度
- 支持中断后继续运行

## 验证修复效果

运行测试命令：
```bash
# 快速测试（只获取3条记录）
python run_api_fetcher.py --max-records 3
```

期望看到：
- ✅ API调用间隔约1秒
- ✅ 没有异常的60秒等待
- ✅ 如果遇到429错误，会显示智能等待时间（5/15/60秒）
- ✅ 正常的进度显示和CSV输出

## 总结

修复后的增量获取器已经解决了60秒等待问题：

1. **智能429处理**：不再固定等待60秒，而是渐进式等待
2. **详细监控**：实时显示API调用性能和错误信息
3. **稳定运行**：支持命令行参数，无需交互式输入
4. **断点续传**：支持中断后继续运行

现在你可以放心使用，API调用会以正常的1秒/请求频率运行！
