#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def debug_replacement():
    """调试替换逻辑"""
    
    # 原始CSV字段
    original = '[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]'
    print('原始:', original)
    print('原始长度:', len(original))
    
    # 查找问题部分
    problem_part = '""12\""""'
    print(f'\n问题部分: {problem_part}')
    print(f'问题部分在原始中的位置: {original.find(problem_part)}')
    
    # 目标替换（双重转义）
    target = '"12\\\\""'
    print(f'目标替换: {target}')
    
    # 执行替换
    step1 = original.replace(problem_part, target)
    print(f'\n步骤1替换后: {step1}')
    
    # 处理其他双引号
    step2 = step1.replace('""', '"')
    print(f'步骤2替换后: {step2}')
    
    # 测试JSON解析
    try:
        parsed = json.loads(step2)
        print('✅ JSON解析成功:', parsed[0]['descriptions'])
    except Exception as e:
        print('❌ JSON解析失败:', e)
        
        # 分析错误位置
        error_pos = str(e).split('char ')[-1].rstrip(')')
        if error_pos.isdigit():
            pos = int(error_pos)
            print(f'错误位置 {pos}: "{step2[max(0, pos-10):pos+10]}"')

if __name__ == '__main__':
    debug_replacement()
