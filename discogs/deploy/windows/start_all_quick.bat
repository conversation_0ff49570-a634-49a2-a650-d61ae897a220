@echo off
chcp 65001 >nul
echo ========================================
echo Discogs 数据处理 - 快速启动所有模块
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

echo 正在检查环境...

:: 检查虚拟环境
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 错误: 虚拟环境不存在
    echo 请先运行 setup.bat 进行初始化
    pause
    exit /b 1
)

:: 检查配置文件
set missing_config=0
for %%m in (artists labels masters releases) do (
    if not exist "config_%%m.env" (
        echo ❌ 缺少配置文件: config_%%m.env
        set missing_config=1
    )
)

if %missing_config%==1 (
    echo 请确保所有配置文件存在
    pause
    exit /b 1
)

:: 检查数据文件
echo.
echo 检查数据文件...
set missing_data=0
for %%m in (artists labels masters releases) do (
    :: 从配置文件读取XML_FILE
    for /f "usebackq tokens=2 delims==" %%f in (`findstr "^XML_FILE=" config_%%m.env 2^>nul`) do (
        if exist "%%f" (
            echo ✅ %%m: %%f
        ) else if exist "data\%%f" (
            echo ✅ %%m: data\%%f
        ) else (
            echo ❌ %%m: %%f ^(未找到^)
            set missing_data=1
        )
    )
)

if %missing_data%==1 (
    echo.
    echo ❌ 部分数据文件未找到
    echo 请将 gzip 文件放置在当前目录或 data 目录下
    echo.
    echo 需要的文件:
    echo - discogs_20250601_artists.xml.gz
    echo - discogs_20250601_labels.xml.gz
    echo - discogs_20250601_masters.xml.gz
    echo - discogs_20250601_releases.xml.gz
    pause
    exit /b 1
)

echo.
echo ✅ 环境检查通过
echo.
echo ⚠️  即将启动4个模块，这将消耗大量系统资源
echo 建议确保有足够的内存（推荐8GB+）和CPU资源
echo.
echo 预计运行时间:
echo - Artists: 1-2 天
echo - Labels: 2-3 天
echo - Masters: 1-2 天
echo - Releases: 15 天左右
echo.
set /p confirm="确认启动所有模块? (y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 正在启动所有模块...
echo.

:: 启动 Artists 模块
echo [1/4] 启动 Artists 模块...
start "Discogs-Artists" cmd /k "run_module.bat artists"
timeout /t 3 /nobreak >nul

:: 启动 Labels 模块
echo [2/4] 启动 Labels 模块...
start "Discogs-Labels" cmd /k "run_module.bat labels"
timeout /t 3 /nobreak >nul

:: 启动 Masters 模块
echo [3/4] 启动 Masters 模块...
start "Discogs-Masters" cmd /k "run_module.bat masters"
timeout /t 3 /nobreak >nul

:: 启动 Releases 模块
echo [4/4] 启动 Releases 模块...
start "Discogs-Releases" cmd /k "run_module.bat releases"

echo.
echo ✅ 所有模块已启动完成！
echo.
echo 📋 接下来您会看到4个独立的命令行窗口:
echo    - Discogs-Artists  (艺术家处理)
echo    - Discogs-Labels   (厂牌处理)
echo    - Discogs-Masters  (母版处理)
echo    - Discogs-Releases (发行版处理)
echo.
echo 💡 管理提示:
echo    - 每个窗口显示对应模块的处理进度
echo    - 可以单独关闭某个窗口停止对应模块
echo    - 使用任务管理器监控系统资源
echo    - 日志文件保存在 logs 目录下
echo.
echo 🔧 如需管理所有模块，请运行: manage_all.bat
echo.
pause
