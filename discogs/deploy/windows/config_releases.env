# Releases 模块 Windows 部署环境配置文件
# 请根据实际环境修改以下配置

# 模块标识
MODULE_NAME=releases
MODULE_DISPLAY_NAME=Releases 发行版

# XML 数据文件路径（支持相对路径和绝对路径）
# 留空表示自动检测文件名（推荐），或指定具体文件名
# XML_FILE=discogs_20250601_releases.xml.gz  # 指定具体文件
XML_FILE=  # 自动检测 discogs_*_releases.xml.gz 格式的文件

# MongoDB 连接配置
MONGO_URI=**********************************************************
DB_NAME=music_test

# 处理配置 - Releases 数据量最大，需要15天处理时间
# MAX_RECORDS=10000  # 处理指定数量的记录（测试用）
MAX_RECORDS=0        # 设置为0表示处理全部数据（推荐用于生产环境）

# 输出文件路径
OUTPUT_FILE=logs/releases_process_output.txt

# 进度保存配置
PROGRESS_FILE=progress/releases_progress.json
CHECKPOINT_INTERVAL=500

# 长时间运行配置 - Releases 需要特别关注资源管理
AUTO_RESTART=true
MAX_MEMORY_MB=4096
LOG_ROTATION_SIZE_MB=50
RESTART_INTERVAL_HOURS=12

# Python 环境配置
PYTHON_ENV=venv

# 特殊配置 - 针对超大数据集
ENABLE_MEMORY_MONITORING=true
ENABLE_DISK_MONITORING=true
MIN_FREE_DISK_GB=10
