@echo off
chcp 65001 >nul
echo ========================================
echo Discogs Artists 处理工具 - 运行脚本
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在，请先运行 setup.bat 进行初始化
    pause
    exit /b 1
)

:: 加载环境配置
if exist "config.env" (
    echo 正在加载配置文件...
    for /f "usebackq tokens=1,2 delims==" %%a in ("config.env") do (
        if not "%%a"=="" if not "%%a:~0,1%"=="#" (
            set "%%a=%%b"
        )
    )
) else (
    echo 警告: 未找到 config.env 配置文件，使用默认配置
)

:: 激活虚拟环境
echo 正在激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查 gzip 文件是否存在
if not exist "%XML_FILE%" (
    echo 错误: 未找到数据文件 %XML_FILE%
    echo 请确保 gzip 文件存在于当前目录或配置正确的文件路径
    pause
    exit /b 1
)

echo 找到数据文件: %XML_FILE%

:: 显示当前配置
echo.
echo 当前配置:
echo - 数据文件: %XML_FILE%
echo - MongoDB: %MONGO_URI%
echo - 数据库: %DB_NAME%
echo - 最大记录数: %MAX_RECORDS%
echo - 输出文件: %OUTPUT_FILE%
echo.

:: 询问是否继续
set /p confirm="确认以上配置无误，继续执行? (y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

:: 运行 Python 脚本
echo.
echo 开始处理数据...
echo ========================================

:: 复制必要的文件到当前目录
if not exist "enums.py" (
    copy "..\..\artists\enums.py" "enums.py" >nul
)
if not exist "process_artists.py" (
    copy "..\..\artists\process_artists.py" "process_artists.py" >nul
)

:: 运行处理脚本
python process_artists.py

:: 检查执行结果
if errorlevel 1 (
    echo.
    echo ========================================
    echo 处理过程中出现错误！
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 处理完成！
    echo ========================================
    if exist "%OUTPUT_FILE%" (
        echo 详细日志已保存到: %OUTPUT_FILE%
    )
)

echo.
pause
