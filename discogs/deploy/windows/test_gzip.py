#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
gzip 文件读取测试脚本
用于验证 Windows 环境下 gzip 文件处理功能
"""

import os
import gzip
import sys
import time
from pathlib import Path

def test_gzip_file(file_path, max_lines=10):
    """测试 gzip 文件读取功能"""
    print(f"测试文件: {file_path}")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 错误: 文件不存在 - {file_path}")
        return False
    
    # 获取文件信息
    file_size = os.path.getsize(file_path)
    print(f"📁 文件大小: {file_size:,} 字节 ({file_size / (1024*1024):.2f} MB)")
    
    try:
        # 测试文件打开
        print("🔍 正在测试文件打开...")
        start_time = time.time()
        
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            print("✅ 文件打开成功")
            
            # 读取前几行
            print(f"📖 读取前 {max_lines} 行内容:")
            print("-" * 30)
            
            line_count = 0
            for line in f:
                line_count += 1
                if line_count <= max_lines:
                    # 显示行内容（截断过长的行）
                    display_line = line.strip()
                    if len(display_line) > 100:
                        display_line = display_line[:100] + "..."
                    print(f"第 {line_count:3d} 行: {display_line}")
                
                # 只读取指定行数用于测试
                if line_count >= max_lines * 10:  # 多读一些行来测试性能
                    break
            
            open_time = time.time() - start_time
            print("-" * 30)
            print(f"✅ 成功读取 {min(line_count, max_lines * 10)} 行")
            print(f"⏱️  读取耗时: {open_time:.3f} 秒")
            
            # 测试 XML 标签检测
            print("\n🔍 测试 XML 标签检测...")
            f.seek(0)  # 重置文件指针
            
            artist_count = 0
            test_lines = 0
            for line in f:
                test_lines += 1
                if '<artist>' in line:
                    artist_count += 1
                if test_lines >= 1000:  # 只测试前1000行
                    break
            
            print(f"📊 前 {test_lines} 行中发现 {artist_count} 个 <artist> 标签")
            
        return True
        
    except UnicodeDecodeError as e:
        print(f"❌ 编码错误: {e}")
        print("💡 建议: 检查文件编码格式")
        return False
    except gzip.BadGzipFile as e:
        print(f"❌ gzip 文件格式错误: {e}")
        print("💡 建议: 检查文件是否为有效的 gzip 格式")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Discogs gzip 文件测试工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
    else:
        # 默认测试文件
        test_file = "discogs_20250601_artists.xml.gz"
    
    # 检查当前目录下的 gzip 文件
    current_dir = Path(".")
    gzip_files = list(current_dir.glob("*.gz"))
    
    if gzip_files:
        print("📂 发现以下 gzip 文件:")
        for i, file in enumerate(gzip_files, 1):
            print(f"  {i}. {file.name}")
        print()
    
    # 执行测试
    if os.path.exists(test_file):
        success = test_gzip_file(test_file)
        
        if success:
            print("\n🎉 测试完成 - gzip 文件读取功能正常")
            print("💡 可以继续运行主处理程序")
        else:
            print("\n❌ 测试失败 - 请检查文件和环境配置")
            
    else:
        print(f"❌ 测试文件不存在: {test_file}")
        print("\n💡 使用方法:")
        print(f"  python {sys.argv[0]} [gzip文件路径]")
        print("\n📝 示例:")
        print(f"  python {sys.argv[0]} discogs_20250601_artists.xml.gz")
        
        if gzip_files:
            print(f"\n或者测试发现的文件:")
            for file in gzip_files:
                print(f"  python {sys.argv[0]} {file.name}")

if __name__ == "__main__":
    main()
