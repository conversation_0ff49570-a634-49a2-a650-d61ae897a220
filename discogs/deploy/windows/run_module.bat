@echo off
chcp 65001 >nul

:: 检查参数
if "%1"=="" (
    echo 错误: 请指定模块名称
    echo 用法: run_module.bat [artists^|labels^|masters^|releases]
    echo.
    echo 示例:
    echo   run_module.bat artists
    echo   run_module.bat releases
    pause
    exit /b 1
)

set MODULE=%1
set CONFIG_FILE=config_%MODULE%.env

echo ========================================
echo Discogs %MODULE% 处理工具 - 长时间运行版本
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:: 检查配置文件是否存在
if not exist "%CONFIG_FILE%" (
    echo 错误: 未找到配置文件 %CONFIG_FILE%
    echo 请确保配置文件存在并正确命名
    pause
    exit /b 1
)

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在，请先运行 setup.bat 进行初始化
    pause
    exit /b 1
)

:: 创建必要的目录
if not exist "logs" mkdir logs
if not exist "progress" mkdir progress
if not exist "data" mkdir data

:: 加载环境配置
echo 正在加载配置文件: %CONFIG_FILE%
for /f "usebackq tokens=1,2 delims==" %%a in ("%CONFIG_FILE%") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

:: 激活虚拟环境
echo 正在激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查 gzip 文件是否存在
if not exist "%XML_FILE%" (
    if not exist "data\%XML_FILE%" (
        echo 错误: 未找到数据文件 %XML_FILE%
        echo 请确保 gzip 文件存在于当前目录或 data 目录中
        pause
        exit /b 1
    ) else (
        set "XML_FILE=data\%XML_FILE%"
    )
)

echo 找到数据文件: %XML_FILE%

:: 显示当前配置
echo.
echo 当前配置:
echo - 模块: %MODULE_DISPLAY_NAME%
echo - 数据文件: %XML_FILE%
echo - MongoDB: %MONGO_URI%
echo - 数据库: %DB_NAME%
echo - 最大记录数: %MAX_RECORDS%
echo - 输出文件: %OUTPUT_FILE%
echo - 进度文件: %PROGRESS_FILE%
echo - 自动重启: %AUTO_RESTART%
echo - 最大内存: %MAX_MEMORY_MB% MB
echo.

:: 询问是否继续
set /p confirm="确认以上配置无误，开始长时间运行处理? (y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b 0
)

:: 复制必要的文件到当前目录
echo 正在准备运行环境...
if not exist "enums.py" (
    if exist "..\..\%MODULE%\enums.py" (
        copy "..\..\%MODULE%\enums.py" "enums.py" >nul
    ) else (
        copy "..\..\artists\enums.py" "enums.py" >nul
    )
)

set PROCESS_SCRIPT=process_%MODULE%.py
if not exist "%PROCESS_SCRIPT%" (
    copy "..\..\%MODULE%\%PROCESS_SCRIPT%" "%PROCESS_SCRIPT%" >nul
)

:: 开始长时间运行循环
echo.
echo ========================================
echo 开始长时间运行处理...
echo 按 Ctrl+C 可以安全停止处理
echo ========================================

set RUN_COUNT=0
set START_TIME=%TIME%

:MAIN_LOOP
set /a RUN_COUNT+=1
echo.
echo [%DATE% %TIME%] 开始第 %RUN_COUNT% 次运行...

:: 运行处理脚本
python %PROCESS_SCRIPT%
set LAST_EXIT_CODE=%ERRORLEVEL%

echo [%DATE% %TIME%] 第 %RUN_COUNT% 次运行完成，退出代码: %LAST_EXIT_CODE%

:: 检查是否需要自动重启
if /i "%AUTO_RESTART%"=="true" (
    if %LAST_EXIT_CODE% neq 0 (
        echo 检测到错误，等待 30 秒后重启...
        timeout /t 30 /nobreak >nul
        goto MAIN_LOOP
    ) else (
        echo 处理完成，程序正常退出
    )
) else (
    echo 自动重启已禁用，程序退出
)

echo.
echo ========================================
echo 处理结束统计
echo ========================================
echo - 总运行次数: %RUN_COUNT%
echo - 开始时间: %START_TIME%
echo - 结束时间: %TIME%
echo - 最终退出代码: %LAST_EXIT_CODE%
echo.

if exist "%OUTPUT_FILE%" (
    echo 详细日志已保存到: %OUTPUT_FILE%
)

pause
