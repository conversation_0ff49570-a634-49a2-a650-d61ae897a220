@echo off
chcp 65001 >nul
echo ========================================
echo Discogs Artists 处理工具 - Windows 部署脚本
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:: 检查 Python 是否已安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.8 或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检测到 Python 版本:
python --version

:: 创建虚拟环境
echo.
echo 正在创建 Python 虚拟环境...
if not exist "venv" (
    python -m venv venv
    if errorlevel 1 (
        echo 错误: 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo 虚拟环境创建成功
) else (
    echo 虚拟环境已存在，跳过创建
)

:: 激活虚拟环境
echo.
echo 正在激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo 错误: 激活虚拟环境失败
    pause
    exit /b 1
)

:: 升级 pip
echo.
echo 正在升级 pip...
python -m pip install --upgrade pip

:: 安装依赖
echo.
echo 正在安装项目依赖...
if exist "..\..\requirements.txt" (
    pip install -r "..\..\requirements.txt"
) else (
    echo 警告: 未找到 requirements.txt，手动安装核心依赖
    pip install pymongo flask flask-cors psutil
)

if errorlevel 1 (
    echo 错误: 安装依赖失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 使用说明:
echo 1. 将 gzip 数据文件放在此目录下
echo 2. 修改 config.env 中的配置参数
echo 3. 运行 run.bat 启动处理程序
echo.
pause
