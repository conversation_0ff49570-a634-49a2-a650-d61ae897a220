@echo off
chcp 65001 >nul
echo ========================================
echo Discogs 数据处理工具 - 多模块管理器
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:MAIN_MENU
echo.
echo 请选择操作:
echo.
echo 1. 查看所有模块状态
echo 2. 启动单个模块
echo 3. 启动所有模块（并行）
echo 4. 停止所有模块
echo 5. 查看运行日志
echo 6. 清理日志和进度文件
echo 7. 系统资源监控
echo 8. 配置管理
echo 9. 退出
echo.
set /p choice="请输入选择 (1-9): "

if "%choice%"=="1" goto STATUS
if "%choice%"=="2" goto START_SINGLE
if "%choice%"=="3" goto START_ALL
if "%choice%"=="4" goto STOP_ALL
if "%choice%"=="5" goto VIEW_LOGS
if "%choice%"=="6" goto CLEANUP
if "%choice%"=="7" goto MONITOR
if "%choice%"=="8" goto CONFIG
if "%choice%"=="9" goto EXIT
goto MAIN_MENU

:STATUS
echo.
echo ========================================
echo 模块状态检查
echo ========================================
echo.

:: 检查进程是否运行
echo 正在检查运行中的进程...
tasklist /fi "imagename eq python.exe" /fo table 2>nul | findstr python.exe >nul
if %errorlevel%==0 (
    echo ✅ 发现 Python 进程正在运行
    tasklist /fi "imagename eq python.exe" /fo table
) else (
    echo ❌ 未发现运行中的 Python 进程
)

echo.
echo 检查配置文件:
for %%m in (artists labels masters releases) do (
    if exist "config_%%m.env" (
        echo ✅ config_%%m.env
    ) else (
        echo ❌ config_%%m.env
    )
)

echo.
echo 检查数据文件:
for %%m in (artists labels masters releases) do (
    :: 从配置文件读取XML_FILE
    for /f "usebackq tokens=2 delims==" %%f in (`findstr "^XML_FILE=" config_%%m.env 2^>nul`) do (
        if exist "%%f" (
            echo ✅ %%m: %%f
        ) else if exist "data\%%f" (
            echo ✅ %%m: data\%%f
        ) else (
            echo ❌ %%m: %%f ^(未找到^)
        )
    )
)

echo.
echo 检查日志文件:
if exist "logs" (
    echo 📁 logs 目录:
    dir logs\*.txt /b 2>nul
) else (
    echo ❌ logs 目录不存在
)

pause
goto MAIN_MENU

:START_SINGLE
echo.
echo 可用模块:
echo 1. artists  (艺术家)
echo 2. labels   (厂牌)
echo 3. masters  (母版)
echo 4. releases (发行版)
echo.
set /p module_choice="请选择模块 (1-4): "

if "%module_choice%"=="1" set MODULE_NAME=artists
if "%module_choice%"=="2" set MODULE_NAME=labels
if "%module_choice%"=="3" set MODULE_NAME=masters
if "%module_choice%"=="4" set MODULE_NAME=releases

if "%MODULE_NAME%"=="" (
    echo 无效选择
    goto START_SINGLE
)

echo.
echo 启动 %MODULE_NAME% 模块...
start "Discogs-%MODULE_NAME%" cmd /k "run_module.bat %MODULE_NAME%"
echo ✅ %MODULE_NAME% 模块已在新窗口中启动

pause
goto MAIN_MENU

:START_ALL
echo.
echo ========================================
echo 启动所有模块（并行运行）
echo ========================================
echo.
echo ⚠️  警告: 并行运行所有模块将消耗大量系统资源
echo    建议确保有足够的内存和CPU资源
echo.
set /p confirm="确认启动所有模块? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo 正在启动所有模块...

start "Discogs-Artists" cmd /k "run_module.bat artists"
timeout /t 2 /nobreak >nul

start "Discogs-Labels" cmd /k "run_module.bat labels"
timeout /t 2 /nobreak >nul

start "Discogs-Masters" cmd /k "run_module.bat masters"
timeout /t 2 /nobreak >nul

start "Discogs-Releases" cmd /k "run_module.bat releases"

echo.
echo ✅ 所有模块已启动，请查看各自的窗口
echo 💡 建议使用任务管理器监控系统资源使用情况

pause
goto MAIN_MENU

:STOP_ALL
echo.
echo ========================================
echo 停止所有模块
echo ========================================
echo.
echo ⚠️  这将强制终止所有 Python 进程
set /p confirm="确认停止所有模块? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo 正在停止所有 Python 进程...
taskkill /f /im python.exe 2>nul
if %errorlevel%==0 (
    echo ✅ 已停止所有 Python 进程
) else (
    echo ℹ️  未发现运行中的 Python 进程
)

pause
goto MAIN_MENU

:VIEW_LOGS
echo.
echo ========================================
echo 查看运行日志
echo ========================================
echo.

if not exist "logs" (
    echo ❌ logs 目录不存在
    pause
    goto MAIN_MENU
)

echo 可用日志文件:
echo.
set log_count=0
for %%f in (logs\*.txt) do (
    set /a log_count+=1
    echo !log_count!. %%f
)

if %log_count%==0 (
    echo ❌ 未找到日志文件
    pause
    goto MAIN_MENU
)

echo.
set /p log_choice="请选择要查看的日志文件编号 (1-%log_count%): "

:: 这里可以添加具体的日志查看逻辑
echo 正在打开日志文件...
:: 使用记事本打开最新的日志文件
for %%f in (logs\*.txt) do (
    notepad "%%f"
    goto MAIN_MENU
)

pause
goto MAIN_MENU

:CLEANUP
echo.
echo ========================================
echo 清理日志和进度文件
echo ========================================
echo.
echo ⚠️  这将删除所有日志和进度文件
echo    进度文件删除后，处理将从头开始
echo.
set /p confirm="确认清理? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo 正在清理文件...

if exist "logs" (
    del /q logs\*.* 2>nul
    echo ✅ 已清理 logs 目录
)

if exist "progress" (
    del /q progress\*.* 2>nul
    echo ✅ 已清理 progress 目录
)

echo ✅ 清理完成

pause
goto MAIN_MENU

:MONITOR
echo.
echo ========================================
echo 系统资源监控
echo ========================================
echo.

echo 💾 内存使用情况:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:table

echo.
echo 💿 磁盘空间:
wmic logicaldisk get size,freespace,caption /format:table

echo.
echo 🔄 CPU 使用率:
wmic cpu get loadpercentage /value

echo.
echo 📊 Python 进程:
tasklist /fi "imagename eq python.exe" /fo table 2>nul

pause
goto MAIN_MENU

:CONFIG
echo.
echo ========================================
echo 配置管理
echo ========================================
echo.
echo 1. 编辑 Artists 配置
echo 2. 编辑 Labels 配置  
echo 3. 编辑 Masters 配置
echo 4. 编辑 Releases 配置
echo 5. 返回主菜单
echo.
set /p config_choice="请选择 (1-5): "

if "%config_choice%"=="1" notepad config_artists.env
if "%config_choice%"=="2" notepad config_labels.env
if "%config_choice%"=="3" notepad config_masters.env
if "%config_choice%"=="4" notepad config_releases.env
if "%config_choice%"=="5" goto MAIN_MENU

goto CONFIG

:EXIT
echo.
echo 感谢使用 Discogs 数据处理工具！
echo.
pause
exit /b 0
