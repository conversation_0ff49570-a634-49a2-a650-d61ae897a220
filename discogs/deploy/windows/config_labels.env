# Labels 模块 Windows 部署环境配置文件
# 请根据实际环境修改以下配置

# 模块标识
MODULE_NAME=labels
MODULE_DISPLAY_NAME=Labels 厂牌

# XML 数据文件路径（支持相对路径和绝对路径）
# 留空表示自动检测文件名（推荐），或指定具体文件名
# XML_FILE=discogs_20250601_labels.xml.gz  # 指定具体文件
XML_FILE=  # 自动检测 discogs_*_labels.xml.gz 格式的文件

# MongoDB 连接配置
MONGO_URI=**********************************************************
DB_NAME=music_test

# 处理配置 - Labels 数据量中等
# MAX_RECORDS=30000  # 处理指定数量的记录
MAX_RECORDS=0        # 设置为0表示处理全部数据（推荐用于生产环境）

# 输出文件路径
OUTPUT_FILE=logs/labels_process_output.txt

# 进度保存配置
PROGRESS_FILE=progress/labels_progress.json
CHECKPOINT_INTERVAL=1000

# 长时间运行配置
AUTO_RESTART=true
MAX_MEMORY_MB=2048
LOG_ROTATION_SIZE_MB=100

# Python 环境配置
PYTHON_ENV=venv
