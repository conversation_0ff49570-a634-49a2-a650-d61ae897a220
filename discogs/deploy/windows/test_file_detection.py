#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试动态文件检测功能
"""

import os
import glob
import sys

def find_xml_file(module_type):
    """
    从 deploy/windows/data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    print(f"\n🔍 正在搜索 {module_type} 模块的XML文件...")

    data_dir = 'data'
    pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    print(f"   搜索路径: {pattern}")

    found_files = glob.glob(pattern)

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
        print(f"   所有找到的文件: {found_files}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

def test_file_detection():
    """测试所有模块的文件检测"""
    print("=" * 60)
    print("🧪 测试动态文件检测功能")
    print("=" * 60)
    
    modules = ['artists', 'labels', 'masters', 'releases']
    
    for module in modules:
        result = find_xml_file(module)
        if result:
            # 检查文件大小
            try:
                size = os.path.getsize(result)
                size_mb = size / (1024 * 1024)
                print(f"   文件大小: {size_mb:.1f} MB")
            except:
                print(f"   无法获取文件大小")
        print("-" * 40)
    
    print("\n📋 测试总结:")
    print("1. 如果所有模块都找到文件，说明检测功能正常")
    print("2. 如果某个模块未找到文件，请检查文件是否存在")
    print("3. 文件应该放在当前目录或 data/ 目录下")
    print("4. 文件名格式: discogs_YYYYMMDD_<module>.xml.gz")

def test_with_config():
    """测试简化后的文件检测"""
    print("\n" + "=" * 60)
    print("🧪 测试简化后的文件检测功能")
    print("=" * 60)
    print("现在直接从 data/ 目录获取文件，无需配置文件指定")

    modules = ['artists', 'labels', 'masters', 'releases']
    for module in modules:
        print(f"\n测试 {module} 模块:")
        result = find_xml_file(module)
        if result:
            print(f"   ✅ 成功找到文件")
        else:
            print(f"   ❌ 未找到文件")

if __name__ == "__main__":
    test_file_detection()
    test_with_config()
