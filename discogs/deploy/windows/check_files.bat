@echo off
chcp 65001 >nul
echo ========================================
echo Discogs 数据文件检查工具
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

echo.
echo 📂 检查数据文件位置...
echo.

:: 定义需要的文件
set files=discogs_20250601_artists.xml.gz discogs_20250601_labels.xml.gz discogs_20250601_masters.xml.gz discogs_20250601_releases.xml.gz

:: 检查当前目录
echo 🔍 检查当前目录:
for %%f in (%files%) do (
    if exist "%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ %%f
    )
)

echo.
echo 🔍 检查 data 目录:
if exist "data" (
    for %%f in (%files%) do (
        if exist "data\%%f" (
            echo ✅ data\%%f
        ) else (
            echo ❌ data\%%f
        )
    )
) else (
    echo ❌ data 目录不存在
    echo 💡 建议创建 data 目录并将 gzip 文件放入其中
)

echo.
echo 📊 文件大小信息:
echo.
for %%f in (%files%) do (
    if exist "%%f" (
        for %%s in ("%%f") do echo 📁 %%f: %%~zs 字节
    ) else if exist "data\%%f" (
        for %%s in ("data\%%f") do echo 📁 data\%%f: %%~zs 字节
    )
)

echo.
echo 🧪 测试 gzip 文件完整性:
echo.

:: 测试第一个找到的文件
for %%f in (%files%) do (
    if exist "%%f" (
        echo 正在测试 %%f...
        python test_gzip.py "%%f"
        goto :test_done
    ) else if exist "data\%%f" (
        echo 正在测试 data\%%f...
        python test_gzip.py "data\%%f"
        goto :test_done
    )
)

echo ❌ 未找到任何 gzip 文件进行测试

:test_done
echo.
echo ========================================
echo 检查完成
echo ========================================
echo.
echo 💡 文件放置建议:
echo.
echo 方式1 (推荐): 放在 data 目录下
echo   mkdir data
echo   copy *.gz data\
echo.
echo 方式2: 直接放在当前目录
echo   copy *.gz .
echo.
echo 📝 需要的文件:
for %%f in (%files%) do (
    echo   - %%f
)
echo.
pause
