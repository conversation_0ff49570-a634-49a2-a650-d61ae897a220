#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证递增式API获取器的数据插入结果
"""

import os
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def main():
    try:
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]

        # 检查release_new表中新插入的数据
        collection = db['release_new']

        # 先检查所有记录数量
        total_all_count = collection.count_documents({})
        print(f'📊 release_new表总记录数: {total_all_count}')

        # 检查最近的记录（按_id排序）
        recent_all = list(collection.find({}).sort('_id', -1).limit(5))
        print('📊 最近的5条记录（按插入时间）:')
        for record in recent_all:
            title = record.get('title', 'N/A')
            if len(title) > 50:
                title = title[:50] + '...'
            print(f'  ID: {record.get("id")}, Title: {title}, Source: {record.get("source", "N/A")}')

        # 检查特定source的记录
        recent_records = list(collection.find({'source': 'api_incremental_fetcher'}).sort('id', -1).limit(5))
        print('\n📊 api_incremental_fetcher插入的记录:')
        for record in recent_records:
            title = record.get('title', 'N/A')
            if len(title) > 50:
                title = title[:50] + '...'
            print(f'  ID: {record.get("id")}, Title: {title}, Source: {record.get("source")}')

        # 统计总数
        total_count = collection.count_documents({'source': 'api_incremental_fetcher'})
        print(f'📈 api_incremental_fetcher插入了 {total_count} 条记录')

        # 检查特定ID
        test_record = collection.find_one({'id': 300000})
        if test_record:
            print(f'🔍 找到测试记录 ID 300000: {test_record.get("title", "N/A")}, Source: {test_record.get("source", "N/A")}')
        else:
            print('❌ 未找到测试记录 ID 300000')

        # 检查404表
        not_found_collection = db['release_404']
        not_found_count = not_found_collection.count_documents({'source': 'api_incremental_fetcher'})
        print(f'📈 404记录数: {not_found_count} 条')

        # 检查429记录
        rate_limited_count = collection.count_documents({'source': 'api_incremental_fetcher', 'status': 429})
        print(f'📈 429记录数: {rate_limited_count} 条')

        client.close()
        print('✅ 验证完成')

    except Exception as e:
        print(f'❌ 验证失败: {e}')
        import traceback
        print(f'详细错误: {traceback.format_exc()}')

if __name__ == "__main__":
    main()
