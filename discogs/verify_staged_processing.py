#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分阶段处理功能验证脚本

简单验证分阶段处理的核心功能是否正常工作
"""

import os
import sys

# 设置测试环境变量
os.environ['TEST_MODE'] = 'true'
os.environ['STAGED_MODE'] = 'true'
os.environ['SEGMENT_SIZE'] = '100'  # 非常小的段
os.environ['PROCESSING_BATCH_SIZE'] = '5'  # 非常小的批次

def test_imports():
    """测试导入功能"""
    print("🧪 测试模块导入...")
    
    try:
        from api_release_补全器 import (
            detect_missing_ids_by_segments,
            create_processing_batches,
            process_batch,
            main_staged,
            connect_to_mongodb,
            DiscogsAPIClient
        )
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入异常: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🧪 测试数据库连接...")
    
    try:
        from api_release_补全器 import connect_to_mongodb
        
        client, db = connect_to_mongodb()
        
        # 简单测试查询
        collection = db.release_new
        count = collection.count_documents({})
        print(f"✅ 数据库连接成功，release_new集合包含 {count:,} 条记录")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_api_client():
    """测试API客户端"""
    print("🧪 测试API客户端...")
    
    try:
        from api_release_补全器 import DiscogsAPIClient
        
        api_client = DiscogsAPIClient()
        
        # 测试一个已知存在的ID
        test_id = 1
        result = api_client.get_release(test_id)
        
        if result and isinstance(result, dict):
            print(f"✅ API客户端工作正常，成功获取ID {test_id} 的数据")
            return True
        elif result is None:
            print(f"⚠️ API客户端工作正常，但ID {test_id} 不存在 (404)")
            return True
        else:
            print(f"❌ API客户端返回异常结果: {result}")
            return False
            
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        return False

def test_batch_creation():
    """测试批次创建功能"""
    print("🧪 测试批次创建...")
    
    try:
        from api_release_补全器 import create_processing_batches
        
        # 创建测试数据
        test_ids = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
        
        batches = create_processing_batches(test_ids)
        
        print(f"✅ 成功创建 {len(batches)} 个批次")
        
        # 验证批次内容
        total_ids = sum(len(batch['ids']) for batch in batches)
        if total_ids == len(test_ids):
            print(f"✅ 批次ID数量正确: {total_ids}")
            return True
        else:
            print(f"❌ 批次ID数量不匹配: 期望 {len(test_ids)}, 实际 {total_ids}")
            return False
            
    except Exception as e:
        print(f"❌ 批次创建测试失败: {e}")
        return False

def test_configuration():
    """测试配置参数"""
    print("🧪 测试配置参数...")
    
    try:
        from api_release_补全器 import (
            SEGMENT_SIZE,
            PROCESSING_BATCH_SIZE,
            STAGED_MODE,
            TEST_MODE
        )
        
        print(f"📊 SEGMENT_SIZE: {SEGMENT_SIZE}")
        print(f"📦 PROCESSING_BATCH_SIZE: {PROCESSING_BATCH_SIZE}")
        print(f"🔄 STAGED_MODE: {os.getenv('STAGED_MODE')}")
        print(f"🧪 TEST_MODE: {TEST_MODE}")
        
        # 验证配置值
        if SEGMENT_SIZE == 100 and PROCESSING_BATCH_SIZE == 5:
            print("✅ 配置参数正确")
            return True
        else:
            print("❌ 配置参数不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("🗑️ 清理测试文件...")
    
    files_to_clean = [
        'missing_ids_segments.json',
        'batch_config.json',
        'segment_progress.json'
    ]
    
    # 清理批次文件
    try:
        batch_files = [f for f in os.listdir('.') if f.startswith('batch_') and f.endswith('.csv')]
        files_to_clean.extend(batch_files)
    except:
        pass
    
    # 清理进度目录
    if os.path.exists('batch_progress'):
        import shutil
        shutil.rmtree('batch_progress')
        print("🗑️ 清理批次进度目录")
    
    cleaned_count = 0
    for file in files_to_clean:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ 清理: {file}")
            cleaned_count += 1
    
    print(f"✅ 清理完成，删除了 {cleaned_count} 个文件")

def main():
    """主函数"""
    print("🚀 分阶段处理功能验证脚本")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据库连接", test_database_connection),
        ("API客户端", test_api_client),
        ("批次创建", test_batch_creation),
        ("配置参数", test_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能验证通过！")
        print("💡 分阶段处理功能已准备就绪")
        print("\n📝 使用方法:")
        print("   export STAGED_MODE='true'")
        print("   python3 api_release_补全器.py")
    else:
        print("⚠️ 部分功能验证失败，请检查配置")
    
    # 清理测试文件
    cleanup_test_files()
    
    print("\n🏁 验证完成")

if __name__ == "__main__":
    main()
