#!/usr/bin/env python3
"""
逐步调试JSON解析过程
"""

import json
import re

def debug_parse_json_field(field_value: str):
    """逐步调试JSON解析过程"""
    print(f"原始输入: {field_value}")
    
    if not field_value or field_value.strip() == '':
        return []
    
    # 处理CSV中的双引号转义
    if field_value.startswith('"') and field_value.endswith('"'):
        field_value = field_value[1:-1]
        print(f"去除外层引号: {field_value}")
    
    # 1. 先处理特殊的引号+反斜杠模式
    original = field_value
    field_value = field_value.replace('""12\""""', '"12__QUOTE__"')
    if field_value != original:
        print(f"处理12英寸模式: {field_value}")
    
    # 2. 先处理普通的双引号转义
    original = field_value
    field_value = field_value.replace('""', '"')
    if field_value != original:
        print(f"处理双引号转义: {field_value}")

    # 3. 修复空字符串模式
    original = field_value
    field_value = re.sub(r':\s*"\s*,', r': "",', field_value)
    if field_value != original:
        print(f"修复空字符串+逗号: {field_value}")

    original = field_value
    field_value = re.sub(r':\s*"\s*}', r': ""}', field_value)
    if field_value != original:
        print(f"修复空字符串+右括号: {field_value}")

    original = field_value
    field_value = re.sub(r':\s*"\s*]', r': ""]', field_value)
    if field_value != original:
        print(f"修复空字符串+右方括号: {field_value}")

    # 4. 修复未终止字符串
    original = field_value
    field_value = re.sub(r':\s*"([^"]*)"?\s*}', r': "\1"}', field_value)
    if field_value != original:
        print(f"修复未终止字符串+右括号: {field_value}")

    original = field_value
    field_value = re.sub(r':\s*"([^"]*)"?\s*]', r': "\1"]', field_value)
    if field_value != original:
        print(f"修复未终止字符串+右方括号: {field_value}")

    # 5. 恢复特殊标记
    original = field_value
    field_value = field_value.replace('__QUOTE__', '\\"')
    if field_value != original:
        print(f"恢复特殊标记: {field_value}")
    
    print(f"最终结果: {field_value}")
    
    try:
        parsed = json.loads(field_value)
        print(f"✅ 解析成功: {parsed}")
        return parsed
    except (json.JSONDecodeError, ValueError) as e:
        print(f"❌ 解析失败: {e}")
        return []

# 测试一个具体的问题案例
test_case = '[{"artist_id": 22072, "name": "Manitou", "role": ", "anv": "}]'
print("=" * 60)
print("调试测试案例:")
debug_parse_json_field(test_case)
