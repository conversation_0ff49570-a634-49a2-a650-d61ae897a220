#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的searchRuleRelease字段检查脚本
快速查看几条记录来了解字段结构
"""

import os
import json
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def check_searchrule_field():
    """检查searchRuleRelease字段"""
    print("🔍 开始检查searchRuleRelease字段...")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    print("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取release集合
        release_collection = db['release']
        
        # 检查集合是否存在
        if 'release' not in db.list_collection_names():
            print("❌ 错误: release集合不存在")
            return
        
        print("📊 正在查找包含searchRuleRelease字段的记录...")
        
        # 查找前5条包含searchRuleRelease字段的记录
        records = list(release_collection.find(
            {"searchRuleRelease": {"$exists": True}},
            {"id": 1, "searchRuleRelease": 1}
        ).limit(5))
        
        if not records:
            print("⚠️ 没有找到包含searchRuleRelease字段的记录")
            
            # 检查字段名是否不同
            print("🔍 检查可能的相似字段名...")
            sample_record = release_collection.find_one({}, {"_id": 0})
            if sample_record:
                fields = list(sample_record.keys())
                search_related = [f for f in fields if 'search' in f.lower() or 'rule' in f.lower()]
                if search_related:
                    print(f"📋 找到相关字段: {search_related}")
                else:
                    print("📋 所有字段:", fields[:20])  # 显示前20个字段
            return
        
        print(f"✅ 找到 {len(records)} 条包含searchRuleRelease字段的记录")
        
        for i, record in enumerate(records, 1):
            print(f"\n📋 记录 #{i} (ID: {record.get('id', 'unknown')})")
            print("-" * 50)
            
            searchrule_data = record.get('searchRuleRelease')
            
            if searchrule_data is None:
                print("   searchRuleRelease: null")
                continue
            
            # 检查数据类型
            data_type = type(searchrule_data).__name__
            print(f"   数据类型: {data_type}")
            
            if isinstance(searchrule_data, str):
                print(f"   字符串长度: {len(searchrule_data)}")
                if len(searchrule_data) > 0:
                    print(f"   前100字符: {searchrule_data[:100]}")
                    
                    # 尝试解析JSON
                    try:
                        parsed_data = json.loads(searchrule_data)
                        print("   ✅ JSON解析成功")
                        searchrule_data = parsed_data
                    except json.JSONDecodeError as e:
                        print(f"   ❌ JSON解析失败: {e}")
                        continue
            
            if isinstance(searchrule_data, dict):
                print("   📁 JSON结构:")
                print(f"   顶级键: {list(searchrule_data.keys())}")
                
                # 检查baseInfo
                if 'baseInfo' in searchrule_data:
                    base_info = searchrule_data['baseInfo']
                    print("   ✅ 找到baseInfo字段")
                    
                    if isinstance(base_info, dict):
                        print(f"   baseInfo键: {list(base_info.keys())}")
                        
                        # 检查barcode
                        if 'barcode' in base_info:
                            barcode_value = base_info['barcode']
                            print(f"   🎯 找到barcode: {barcode_value}")
                        else:
                            print("   ❌ baseInfo中未找到barcode字段")
                    else:
                        print(f"   ⚠️ baseInfo不是字典: {type(base_info).__name__}")
                else:
                    print("   ❌ 未找到baseInfo字段")
                
                # 显示完整结构
                try:
                    json_str = json.dumps(searchrule_data, ensure_ascii=False, indent=2)
                    if len(json_str) > 1000:
                        json_str = json_str[:1000] + "\n   ..."
                    print(f"   完整JSON:\n{json_str}")
                except Exception as e:
                    print(f"   ❌ 无法序列化JSON: {e}")
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
        raise
    finally:
        client.close()
        print("\n🔒 已关闭MongoDB连接")

if __name__ == "__main__":
    try:
        check_searchrule_field()
        print("\n✅ 检查完成！")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        exit(1)
