#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
精确的格式错误记录分析工具

基于初步分析的发现，重新评估所谓的"格式错误"记录
重点验证：这些记录是否真的是嵌套问题，还是连续的独立记录

作者：AI Assistant
创建时间：2025-07-28
"""

import gzip
import time
import re
import os
from datetime import datetime

# 输出文件路径
REPORT_FILE = 'precise_analysis_report.txt'
DETAILED_SAMPLES_FILE = 'detailed_samples.txt'

# 清理旧文件
for file_path in [REPORT_FILE, DETAILED_SAMPLES_FILE]:
    if os.path.exists(file_path):
        os.remove(file_path)

def write_output(message, print_to_console=True):
    """将消息写入报告文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('[%Y-%m-%d %H:%M:%S]')
    formatted_message = f"{timestamp} {message}"
    
    with open(REPORT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def write_detailed(content):
    """将详细内容写入详细样本文件"""
    with open(DETAILED_SAMPLES_FILE, 'a', encoding='utf-8') as f:
        f.write(content + '\n')

class PreciseMalformedAnalyzer:
    def __init__(self, xml_file):
        self.xml_file = xml_file
        self.analysis_results = {
            'consecutive_releases': 0,  # 连续的独立release记录
            'true_nested_releases': 0,  # 真正的嵌套release
            'incomplete_releases': 0,   # 不完整的release记录
            'valid_releases': 0,        # 有效的release记录
            'parsing_errors': 0         # 解析错误
        }
        self.detailed_samples = []
        
    def analyze_specific_lines(self, target_lines):
        """分析特定行号周围的内容，验证是否真的是嵌套问题"""
        write_output(f"🔍 精确分析特定行号周围的内容...")
        
        # 从之前的分析中提取的问题行号
        problem_lines = [1268, 2646, 2653, 2658, 2967, 3239, 3381, 4041, 4563, 4693]
        
        try:
            with gzip.open(self.xml_file, 'rt', encoding='utf-8') as f:
                lines = []
                current_line = 0
                
                for line in f:
                    current_line += 1
                    lines.append((current_line, line))
                    
                    # 检查是否接近目标行号
                    for target_line in problem_lines:
                        if abs(current_line - target_line) <= 5:
                            self._analyze_context_around_line(lines, target_line, current_line)
                    
                    # 限制内存使用，只保留最近的20行
                    if len(lines) > 20:
                        lines = lines[-20:]
                    
                    # 限制分析范围
                    if current_line > max(problem_lines) + 10:
                        break
                        
        except Exception as e:
            write_output(f"❌ 分析过程中出错: {e}")
            return False
        
        return True
    
    def _analyze_context_around_line(self, lines, target_line, current_line):
        """分析目标行周围的上下文"""
        if current_line == target_line:
            # 找到目标行，分析前后5行的上下文
            context_lines = [line for line_num, line in lines if abs(line_num - target_line) <= 5]
            
            write_detailed(f"\n{'='*60}")
            write_detailed(f"分析目标行 {target_line} 周围的上下文:")
            write_detailed(f"{'='*60}")
            
            for line_num, line in lines:
                if abs(line_num - target_line) <= 5:
                    marker = " >>> " if line_num == target_line else "     "
                    write_detailed(f"{marker}第{line_num}行: {line.strip()}")
            
            # 分析这个上下文
            analysis = self._analyze_context_pattern(context_lines, target_line)
            write_detailed(f"\n分析结果: {analysis}")
            
            self.detailed_samples.append({
                'target_line': target_line,
                'context': context_lines,
                'analysis': analysis
            })
    
    def _analyze_context_pattern(self, context_lines, target_line):
        """分析上下文模式，判断是否真的是嵌套问题"""
        context_text = ''.join(context_lines)
        
        # 计算release开始和结束标签
        release_starts = len(re.findall(r'<release\s+id=', context_text))
        release_ends = len(re.findall(r'</release>', context_text))
        
        # 检查是否有真正的嵌套结构
        if release_starts > release_ends + 1:
            return f"可能的真正嵌套: {release_starts}个开始标签, {release_ends}个结束标签"
        elif release_starts == release_ends + 1:
            return f"正常的连续记录: {release_starts}个开始标签, {release_ends}个结束标签"
        elif release_starts == release_ends:
            return f"完整的记录块: {release_starts}个开始标签, {release_ends}个结束标签"
        else:
            return f"异常模式: {release_starts}个开始标签, {release_ends}个结束标签"
    
    def analyze_xml_structure_improved(self, max_records=10000):
        """改进的XML结构分析，更精确地识别问题"""
        write_output(f"🔍 开始改进的XML结构分析（处理{max_records:,}条记录）...")
        start_time = time.time()
        
        processed_records = 0
        current_record_lines = []
        in_release = False
        release_start_line = 0
        release_id = None
        
        try:
            with gzip.open(self.xml_file, 'rt', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    
                    # 检测release记录开始
                    if '<release ' in line and 'id=' in line:
                        # 提取release ID
                        id_match = re.search(r'id="(\d+)"', line)
                        new_release_id = id_match.group(1) if id_match else "unknown"
                        
                        if in_release:
                            # 发现新的release开始，但前一个还没结束
                            write_output(f"   ⚠️ 第{line_num}行：发现新release {new_release_id}，但前一个release {release_id}（第{release_start_line}行开始）还未结束")
                            
                            # 检查前一个记录是否真的不完整
                            if self._is_record_actually_complete(current_record_lines):
                                write_output(f"   ✅ 前一个记录实际上是完整的，这是连续的独立记录")
                                self.analysis_results['consecutive_releases'] += 1
                            else:
                                write_output(f"   ❌ 前一个记录确实不完整")
                                self.analysis_results['incomplete_releases'] += 1
                        
                        # 开始新记录
                        in_release = True
                        release_start_line = line_num
                        release_id = new_release_id
                        current_record_lines = [line]
                        
                    elif '</release>' in line and in_release:
                        current_record_lines.append(line)
                        in_release = False
                        processed_records += 1
                        
                        # 验证记录完整性
                        if self._validate_complete_record(current_record_lines):
                            self.analysis_results['valid_releases'] += 1
                        else:
                            self.analysis_results['parsing_errors'] += 1
                        
                        current_record_lines = []
                        
                        # 限制处理数量
                        if processed_records >= max_records:
                            break
                        
                    elif in_release:
                        current_record_lines.append(line)
                    
                    # 显示进度
                    if processed_records > 0 and processed_records % 2000 == 0:
                        write_output(f"   📈 已处理 {processed_records:,} 条记录")
        
        except Exception as e:
            write_output(f"❌ 分析过程中出错: {e}")
            return False
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ 改进的XML结构分析完成")
        write_output(f"   📊 处理记录数: {processed_records:,}")
        write_output(f"   ⏱️ 耗时: {elapsed_time:.2f} 秒")
        
        return True
    
    def _is_record_actually_complete(self, record_lines):
        """检查记录是否实际上是完整的（即使没有明确的结束标签）"""
        record_text = ''.join(record_lines)
        
        # 检查是否包含基本的release结构
        has_id = 'id=' in record_text
        has_title = '<title>' in record_text
        has_artists = '<artists>' in record_text
        
        # 如果包含这些基本元素，可能是完整的记录
        return has_id and has_title and has_artists
    
    def _validate_complete_record(self, record_lines):
        """验证完整记录的有效性"""
        record_text = ''.join(record_lines)
        
        # 基本结构检查
        if not record_text.strip():
            return False
        
        # 检查开始和结束标签
        if '<release ' not in record_text or '</release>' not in record_text:
            return False
        
        # 检查基本字段
        required_fields = ['id=', '<title>', '<artists>']
        for field in required_fields:
            if field not in record_text:
                return False
        
        return True
    
    def generate_final_assessment(self):
        """生成最终评估报告"""
        write_output("\n" + "="*60)
        write_output("📋 精确分析最终评估报告")
        write_output("="*60)
        
        total_analyzed = sum(self.analysis_results.values())
        write_output(f"📊 总分析记录数: {total_analyzed:,}")
        
        # 详细统计
        write_output(f"\n📊 详细分析结果:")
        for result_type, count in self.analysis_results.items():
            if count > 0:
                percentage = (count / total_analyzed) * 100 if total_analyzed > 0 else 0
                write_output(f"   {result_type}: {count:,} ({percentage:.1f}%)")
        
        # 关键发现
        write_output(f"\n🎯 关键发现:")
        
        consecutive_ratio = (self.analysis_results['consecutive_releases'] / total_analyzed) * 100 if total_analyzed > 0 else 0
        
        if consecutive_ratio > 80:
            write_output("✅ 大部分'嵌套'问题实际上是连续的独立release记录")
            write_output("✅ 这表明原始的解析逻辑存在误判")
            write_output("✅ XML文件结构基本正常，无需大规模数据修复")
        elif consecutive_ratio > 50:
            write_output("⚠️ 部分'嵌套'问题是连续记录，部分可能是真正的问题")
            write_output("⚠️ 需要进一步细化分析")
        else:
            write_output("❌ 发现真正的数据质量问题")
            write_output("❌ 需要制定数据修复策略")
        
        # 修复建议
        write_output(f"\n💡 修复建议:")
        if consecutive_ratio > 80:
            write_output("1. 修正XML解析逻辑，正确处理连续的release记录")
            write_output("2. 不需要修复XML源文件")
            write_output("3. 重新运行数据导入流程，使用改进的解析逻辑")
        else:
            write_output("1. 详细检查真正的嵌套问题")
            write_output("2. 制定针对性的数据清理策略")
            write_output("3. 考虑从源头重新获取数据")
        
        write_output(f"\n📄 详细样本分析已保存到: {DETAILED_SAMPLES_FILE}")
        write_output("="*60)

def main():
    """主函数"""
    print("🔍 精确的格式错误记录分析工具")
    print("="*60)
    
    xml_file = 'discogs_20250701_releases.xml.gz'
    if not os.path.exists(xml_file):
        write_output(f"❌ 找不到XML文件: {xml_file}")
        return
    
    start_time = time.time()
    
    # 创建分析器
    analyzer = PreciseMalformedAnalyzer(xml_file)
    
    # 执行精确分析
    write_output("🎯 开始精确分析...")
    
    # 1. 分析特定问题行号
    analyzer.analyze_specific_lines([1268, 2646, 2653, 2658, 2967])
    
    # 2. 改进的整体结构分析
    if analyzer.analyze_xml_structure_improved():
        # 3. 生成最终评估
        analyzer.generate_final_assessment()
    
    total_time = time.time() - start_time
    write_output(f"\n⏱️ 总分析时间: {total_time:.2f} 秒")
    write_output(f"📄 分析报告已保存到: {REPORT_FILE}")

if __name__ == "__main__":
    main()
