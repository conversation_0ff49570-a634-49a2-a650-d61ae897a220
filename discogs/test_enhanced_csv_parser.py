#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强型CSV解析器测试脚本

功能：
1. 测试修改后的csv_to_mongodb_importer.py能否正确处理包含换行符的CSV文件
2. 验证JSON字段解析功能
3. 确认所有数据行都能被正确解析
4. 检查notes字段的换行符处理

作者：AI Assistant
创建时间：2025-07-30
"""

import os
import sys
import json
import pandas as pd
import csv
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from csv_to_mongodb_importer import CSVToMongoImporter

def test_pandas_csv_reading():
    """测试pandas读取CSV文件的能力"""
    print("🧪 测试pandas读取CSV文件...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        # 使用pandas读取CSV
        df = pd.read_csv(
            csv_file,
            encoding='utf-8',
            quoting=csv.QUOTE_ALL,
            na_filter=False,
            dtype=str
        )
        
        print(f"✅ 成功读取CSV文件，总行数: {len(df)}")
        print(f"📊 列数: {len(df.columns)}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 检查前几行数据
        print("\n📖 前3行数据预览:")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"行 {i+1}:")
            print(f"  ID: {row['id']}")
            print(f"  Y_ID: {row['y_id']}")
            print(f"  Title: {row['title'][:50]}...")
            print(f"  Notes: {row['notes'][:100] if row['notes'] else 'None'}...")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ pandas读取CSV失败: {e}")
        return False

def test_json_field_parsing():
    """测试JSON字段解析"""
    print("🧪 测试JSON字段解析...")
    
    # 创建测试实例
    importer = CSVToMongoImporter("dummy.csv", test_mode=True)
    
    # 测试artists字段
    test_artists = '[{"artist_id": 3394051, "name": "The California Ramblers", "role": "", "anv": ""}]'
    result = importer.parse_json_field(test_artists)
    
    if isinstance(result, list) and len(result) > 0:
        print("✅ artists字段解析成功")
        print(f"   解析结果: {result[0]}")
    else:
        print("❌ artists字段解析失败")
        return False
    
    # 测试转义的JSON
    test_escaped = '"[{""artist_id"": 3394051, ""name"": ""Test Artist"", ""role"": """", ""anv"": """"}]"'
    result = importer.parse_json_field(test_escaped)
    
    if isinstance(result, list) and len(result) > 0:
        print("✅ 转义JSON解析成功")
    else:
        print("❌ 转义JSON解析失败")
        return False
    
    return True

def test_text_field_cleaning():
    """测试文本字段清理"""
    print("🧪 测试文本字段清理...")
    
    importer = CSVToMongoImporter("dummy.csv", test_mode=True)
    
    # 测试包含换行符的文本
    test_text = 'Original Italian theme from television broadcast "Gaiking Il Robot Guerriero".\n\nFor a similar version, but printed in October 1979, see [r=3481118]'
    
    cleaned = importer.clean_text_field(test_text)
    
    print(f"原始文本: {repr(test_text)}")
    print(f"清理后: {repr(cleaned)}")
    
    if cleaned and len(cleaned) > 0:
        print("✅ 文本字段清理成功")
        return True
    else:
        print("❌ 文本字段清理失败")
        return False

def test_problematic_rows():
    """测试问题行的解析"""
    print("🧪 测试问题行解析...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        # 读取CSV文件
        df = pd.read_csv(
            csv_file,
            encoding='utf-8',
            quoting=csv.QUOTE_ALL,
            na_filter=False,
            dtype=str
        )
        
        # 创建导入器实例
        importer = CSVToMongoImporter(csv_file, test_mode=True)
        
        # 测试前10行
        success_count = 0
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            row_dict = row.to_dict()
            
            try:
                doc = importer.convert_csv_row_to_document(row_dict)
                if doc:
                    success_count += 1
                    print(f"✅ 行 {i+1} 解析成功 - ID: {doc.get('id')}, Title: {doc.get('title', '')[:30]}...")
                else:
                    print(f"❌ 行 {i+1} 解析失败")
            except Exception as e:
                print(f"❌ 行 {i+1} 解析异常: {e}")
        
        print(f"\n📊 测试结果: {success_count}/10 行解析成功")
        return success_count == 10
        
    except Exception as e:
        print(f"❌ 测试问题行失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始增强型CSV解析器测试")
    print("=" * 60)
    
    tests = [
        ("pandas CSV读取", test_pandas_csv_reading),
        ("JSON字段解析", test_json_field_parsing),
        ("文本字段清理", test_text_field_cleaning),
        ("问题行解析", test_problematic_rows),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强型CSV解析器工作正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
