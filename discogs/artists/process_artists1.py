#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
from pymongo import MongoClient
from datetime import datetime
import re
import sys
import os
import gzip
import glob
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source, DeleteStatus
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status, Source, DeleteStatus

def find_xml_file(module_type):
    """
    从 deploy/windows/data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    data_dir = 'deploy/windows/data'
    pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    found_files = glob.glob(pattern)

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

# 配置参数
XML_FILE = find_xml_file('artists')

if not XML_FILE:
    print("❌ 错误: 无法找到 artists 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_artists.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '1000'))  # 最大处理记录数，设置为0表示处理全部数据

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')


# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    client = MongoClient(MONGO_URI)
    return client, client[DB_NAME]

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None


def extract_namevariations(content):
    """提取namevariations字段"""
    namevariations = []
    namevariations_match = re.search(r'<namevariations>(.*?)</namevariations>', content, re.DOTALL)
    if not namevariations_match:
        return namevariations

    namevariations_content = namevariations_match.group(1)
    extracted_variations = re.findall(r'<name>(.*?)</name>', namevariations_content, re.DOTALL)
    # 清理名称变体，去除前后空白字符
    return [variation.strip() for variation in extracted_variations if variation.strip()]

def extract_aliases(content):
    """提取aliases字段"""
    aliases = []
    aliases_match = re.search(r'<aliases>(.*?)</aliases>', content, re.DOTALL)
    if not aliases_match:
        return aliases

    aliases_content = aliases_match.group(1)

    # 使用正则表达式提取aliases
    alias_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    alias_matches = re.findall(alias_pattern, aliases_content)

    if alias_matches:
        for alias_id, alias_text in alias_matches:
            clean_text = alias_text.strip()
            if clean_text:  # 只添加非空的别名
                aliases.append({
                    'id': alias_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        alias_tags = aliases_content.split('</name>')
        for tag in alias_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        alias_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            alias_text = tag[text_start:].strip()
                            aliases.append({
                                'id': alias_id,
                                'name': alias_text
                            })

    return aliases

def extract_groups(content):
    """提取groups字段"""
    groups = []
    groups_match = re.search(r'<groups>(.*?)</groups>', content, re.DOTALL)
    if not groups_match:
        return groups

    groups_content = groups_match.group(1)

    # 使用正则表达式提取groups
    group_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    group_matches = re.findall(group_pattern, groups_content)

    if group_matches:
        for group_id, group_text in group_matches:
            clean_text = group_text.strip()
            if clean_text:  # 只添加非空的组合名称
                groups.append({
                    'id': group_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        group_tags = groups_content.split('</name>')
        for tag in group_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        group_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            group_text = tag[text_start:].strip()
                            groups.append({
                                'id': group_id,
                                'name': group_text
                            })
    return groups

def extract_members(content):
    """提取members字段"""
    members = []
    members_match = re.search(r'<members>(.*?)</members>', content, re.DOTALL)
    if not members_match:
        return members

    members_content = members_match.group(1)

    # 使用正则表达式提取members
    member_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    member_matches = re.findall(member_pattern, members_content)

    if member_matches:
        for member_id, member_text in member_matches:
            clean_text = member_text.strip()
            if clean_text:  # 只添加非空的成员名称
                members.append({
                    'id': member_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        member_tags = members_content.split('</name>')
        for tag in member_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        member_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            member_text = tag[text_start:].strip()
                            members.append({
                                'id': member_id,
                                'name': member_text
                            })

    return members

def extract_sites(content):
    """提取sites字段（从urls标签中获取所有url）"""
    sites = []
    urls_match = re.search(r'<urls>(.*?)</urls>', content, re.DOTALL)
    if not urls_match:
        return sites

    urls_content = urls_match.group(1)

    # 使用正则表达式提取所有url标签的内容
    url_pattern = r'<url>(.*?)</url>'
    url_matches = re.findall(url_pattern, urls_content, re.DOTALL)

    if url_matches:
        for url_text in url_matches:
            # 清理URL，去除前后空白字符并处理HTML实体
            clean_url = url_text.strip()
            if clean_url:  # 只添加非空的URL
                # 处理HTML实体转义
                clean_url = clean_url.replace('&amp;', '&')
                sites.append(clean_url)

    return sites

def get_artist_table_by_id(db, artist_id):
    """从artist表中获取images字段"""
    try:
        # 将artist_id转换为string类型进行查询
        artist_id_str = str(artist_id)
        artist_doc = db.artist.find_one({'id': artist_id_str})
        if artist_doc and 'images' in artist_doc:
            return artist_doc
            # //['images']
        return []
    except Exception as e:
        write_output(f"获取images失败 (artist_id: {artist_id}): {e}", False)
        return []

def process_artist_content(buffer, sequential_id, db):
    """处理单个artist标签的内容"""
    # 提取ID
    artist_id = extract_field(buffer, 'id')
    if not artist_id:
        return None

    old_release_doc = get_artist_table_by_id(db, artist_id)
    images = old_release_doc['images'];

    # 创建artist文档
    artist_doc = {
        'id': artist_id,
        'y_id': f"YA{sequential_id}",
        'name': extract_field(buffer, 'name'),
        'realname': extract_field(buffer, 'realname'),
        'images': images,
        'images_permissions': Permissions.ALL_VISIBLE.value,
        'profile': extract_field(buffer, 'profile'),
        'variations': extract_namevariations(buffer),
        'aliases': extract_aliases(buffer),
        'groups': extract_groups(buffer),
        'members': extract_members(buffer),
        'sites': extract_sites(buffer),
        'delete_status': DeleteStatus.NOT_DELETED.value,  # 逻辑删除状态，默认未删除
        'deleted_at': None,  # 为软删除功能准备
        'created_at': datetime.now(),
        'updated_at': datetime.now(),
        'source': Source.DISCOGS.value,
        'permissions': Permissions.ALL_VISIBLE.value,
    }

    return artist_doc

def process_artists():
    """处理XML文件中的artist记录"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()

    # 确保artists_new集合存在
    if 'artists_new' not in db.list_collection_names():
        db.create_collection('artists_new')

    # 获取集合
    artists_collection = db['artists']
    artists_new_collection = db['artists_new']

    # 清空artists_new集合
    artists_new_collection.delete_many({})

    processed_count = 0
    variations_count = 0
    aliases_count = 0
    groups_count = 0
    members_count = 0
    sites_count = 0
    total_records_found = 0

    try:
        # 打开gz压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_artist = False

            for line in f:
                if '<artist>' in line:
                    buffer = line
                    in_artist = True
                    total_records_found += 1
                elif '</artist>' in line and in_artist:
                    buffer += line
                    in_artist = False

                    # 处理artist内容，使用连续的序号作为y_id
                    sequential_id = processed_count + 1
                    artist_doc = process_artist_content(buffer, sequential_id, db)
                    if artist_doc:
                        # 查询数据库中是否存在该artist
                        existing_artist = artists_collection.find_one({'id': artist_doc['id']})

                        if existing_artist and '_id' in existing_artist:
                            # 保留原始_id
                            artist_doc['_id'] = existing_artist['_id']

                        # 更新或插入到artists_new集合
                        artists_new_collection.update_one(
                            {'id': artist_doc['id']},
                            {'$set': artist_doc},
                            upsert=True
                        )

                        processed_count += 1
                        variations_count += len(artist_doc['variations'])
                        aliases_count += len(artist_doc['aliases'])
                        groups_count += len(artist_doc['groups'])
                        members_count += len(artist_doc['members'])
                        sites_count += len(artist_doc['sites'])

                        # 显示进度
                        if processed_count % 10 == 0:
                            name_short = artist_doc['name'][:50] if artist_doc['name'] else "无名称"
                            log_message = (f"处理记录 {processed_count}: y_id={artist_doc['y_id']}, "
                                      f"id={artist_doc['id']}, name={name_short}...")
                            # 只写入文件，不打印到控制台
                            write_output(log_message, False)
                            # 打印到控制台
                            print(f"已处理 {processed_count} 条记录...")

                        # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                        if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_artist:
                    buffer += line
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"共处理了 {processed_count} 条记录",
            f"提取了 {variations_count} 个variations",
            f"提取了 {aliases_count} 个aliases",
            f"提取了 {groups_count} 个groups",
            f"提取了 {members_count} 个members",
            f"提取了 {sites_count} 个sites",
            f"XML文件中共发现 {total_records_found} 条记录"
        ]

        if MAX_RECORDS < total_records_found:
            stats.append(f"由于设置了最大处理记录数限制 (MAX_RECORDS={MAX_RECORDS})，只处理了部分记录")

        stats.extend([
            f"处理时长: {processing_time:.2f} 秒",
            f"平均每条记录处理时间: {processing_time/processed_count:.4f} 秒" if processed_count > 0 else "平均每条记录处理时间: 0.0000 秒",
            "="*50
        ])

        # 只打印到控制台，不写入文件
        for stat in stats:
            print(stat)

        # 关闭数据库连接
        client.close()

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")

# 确保只在直接运行脚本时执行process_artists函数
if __name__ == "__main__":
    process_artists()
