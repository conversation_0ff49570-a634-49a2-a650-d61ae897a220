#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import gzip
import csv
from pymongo import MongoClient
from datetime import datetime
import re
import os
from enums import Permissions # 从 enums.py 导入
from enums import Source
from enums import DeleteStatus

# 配置参数
XML_FILE = 'discogs_20250701_labels.xml.gz'
MONGO_URI = '**********************************************************'
DB_NAME = 'music_test'

# 目标ID列表 - 只处理这些ID
TARGET_IDS = ['1723', '1866', '2294']

# 输出文件路径
OUTPUT_FILE = 'process_output.txt'
CSV_OUTPUT_FILE = 'target_labels_output.csv'


# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True, log_level="INFO"):
    """将消息写入输出文件，并可选择是否打印到控制台

    Args:
        message: 要输出的消息
        print_to_console: 是否打印到控制台
        log_level: 日志级别 (DEBUG, INFO, WARN, ERROR)
    """
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] [{log_level}] {message}"

    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    client = MongoClient(MONGO_URI)
    return client, client[DB_NAME]

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def extract_sublabels(content, debug_mode=False, record_id=None):
    """提取sublabels字段，增强调试和错误处理"""
    sublabels = []

    try:
        # 查找sublabels标签
        sublabels_match = re.search(r'<sublabels>(.*?)</sublabels>', content, re.DOTALL)
        if not sublabels_match:
            if debug_mode and record_id:
                write_output(f"[DEBUG] 记录 {record_id}: 未找到sublabels标签", False)
            return sublabels

        sublabels_content = sublabels_match.group(1).strip()
        if debug_mode and record_id:
            write_output(f"[DEBUG] 记录 {record_id}: 找到sublabels内容，长度: {len(sublabels_content)}",
                        False)

        # 改进的正则表达式模式，支持有引号和无引号的id
        sublabel_pattern = r'<label\s+id=(?:"(\d+)"|(\d+))>(.*?)</label>'
        sublabel_matches = re.findall(sublabel_pattern, sublabels_content, re.DOTALL)

        if sublabel_matches:
            for match in sublabel_matches:
                # match是一个三元组：(quoted_id, unquoted_id, text)
                sublabel_id = match[0] if match[0] else match[1]
                sublabel_text = match[2].strip()

                if sublabel_id and sublabel_text:
                    sublabels.append({
                        'id': sublabel_id,
                        'text': sublabel_text
                    })

            if debug_mode and record_id:
                write_output(f"[DEBUG] 记录 {record_id}: 正则表达式成功提取 {len(sublabels)} 个sublabels",
                            False)
        else:
            # 备用方法：手动解析，增强错误处理
            if debug_mode and record_id:
                write_output(f"[DEBUG] 记录 {record_id}: 正则表达式失败，使用备用解析方法", False)

            sublabel_tags = sublabels_content.split('</label>')
            for i, tag in enumerate(sublabel_tags):
                if '<label id=' in tag:
                    try:
                        # 更健壮的id提取
                        id_patterns = [r'id="(\d+)"', r"id='(\d+)'", r'id=(\d+)']
                        sublabel_id = None

                        for pattern in id_patterns:
                            id_match = re.search(pattern, tag)
                            if id_match:
                                sublabel_id = id_match.group(1)
                                break

                        if sublabel_id:
                            # 提取文本内容
                            text_start = tag.find('>', tag.find('<label id=')) + 1
                            if text_start > 0:
                                sublabel_text = tag[text_start:].strip()
                                if sublabel_text:
                                    sublabels.append({
                                        'id': sublabel_id,
                                        'text': sublabel_text
                                    })

                    except Exception as e:
                        if debug_mode and record_id:
                            write_output(f"[DEBUG] 记录 {record_id}: 解析第 {i} 个sublabel时出错: {e}",
                                        False)
                        continue

            if debug_mode and record_id:
                write_output(f"[DEBUG] 记录 {record_id}: 备用方法提取 {len(sublabels)} 个sublabels", False)

    except Exception as e:
        error_msg = f"提取sublabels时出错 (记录 {record_id}): {e}"
        write_output(error_msg, False)
        if debug_mode:
            write_output(f"[DEBUG] sublabels内容片段: {content[:200]}...", False)

    return sublabels

def extract_parent_label(content):
    """提取parent label信息，包括id和name"""
    try:
        # 匹配 <parentLabel id="xxx">Label Name</parentLabel> 格式
        parent_label_pattern = r'<parentLabel id="?(\d+)"?>(.*?)</parentLabel>'
        match = re.search(parent_label_pattern, content, re.DOTALL)

        if match:
            parent_id = match.group(1)
            parent_name = match.group(2).strip()
            return {
                'id': parent_id,
                'name': parent_name
            }
        return None
    except Exception as e:
        # 记录解析错误但不中断处理
        print(f"解析parent_label时出错: {e}")
        return None
    
def extract_links(content):
    """提取sites字段（从urls标签中获取所有url）"""
    links = []
    urls_match = re.search(r'<urls>(.*?)</urls>', content, re.DOTALL)
    if not urls_match:
        return links

    urls_content = urls_match.group(1)

    # 使用正则表达式提取所有url标签的内容
    url_pattern = r'<url>(.*?)</url>'
    url_matches = re.findall(url_pattern, urls_content, re.DOTALL)

    if url_matches:
        for url_text in url_matches:
            # 清理URL，去除前后空白字符并处理HTML实体
            clean_url = url_text.strip()
            if clean_url:  # 只添加非空的URL
                # 处理HTML实体转义
                clean_url = clean_url.replace('&amp;', '&')
                links.append(clean_url)

    return links

def get_label_table_by_id(db, label_id):
    """从label表中获取images字段"""
    try:
        # 将artist_id转换为string类型进行查询
        label_id_str = str(label_id)
        label_doc = db.label.find_one({'id': label_id_str})
        if label_doc and 'images' in label_doc:
            return label_doc
            # //['images']
        return []
    except Exception as e:
        write_output(f"获取images失败 (label_id: {label_id}): {e}", False)
        return []

def write_to_csv(label_docs, csv_file):
    """将label数据写入CSV文件"""
    if not label_docs:
        write_output("没有数据需要写入CSV文件")
        return

    # 定义CSV字段
    fieldnames = [
        'id', 'y_id', 'name', 'profile', 'contactinfo',
        'links', 'sublabels', 'parent_label', 'images',
        'delete_status', 'created_at', 'source', 'permissions'
    ]

    try:
        with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for label_doc in label_docs:
                # 处理复杂字段，转换为字符串格式
                csv_row = {}
                for field in fieldnames:
                    if field in label_doc:
                        value = label_doc[field]
                        if isinstance(value, (list, dict)):
                            # 将列表和字典转换为字符串
                            csv_row[field] = str(value)
                        elif isinstance(value, datetime):
                            # 将日期时间转换为字符串
                            csv_row[field] = value.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            csv_row[field] = value
                    else:
                        csv_row[field] = ''

                writer.writerow(csv_row)

        write_output(f"成功将 {len(label_docs)} 条记录写入CSV文件: {csv_file}")

    except Exception as e:
        write_output(f"写入CSV文件时出错: {e}")
        raise

def process_label_content(buffer, sequential_id, debug_mode=False, db=None):
    """处理单个label标签的内容，增加数据验证"""
    # 提取ID
    label_id = extract_field(buffer, 'id')
    if not label_id:
        return None

    # 提取parent label信息
    parent_label_info = extract_parent_label(buffer)

    old_label_doc = get_label_table_by_id(db, label_id)
    images = old_label_doc['images']

    # 提取sublabels，启用调试模式用于前几条记录
    sublabels = extract_sublabels(buffer, debug_mode, label_id)

    # 数据验证：检查sublabels的完整性
    if sublabels:
        validated_sublabels = []
        for sublabel in sublabels:
            if isinstance(sublabel, dict) and 'id' in sublabel and 'text' in sublabel:
                if sublabel['id'] and sublabel['text'].strip():
                    validated_sublabels.append(sublabel)
                elif debug_mode:
                    write_output(f"[WARN] 记录 {label_id}: 发现无效sublabel: {sublabel}", False)
        sublabels = validated_sublabels

    # 创建label文档
    label_doc = {
        'id': label_id,
        'y_id': f"YL{sequential_id}",
        'name': extract_field(buffer, 'name'),
        'images': images,
        'profile': extract_field(buffer, 'profile'),
        'contactinfo': extract_field(buffer, 'contactinfo'),
        'links': extract_links(buffer),
        'sublabels': sublabels,
        'parent_label': parent_label_info,  # 完整的parent_label对象
        'delete_status': DeleteStatus.NOT_DELETED.value,  # 逻辑删除状态，默认未删除
        'deleted_at': None,  # 为软删除功能准备
        'created_at': datetime.now(),
        'updated_at': datetime.now(),
        'source': Source.DISCOGS.value,
        'permissions': Permissions.ALL_VISIBLE.value,
        # 'status': Status.ACTIVE.value,
    }

    return label_doc

def process_labels():
    """处理XML文件中的label记录，只处理指定ID并生成CSV"""
    start_time = time.time()

    # 连接MongoDB（仍需要获取images字段）
    client, db = connect_to_mongodb()

    # 存储找到的目标记录
    target_labels = []
    processed_count = 0
    sublabels_count = 0
    total_records_found = 0
    records_with_sublabels = 0
    target_ids_found = set()  # 跟踪已找到的目标ID

    try:
        write_output(f"开始处理XML文件，寻找目标ID: {TARGET_IDS}")

        # 打开gz压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False

            for line in f:
                if '<label>' in line:
                    buffer = line
                    in_label = True
                    total_records_found += 1
                elif '</label>' in line and in_label:
                    buffer += line
                    in_label = False

                    # 先提取ID检查是否为目标ID
                    label_id = extract_field(buffer, 'id')
                    if label_id and label_id in TARGET_IDS:
                        # 这是目标ID，进行处理
                        sequential_id = processed_count + 1
                        debug_mode = True  # 对目标记录启用调试模式
                        label_doc = process_label_content(buffer, sequential_id, debug_mode, db)

                        if label_doc:
                            target_labels.append(label_doc)
                            target_ids_found.add(label_id)
                            processed_count += 1
                            current_sublabels_count = len(label_doc['sublabels'])
                            sublabels_count += current_sublabels_count
                            if current_sublabels_count > 0:
                                records_with_sublabels += 1

                            # 显示详细信息
                            title_short = label_doc['name'][:50] if label_doc['name'] else "无标题"
                            sublabel_info = (f"包含{current_sublabels_count}个sublabels"
                                            if current_sublabels_count > 0 else "无sublabels")
                            detailed_log = (f"[找到目标] 记录 {processed_count}: y_id={label_doc['y_id']}, "
                                          f"id={label_doc['id']}, title={title_short}, "
                                          f"{sublabel_info}")
                            write_output(detailed_log)
                            print(detailed_log)

                            # 如果已找到所有目标ID，可以提前退出
                            if len(target_ids_found) == len(TARGET_IDS):
                                write_output("已找到所有目标ID，提前结束处理")
                                break

                    # 清空缓冲区
                    buffer = ""
                elif in_label:
                    buffer += line

                # 每处理10000条记录显示一次进度
                if total_records_found % 10000 == 0:
                    print(f"已扫描 {total_records_found} 条记录，找到 {len(target_ids_found)} 个目标ID...")

        # 处理完成后写入CSV
        if target_labels:
            write_to_csv(target_labels, CSV_OUTPUT_FILE)
        else:
            write_output("未找到任何目标ID的记录")
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time
        # 输出处理结果统计
        sublabel_percentage = ((records_with_sublabels / processed_count * 100)
                              if processed_count > 0 else 0)
        avg_sublabels = ((sublabels_count / records_with_sublabels)
                        if records_with_sublabels > 0 else 0)

        # 检查哪些目标ID未找到
        missing_ids = set(TARGET_IDS) - target_ids_found

        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"目标ID列表: {TARGET_IDS}",
            f"找到的ID: {list(target_ids_found)}",
            f"未找到的ID: {list(missing_ids) if missing_ids else '无'}",
            f"共处理了 {processed_count} 条目标记录",
            f"包含sublabels的记录数: {records_with_sublabels} ({sublabel_percentage:.1f}%)",
            f"提取了 {sublabels_count} 个sublabels",
            f"平均每个有sublabels的记录包含: {avg_sublabels:.1f} 个sublabels",
            f"XML文件中共扫描 {total_records_found} 条记录",
            f"CSV输出文件: {CSV_OUTPUT_FILE}",
            f"处理时长: {processing_time:.2f} 秒",
            (f"平均每条记录处理时间: {processing_time/processed_count:.4f} 秒"
             if processed_count > 0 else "平均每条记录处理时间: 0.0000 秒"),
            "="*50
        ]

        # 只打印到控制台，不写入文件
        for stat in stats:
            print(stat)

        # 关闭数据库连接
        client.close()

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")

# 确保只在直接运行脚本时执行process_labels函数
if __name__ == "__main__":
    process_labels()