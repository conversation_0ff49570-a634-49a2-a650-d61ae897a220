#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pymongo import MongoClient
from datetime import datetime

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def verify_parent_label_status():
    """验证数据库中parent_label字段的状态"""
    print("🔍 开始验证数据库中parent_label字段状态...")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    
    try:
        # 获取label_new集合
        collection = db.label_new
        
        # 统计总记录数
        total_count = collection.count_documents({})
        print(f"📊 总记录数: {total_count:,}")
        
        # 统计包含parent_label字段的记录数
        with_parent_label = collection.count_documents({'parent_label': {'$exists': True, '$ne': None}})
        print(f"🏷️ 包含parent_label字段的记录数: {with_parent_label:,}")
        
        # 统计缺失parent_label字段的记录数
        missing_parent_label = collection.count_documents({
            '$or': [
                {'parent_label': {'$exists': False}},
                {'parent_label': None}
            ]
        })
        print(f"❌ 缺失parent_label字段的记录数: {missing_parent_label:,}")
        
        # 显示一些包含parent_label的示例记录
        print("\n📋 包含parent_label的示例记录:")
        sample_records = collection.find(
            {'parent_label': {'$exists': True, '$ne': None}},
            {'id': 1, 'y_id': 1, 'name': 1, 'parent_label': 1}
        ).limit(5)
        
        for i, record in enumerate(sample_records, 1):
            parent_label = record.get('parent_label', {})
            parent_id = parent_label.get('id', 'N/A') if isinstance(parent_label, dict) else 'N/A'
            parent_name = parent_label.get('name', 'N/A') if isinstance(parent_label, dict) else 'N/A'
            print(f"  {i}. ID: {record.get('id')}, Y_ID: {record.get('y_id')}, "
                  f"Name: {record.get('name', 'N/A')[:30]}..., "
                  f"Parent: {parent_name[:30]}... (ID: {parent_id})")
        
        # 显示一些缺失parent_label的示例记录（如果有的话）
        if missing_parent_label > 0:
            print("\n❌ 缺失parent_label的示例记录:")
            missing_records = collection.find(
                {
                    '$or': [
                        {'parent_label': {'$exists': False}},
                        {'parent_label': None}
                    ]
                },
                {'id': 1, 'y_id': 1, 'name': 1}
            ).limit(5)
            
            for i, record in enumerate(missing_records, 1):
                print(f"  {i}. ID: {record.get('id')}, Y_ID: {record.get('y_id')}, "
                      f"Name: {record.get('name', 'N/A')[:30]}...")
        
        # 计算百分比
        if total_count > 0:
            percentage_with_parent = (with_parent_label / total_count) * 100
            percentage_missing = (missing_parent_label / total_count) * 100
            
            print(f"\n📈 统计摘要:")
            print(f"  - 包含parent_label的记录: {percentage_with_parent:.2f}%")
            print(f"  - 缺失parent_label的记录: {percentage_missing:.2f}%")
            
            if missing_parent_label == 0:
                print("\n✅ 所有记录都已包含parent_label字段，无需更新！")
            else:
                print(f"\n⚠️ 发现 {missing_parent_label:,} 条记录缺失parent_label字段，需要更新。")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
    
    finally:
        # 关闭数据库连接
        client.close()

if __name__ == "__main__":
    verify_parent_label_status()
