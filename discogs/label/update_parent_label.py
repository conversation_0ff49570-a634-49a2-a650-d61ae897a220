#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import gzip
import re
import os
from pymongo import MongoClient
from datetime import datetime
from enums import Permissions, Source, DeleteStatus

# 配置参数
XML_FILE = 'deploy/windows/data/discogs_20250701_labels.xml.gz'
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 输出文件路径
OUTPUT_FILE = 'parent_label_update_output.txt'

# 进度保存间隔
CHECKPOINT_INTERVAL = 1000

# 分批处理参数
BATCH_SIZE = 10000
PROGRESS_FILE = 'parent_label_progress.txt'

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True, log_level="INFO"):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] [{log_level}] {message}"

    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def extract_parent_label(content):
    """提取parent label信息，包括id和name"""
    try:
        # 匹配 <parentLabel id="xxx">Label Name</parentLabel> 格式
        parent_label_pattern = r'<parentLabel id="?(\d+)"?>(.*?)</parentLabel>'
        match = re.search(parent_label_pattern, content, re.DOTALL)

        if match:
            parent_id = match.group(1)
            parent_name = match.group(2).strip()
            return {
                'id': parent_id,
                'name': parent_name
            }
        return None
    except Exception as e:
        write_output(f"解析parent_label时出错: {e}", False, "WARN")
        return None

def extract_label_id_from_tag(line):
    """从label标签中提取ID"""
    # 匹配 <label id="123"> 格式
    pattern = r'<label\s+id="(\d+)"'
    match = re.search(pattern, line)
    return match.group(1) if match else None

def check_missing_parent_label(db, label_id):
    """检查数据库中的记录是否缺失parent_label字段"""
    try:
        # 查询label_new集合中的记录
        existing_record = db.label_new.find_one({'id': str(label_id)})
        if existing_record:
            # 检查是否缺失parent_label字段或字段为空
            return 'parent_label' not in existing_record or existing_record.get('parent_label') is None
        return False
    except Exception as e:
        write_output(f"检查记录失败 (label_id: {label_id}): {e}", False, "ERROR")
        return False

def save_progress(processed_count, updated_count, error_count):
    """保存处理进度"""
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            f.write(f"{processed_count},{updated_count},{error_count}")
    except Exception as e:
        write_output(f"保存进度失败: {e}", False, "ERROR")

def load_progress():
    """加载处理进度"""
    try:
        if os.path.exists(PROGRESS_FILE):
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                line = f.read().strip()
                if line:
                    parts = line.split(',')
                    if len(parts) == 3:
                        return int(parts[0]), int(parts[1]), int(parts[2])
        return 0, 0, 0
    except Exception as e:
        write_output(f"加载进度失败: {e}", False, "ERROR")
        return 0, 0, 0

def batch_update_parent_labels(db, update_batch):
    """批量更新parent_label字段"""
    if not update_batch:
        return 0, 0

    success_count = 0
    error_count = 0

    try:
        # 构建批量更新操作
        bulk_operations = []
        for label_id, parent_label_info in update_batch:
            bulk_operations.append(
                {
                    'updateOne': {
                        'filter': {'id': str(label_id)},
                        'update': {
                            '$set': {
                                'parent_label': parent_label_info,
                                'updated_at': datetime.now()
                            }
                        }
                    }
                }
            )

        # 执行批量更新
        if bulk_operations:
            result = db.label_new.bulk_write(bulk_operations, ordered=False)
            success_count = result.modified_count

            if success_count != len(update_batch):
                error_count = len(update_batch) - success_count
                write_output(f"批量更新部分失败: 成功 {success_count}, 失败 {error_count}", False, "WARN")

    except Exception as e:
        write_output(f"批量更新失败: {e}", False, "ERROR")
        error_count = len(update_batch)

    return success_count, error_count

def update_parent_label_in_db(db, label_id, parent_label_info):
    """更新数据库中记录的parent_label字段"""
    try:
        # 更新label_new集合中的记录
        result = db.label_new.update_one(
            {'id': str(label_id)},
            {
                '$set': {
                    'parent_label': parent_label_info,
                    'updated_at': datetime.now()
                }
            }
        )

        if result.modified_count > 0:
            return True
        else:
            write_output(f"未找到需要更新的记录 (label_id: {label_id})", False, "WARN")
            return False
    except Exception as e:
        write_output(f"更新记录失败 (label_id: {label_id}): {e}", False, "ERROR")
        return False

def process_parent_label_updates():
    """处理XML文件中的label记录，更新缺失的parent_label字段"""
    start_time = time.time()
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    
    # 加载之前的进度
    prev_processed, prev_updated, prev_errors = load_progress()

    # 统计变量
    total_records_processed = prev_processed
    records_with_parent_label = 0
    missing_parent_label_count = 0
    updated_count = prev_updated
    error_count = prev_errors

    write_output("🚀 开始处理parent_label字段更新任务")
    write_output(f"📁 XML文件: {XML_FILE}")
    write_output(f"🗄️ 数据库: {DB_NAME}")

    if prev_processed > 0:
        write_output(f"📊 恢复之前的进度: 已处理 {prev_processed:,} 条记录, 已更新 {prev_updated:,} 条记录")

    # 用于批量更新的缓存
    update_batch = []
    current_batch_size = 0
    
    try:
        # 检查XML文件是否存在
        if not os.path.exists(XML_FILE):
            write_output(f"❌ XML文件不存在: {XML_FILE}")
            return
        
        # 打开gz压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False
            
            write_output("📖 开始读取XML文件...")
            
            for line in f:
                # 检查是否包含完整的label记录（在同一行）
                if '<label' in line and 'id=' in line and '</label>' in line:
                    # 完整记录在同一行
                    buffer = line
                    total_records_processed += 1
                    
                    # 提取label ID
                    label_id = extract_label_id_from_tag(line)
                    
                    if label_id:
                        # 检查XML中是否包含parent_label信息
                        parent_label_info = extract_parent_label(buffer)
                        
                        if parent_label_info:
                            records_with_parent_label += 1
                            
                            # 检查数据库中是否缺失parent_label字段
                            if check_missing_parent_label(db, label_id):
                                missing_parent_label_count += 1
                                
                                # 更新数据库记录
                                if update_parent_label_in_db(db, label_id, parent_label_info):
                                    updated_count += 1
                                    if updated_count <= 10:  # 只显示前10个更新的记录
                                        write_output(f"✅ 更新成功 - ID: {label_id}, Parent: {parent_label_info['name'][:30]}...")
                                else:
                                    error_count += 1
                    
                    # 清空缓冲区
                    buffer = ""
                    
                else:
                    # 处理跨行的label记录
                    if '<label' in line and 'id=' in line:
                        buffer = line
                        in_label = True
                        total_records_processed += 1
                    elif '</label>' in line and in_label:
                        buffer += line
                        in_label = False
                        
                        # 提取label ID
                        label_id = extract_label_id_from_tag(buffer)
                        
                        if label_id:
                            # 检查XML中是否包含parent_label信息
                            parent_label_info = extract_parent_label(buffer)
                            
                            if parent_label_info:
                                records_with_parent_label += 1
                                
                                # 检查数据库中是否缺失parent_label字段
                                if check_missing_parent_label(db, label_id):
                                    missing_parent_label_count += 1
                                    
                                    # 更新数据库记录
                                    if update_parent_label_in_db(db, label_id, parent_label_info):
                                        updated_count += 1
                                        if updated_count <= 10:  # 只显示前10个更新的记录
                                            write_output(f"✅ 更新成功 - ID: {label_id}, Parent: {parent_label_info['name'][:30]}...")
                                    else:
                                        error_count += 1
                        
                        # 清空缓冲区
                        buffer = ""
                    elif in_label:
                        buffer += line
                
                # 显示进度
                if total_records_processed % CHECKPOINT_INTERVAL == 0:
                    elapsed_time = time.time() - start_time
                    write_output(f"📊 进度: 已处理 {total_records_processed:,} 条记录, "
                               f"发现parent_label: {records_with_parent_label:,}, "
                               f"需要更新: {missing_parent_label_count:,}, "
                               f"已更新: {updated_count:,}, "
                               f"耗时: {elapsed_time:.1f}秒")
    
    except Exception as e:
        write_output(f"❌ 处理过程中发生错误: {e}", True, "ERROR")
        raise
    
    finally:
        # 关闭数据库连接
        client.close()
        
        # 计算总耗时
        total_time = time.time() - start_time
        
        # 输出最终统计
        write_output("\n" + "="*60)
        write_output("📈 Parent Label 更新任务完成统计")
        write_output("="*60)
        write_output(f"📊 总处理记录数: {total_records_processed:,}")
        write_output(f"🏷️ 包含parent_label的记录: {records_with_parent_label:,}")
        write_output(f"🔍 数据库中缺失parent_label的记录: {missing_parent_label_count:,}")
        write_output(f"✅ 成功更新的记录: {updated_count:,}")
        write_output(f"❌ 更新失败的记录: {error_count:,}")
        write_output(f"⏱️ 总耗时: {total_time:.2f} 秒")
        write_output(f"🚀 平均处理速度: {total_records_processed/total_time:.1f} 记录/秒")
        
        if updated_count > 0:
            write_output(f"🎉 成功为 {updated_count:,} 条记录补充了parent_label字段！")
        else:
            write_output("ℹ️ 没有发现需要更新的记录")

if __name__ == "__main__":
    process_parent_label_updates()
