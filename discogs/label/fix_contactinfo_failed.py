#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import gzip
import re
import os
import html
from pymongo import MongoClient, UpdateOne
from datetime import datetime
import random

# 配置参数
XML_FILE = 'deploy/windows/data/discogs_20250701_labels.xml.gz'
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 输出文件路径
OUTPUT_FILE = 'contactinfo_fix_output.txt'

# 分批处理参数（减小批量大小以提高稳定性）
BATCH_SIZE = 1000
CHECKPOINT_INTERVAL = 5000
PROGRESS_FILE = 'contactinfo_fix_progress.txt'

# 重试配置
MAX_RETRIES = 5
RETRY_DELAY_BASE = 2  # 基础延迟秒数
MAX_RETRY_DELAY = 60  # 最大延迟秒数

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True, log_level="INFO"):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] [{log_level}] {message}"

    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

    if print_to_console:
        print(formatted_message)

def connect_to_mongodb_with_retry():
    """连接到MongoDB并返回数据库对象，包含重试机制"""
    for attempt in range(MAX_RETRIES):
        try:
            client = MongoClient(MONGO_URI, 
                               serverSelectionTimeoutMS=30000,
                               socketTimeoutMS=30000,
                               connectTimeoutMS=30000,
                               maxPoolSize=10,
                               retryWrites=True)
            # 测试连接
            client.admin.command('ping')
            write_output(f"✅ MongoDB连接成功 (尝试 {attempt + 1}/{MAX_RETRIES})")
            return client, client[DB_NAME]
        except Exception as e:
            delay = min(RETRY_DELAY_BASE * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
            write_output(f"❌ MongoDB连接失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
            if attempt < MAX_RETRIES - 1:
                write_output(f"⏳ 等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
            else:
                write_output("❌ 所有连接尝试都失败了")
                raise

def extract_contactinfo(content):
    """提取contactinfo信息"""
    try:
        # 匹配 <contactinfo>联系信息内容</contactinfo> 格式
        contactinfo_pattern = r'<contactinfo>(.*?)</contactinfo>'
        match = re.search(contactinfo_pattern, content, re.DOTALL)

        if match:
            contactinfo_content = match.group(1)
            
            # 处理HTML实体编码
            # 首先处理&#13;（回车符）为换行符
            contactinfo_content = contactinfo_content.replace('&#13;', '\n')
            
            # 处理其他HTML实体编码
            contactinfo_content = html.unescape(contactinfo_content)
            
            # 清理多余的空白字符，但保留换行符
            contactinfo_content = contactinfo_content.strip()
            
            return contactinfo_content
        return None
    except Exception as e:
        write_output(f"解析contactinfo时出错: {e}", False, "WARN")
        return None

def extract_label_id_from_tag(line):
    """从label标签中提取ID"""
    # 匹配 <label><id>123</id> 或 <label id="123"> 格式
    pattern1 = r'<label><id>(\d+)</id>'
    pattern2 = r'<label\s+id=["\']?(\d+)["\']?'
    
    match = re.search(pattern1, line)
    if match:
        return match.group(1)
    
    match = re.search(pattern2, line)
    if match:
        return match.group(1)
    
    return None

def get_failed_records(db):
    """获取contactinfo字段为null或不存在的记录ID列表"""
    try:
        write_output("🔍 查询需要修复的记录...")

        # 先查询不存在contactinfo字段的记录
        write_output("🔍 查询不存在contactinfo字段的记录...")
        query1 = {'contactinfo': {'$exists': False}}
        failed_records_1 = list(db.label_new.find(query1, {'id': 1, '_id': 0}).limit(100000))

        # 再查询contactinfo字段为null的记录
        write_output("🔍 查询contactinfo字段为null的记录...")
        query2 = {'contactinfo': None}
        failed_records_2 = list(db.label_new.find(query2, {'id': 1, '_id': 0}).limit(100000))

        # 合并结果
        all_failed_records = failed_records_1 + failed_records_2
        failed_ids = [record['id'] for record in all_failed_records]

        # 去重
        failed_ids = list(set(failed_ids))

        write_output(f"📊 发现需要修复的记录数: {len(failed_ids):,}")
        write_output(f"📊 其中不存在字段的记录: {len(failed_records_1):,}")
        write_output(f"📊 其中字段为null的记录: {len(failed_records_2):,}")

        return failed_ids

    except Exception as e:
        write_output(f"❌ 查询失败记录时出错: {e}", True, "ERROR")
        return []

def save_progress(processed_count, with_contactinfo_count, without_contactinfo_count, error_count):
    """保存处理进度"""
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            f.write(f"{processed_count},{with_contactinfo_count},{without_contactinfo_count},{error_count}")
    except Exception as e:
        write_output(f"保存进度失败: {e}", False, "ERROR")

def load_progress():
    """加载处理进度"""
    try:
        if os.path.exists(PROGRESS_FILE):
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                line = f.read().strip()
                if line:
                    parts = line.split(',')
                    if len(parts) == 4:
                        return int(parts[0]), int(parts[1]), int(parts[2]), int(parts[3])
        return 0, 0, 0, 0
    except Exception as e:
        write_output(f"加载进度失败: {e}", False, "ERROR")
        return 0, 0, 0, 0

def batch_update_contactinfo_with_retry(db, update_batch):
    """批量更新contactinfo字段，包含重试机制"""
    if not update_batch:
        return 0, 0

    for attempt in range(MAX_RETRIES):
        try:
            # 构建批量更新操作
            bulk_operations = []
            for label_id, contactinfo_content in update_batch:
                bulk_operations.append(
                    UpdateOne(
                        {'id': str(label_id)},
                        {
                            '$set': {
                                'contactinfo': contactinfo_content,
                                'updated_at': datetime.now()
                            }
                        }
                    )
                )

            # 执行批量更新
            if bulk_operations:
                result = db.label_new.bulk_write(bulk_operations, ordered=False)
                success_count = result.modified_count
                error_count = len(update_batch) - success_count
                
                if attempt > 0:
                    write_output(f"✅ 重试成功: 批量更新 {success_count} 条记录 (尝试 {attempt + 1})")
                
                return success_count, error_count

        except Exception as e:
            delay = min(RETRY_DELAY_BASE * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
            write_output(f"❌ 批量更新失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
            
            if attempt < MAX_RETRIES - 1:
                write_output(f"⏳ 等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
                
                # 重新连接数据库
                try:
                    client, db = connect_to_mongodb_with_retry()
                except Exception as conn_e:
                    write_output(f"❌ 重新连接失败: {conn_e}")
                    continue
            else:
                write_output(f"❌ 批量更新最终失败，跳过这批记录")
                return 0, len(update_batch)

    return 0, len(update_batch)

def create_id_to_xml_mapping(failed_ids):
    """创建ID到XML内容的映射"""
    write_output("📖 创建ID到XML内容的映射...")
    id_to_xml = {}
    
    try:
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False
            processed_count = 0
            
            for line in f:
                # 检查是否包含完整的label记录（在同一行）
                if '<label>' in line and '</label>' in line:
                    processed_count += 1
                    label_id = extract_label_id_from_tag(line)
                    
                    if label_id and label_id in failed_ids:
                        id_to_xml[label_id] = line
                
                # 处理跨行的label记录
                elif '<label>' in line and '</label>' not in line:
                    buffer = line
                    in_label = True
                elif '</label>' in line and in_label:
                    buffer += line
                    in_label = False
                    processed_count += 1
                    
                    label_id = extract_label_id_from_tag(buffer)
                    if label_id and label_id in failed_ids:
                        id_to_xml[label_id] = buffer
                    
                    buffer = ""
                elif in_label:
                    buffer += line
                
                # 显示进度
                if processed_count % 100000 == 0:
                    write_output(f"📊 已扫描 {processed_count:,} 条XML记录，找到 {len(id_to_xml):,} 条需要修复的记录")
    
    except Exception as e:
        write_output(f"❌ 创建映射时出错: {e}", True, "ERROR")
    
    write_output(f"✅ 映射创建完成，共找到 {len(id_to_xml):,} 条记录的XML内容")
    return id_to_xml

def fix_failed_contactinfo():
    """修复失败的contactinfo记录"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb_with_retry()

    # 获取需要修复的记录ID列表
    failed_ids = get_failed_records(db)
    
    if not failed_ids:
        write_output("✅ 没有需要修复的记录")
        return
    
    # 转换为set以提高查找效率
    failed_ids_set = set(failed_ids)
    
    # 创建ID到XML内容的映射
    id_to_xml = create_id_to_xml_mapping(failed_ids_set)
    
    # 加载之前的进度
    prev_processed, prev_with_contactinfo, prev_without_contactinfo, prev_errors = load_progress()

    # 统计变量
    total_records_processed = prev_processed
    records_with_contactinfo = prev_with_contactinfo
    records_without_contactinfo = prev_without_contactinfo
    error_count = prev_errors

    write_output("🚀 开始修复失败的contactinfo记录")
    write_output(f"📊 需要修复的记录总数: {len(failed_ids):,}")

    if prev_processed > 0:
        write_output(f"📊 恢复之前的进度: 已处理 {prev_processed:,} 条记录")

    # 用于批量更新的缓存
    update_batch = []
    
    # 跳过已处理的记录
    remaining_ids = failed_ids[prev_processed:]

    try:
        for i, label_id in enumerate(remaining_ids):
            total_records_processed += 1
            
            # 从映射中获取XML内容
            xml_content = id_to_xml.get(label_id)
            
            if xml_content:
                # 提取contactinfo信息
                contactinfo_content = extract_contactinfo(xml_content)
                
                if contactinfo_content:
                    # 有contactinfo信息
                    records_with_contactinfo += 1
                    update_batch.append((label_id, contactinfo_content))
                else:
                    # 没有contactinfo信息，设置为空字符串
                    records_without_contactinfo += 1
                    update_batch.append((label_id, ""))
            else:
                # 没有找到XML内容，设置为空字符串
                records_without_contactinfo += 1
                update_batch.append((label_id, ""))
            
            # 当批次达到指定大小时执行批量更新
            if len(update_batch) >= BATCH_SIZE:
                batch_success, batch_errors = batch_update_contactinfo_with_retry(db, update_batch)
                if batch_success > 0:
                    write_output(f"✅ 批量更新成功: {batch_success} 条记录")
                if batch_errors > 0:
                    error_count += batch_errors
                    write_output(f"❌ 批量更新失败: {batch_errors} 条记录")
                
                # 清空批次
                update_batch = []
            
            # 显示进度并保存检查点
            if total_records_processed % CHECKPOINT_INTERVAL == 0:
                elapsed_time = time.time() - start_time
                write_output(f"📊 修复进度: 已处理 {total_records_processed:,}/{len(failed_ids):,} 条记录 "
                           f"({total_records_processed/len(failed_ids)*100:.1f}%), "
                           f"有contactinfo: {records_with_contactinfo:,}, "
                           f"无contactinfo: {records_without_contactinfo:,}, "
                           f"错误: {error_count:,}, "
                           f"耗时: {elapsed_time:.1f}秒")

                # 保存进度
                save_progress(total_records_processed, records_with_contactinfo, 
                            records_without_contactinfo, error_count)

        # 处理剩余的批次
        if update_batch:
            batch_success, batch_errors = batch_update_contactinfo_with_retry(db, update_batch)
            if batch_success > 0:
                write_output(f"✅ 最终批量更新成功: {batch_success} 条记录")
            if batch_errors > 0:
                error_count += batch_errors
                write_output(f"❌ 最终批量更新失败: {batch_errors} 条记录")

    except Exception as e:
        write_output(f"❌ 修复过程中发生错误: {e}", True, "ERROR")
        raise

    finally:
        # 关闭数据库连接
        client.close()

        # 计算总耗时
        total_time = time.time() - start_time

        # 保存最终进度
        save_progress(total_records_processed, records_with_contactinfo, 
                     records_without_contactinfo, error_count)

        # 输出最终统计
        write_output("\n" + "="*60)
        write_output("📈 Contactinfo 修复任务统计")
        write_output("="*60)
        write_output(f"📊 需要修复的记录数: {len(failed_ids):,}")
        write_output(f"📊 实际处理记录数: {total_records_processed:,}")
        write_output(f"📞 包含contactinfo的记录: {records_with_contactinfo:,}")
        write_output(f"📝 设置为空字符串的记录: {records_without_contactinfo:,}")
        write_output(f"❌ 处理失败的记录: {error_count:,}")
        write_output(f"⏱️ 总耗时: {total_time:.2f} 秒")
        
        if total_time > 0:
            write_output(f"🚀 平均处理速度: {total_records_processed/total_time:.1f} 记录/秒")

        success_count = records_with_contactinfo + records_without_contactinfo
        if success_count > 0:
            write_output(f"🎉 成功修复 {success_count:,} 条记录的contactinfo字段！")
            write_output(f"✅ 修复成功率: {success_count/len(failed_ids)*100:.1f}%")
        else:
            write_output("ℹ️ 没有记录被成功修复")

if __name__ == "__main__":
    fix_failed_contactinfo()
