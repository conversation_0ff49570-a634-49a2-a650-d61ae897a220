#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import gzip
import re
import os
import psutil
from pymongo import MongoClient, UpdateOne
from datetime import datetime
import random

# 配置参数
XML_FILE = 'deploy/windows/data/discogs_20250701_labels.xml.gz'
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 输出文件路径
OUTPUT_FILE = 'links_resume_output.txt'

# 分批处理参数（减小批量大小以提高稳定性）
BATCH_SIZE = 2000
CHECKPOINT_INTERVAL = 5000
PROGRESS_FILE = 'links_resume_progress.txt'

# 重试配置
MAX_RETRIES = 5
RETRY_DELAY_BASE = 2
MAX_RETRY_DELAY = 60

# 内存监控配置
MEMORY_THRESHOLD = 80  # 内存使用率阈值（百分比）

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True, log_level="INFO"):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] [{log_level}] {message}"

    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

    if print_to_console:
        print(formatted_message)

def check_memory_usage():
    """检查内存使用情况"""
    memory_percent = psutil.virtual_memory().percent
    if memory_percent > MEMORY_THRESHOLD:
        write_output(f"⚠️ 内存使用率过高: {memory_percent:.1f}%", True, "WARN")
        return False
    return True

def connect_to_mongodb_with_retry():
    """连接到MongoDB并返回数据库对象，包含重试机制"""
    for attempt in range(MAX_RETRIES):
        try:
            client = MongoClient(MONGO_URI, 
                               serverSelectionTimeoutMS=30000,
                               socketTimeoutMS=30000,
                               connectTimeoutMS=30000,
                               maxPoolSize=10,
                               retryWrites=True)
            # 测试连接
            client.admin.command('ping')
            write_output(f"✅ MongoDB连接成功 (尝试 {attempt + 1}/{MAX_RETRIES})")
            return client, client[DB_NAME]
        except Exception as e:
            delay = min(RETRY_DELAY_BASE * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
            write_output(f"❌ MongoDB连接失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
            if attempt < MAX_RETRIES - 1:
                write_output(f"⏳ 等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
            else:
                write_output("❌ 所有连接尝试都失败了")
                raise

def extract_links(content):
    """提取links字段（从urls标签中获取所有url）"""
    links = []
    urls_match = re.search(r'<urls>(.*?)</urls>', content, re.DOTALL)
    if not urls_match:
        return links

    urls_content = urls_match.group(1)

    # 使用正则表达式提取所有url标签的内容
    url_pattern = r'<url>(.*?)</url>'
    url_matches = re.findall(url_pattern, urls_content, re.DOTALL)

    if url_matches:
        for url_text in url_matches:
            # 清理URL，去除前后空白字符并处理HTML实体
            clean_url = url_text.strip()
            if clean_url:  # 只添加非空的URL
                # 处理HTML实体转义
                clean_url = clean_url.replace('&amp;', '&')
                links.append(clean_url)

    return links

def extract_label_id_from_tag(line):
    """从label标签中提取ID"""
    # 匹配 <label><id>123</id> 或 <label id="123"> 格式
    pattern1 = r'<label><id>(\d+)</id>'
    pattern2 = r'<label\s+id=["\']?(\d+)["\']?'

    match = re.search(pattern1, line)
    if match:
        return match.group(1)

    match = re.search(pattern2, line)
    if match:
        return match.group(1)

    return None

def get_processed_count(db):
    """获取已处理的记录数量"""
    try:
        processed_count = db.label_new.count_documents({'links': {'$exists': True}})
        write_output(f"📊 数据库中已处理的记录数: {processed_count:,}")
        return processed_count
    except Exception as e:
        write_output(f"❌ 查询已处理记录数时出错: {e}", True, "ERROR")
        return 0

def save_progress(processed_count, with_links_count, without_links_count, error_count):
    """保存处理进度"""
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            f.write(f"{processed_count},{with_links_count},{without_links_count},{error_count}")
    except Exception as e:
        write_output(f"保存进度失败: {e}", False, "ERROR")

def load_progress():
    """加载处理进度"""
    try:
        if os.path.exists(PROGRESS_FILE):
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                line = f.read().strip()
                if line:
                    parts = line.split(',')
                    if len(parts) == 4:
                        return int(parts[0]), int(parts[1]), int(parts[2]), int(parts[3])
        return 0, 0, 0, 0
    except Exception as e:
        write_output(f"加载进度失败: {e}", False, "ERROR")
        return 0, 0, 0, 0

def batch_update_links_with_retry(db, update_batch):
    """批量更新links字段，包含重试机制"""
    if not update_batch:
        return 0, 0

    for attempt in range(MAX_RETRIES):
        try:
            # 构建批量更新操作
            bulk_operations = []
            for label_id, links_array in update_batch:
                bulk_operations.append(
                    UpdateOne(
                        {'id': str(label_id)},
                        {
                            '$set': {
                                'links': links_array,
                                'updated_at': datetime.now()
                            }
                        }
                    )
                )

            # 执行批量更新
            if bulk_operations:
                result = db.label_new.bulk_write(bulk_operations, ordered=False)
                success_count = result.modified_count
                error_count = len(update_batch) - success_count
                
                if attempt > 0:
                    write_output(f"✅ 重试成功: 批量更新 {success_count} 条记录 (尝试 {attempt + 1})")
                
                return success_count, error_count

        except Exception as e:
            delay = min(RETRY_DELAY_BASE * (2 ** attempt) + random.uniform(0, 1), MAX_RETRY_DELAY)
            write_output(f"❌ 批量更新失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}")
            
            if attempt < MAX_RETRIES - 1:
                write_output(f"⏳ 等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)
                
                # 重新连接数据库
                try:
                    client, db = connect_to_mongodb_with_retry()
                except Exception as conn_e:
                    write_output(f"❌ 重新连接失败: {conn_e}")
                    continue
            else:
                write_output(f"❌ 批量更新最终失败，跳过这批记录")
                return 0, len(update_batch)

    return 0, len(update_batch)

def resume_links_processing():
    """从中断点继续处理links字段"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb_with_retry()

    # 获取数据库中已处理的记录数
    db_processed_count = get_processed_count(db)
    
    # 加载之前的进度文件
    file_processed, file_with_links, file_without_links, file_errors = load_progress()
    
    # 使用数据库中的实际处理数量作为起始点
    skip_count = db_processed_count
    
    # 统计变量
    total_records_processed = 0
    records_with_links = 0
    records_without_links = 0
    error_count = 0

    write_output("🚀 开始从中断点继续处理links字段")
    write_output(f"📁 XML文件: {XML_FILE}")
    write_output(f"🗄️ 数据库: {DB_NAME}")
    write_output(f"📊 数据库中已处理记录: {db_processed_count:,}")
    write_output(f"📊 将跳过前 {skip_count:,} 条记录")

    # 用于批量更新的缓存
    update_batch = []

    try:
        # 检查XML文件是否存在
        if not os.path.exists(XML_FILE):
            write_output(f"❌ XML文件不存在: {XML_FILE}")
            return

        # 打开gz压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False
            xml_record_count = 0

            write_output("📖 开始读取XML文件...")

            for line in f:
                # 检查内存使用情况
                if xml_record_count % 10000 == 0:
                    if not check_memory_usage():
                        write_output("⚠️ 内存使用率过高，暂停处理")
                        time.sleep(5)

                # 检查是否包含完整的label记录（在同一行）
                if '<label>' in line and '</label>' in line:
                    xml_record_count += 1
                    
                    # 如果还在跳过阶段，继续跳过
                    if skip_count > 0:
                        skip_count -= 1
                        continue
                    
                    total_records_processed += 1
                    
                    # 提取label ID
                    label_id = extract_label_id_from_tag(line)
                    
                    if label_id:
                        # 检查XML中是否包含links信息
                        links_array = extract_links(line)
                        
                        if links_array:
                            # 有links信息
                            records_with_links += 1
                            update_batch.append((label_id, links_array))
                        else:
                            # 没有links信息，设置为空数组
                            records_without_links += 1
                            update_batch.append((label_id, []))
                        
                        # 当批次达到指定大小时执行批量更新
                        if len(update_batch) >= BATCH_SIZE:
                            batch_success, batch_errors = batch_update_links_with_retry(db, update_batch)
                            if batch_success > 0:
                                write_output(f"✅ 批量更新成功: {batch_success} 条记录")
                            if batch_errors > 0:
                                error_count += batch_errors
                                write_output(f"❌ 批量更新失败: {batch_errors} 条记录")
                            
                            # 清空批次
                            update_batch = []

                # 处理跨行的label记录（如果有的话）
                elif '<label>' in line and '</label>' not in line:
                    buffer = line
                    in_label = True
                    
                    # 如果还在跳过阶段，继续跳过
                    if skip_count > 0:
                        skip_count -= 1
                        in_label = False
                        buffer = ""
                        xml_record_count += 1
                        continue

                elif '</label>' in line and in_label:
                    buffer += line
                    in_label = False
                    xml_record_count += 1
                    
                    # 如果还在跳过阶段，继续跳过
                    if skip_count > 0:
                        skip_count -= 1
                        buffer = ""
                        continue
                    
                    total_records_processed += 1
                    
                    # 提取label ID
                    label_id = extract_label_id_from_tag(buffer)
                    
                    if label_id:
                        # 检查XML中是否包含links信息
                        links_array = extract_links(buffer)
                        
                        if links_array:
                            # 有links信息
                            records_with_links += 1
                            update_batch.append((label_id, links_array))
                        else:
                            # 没有links信息，设置为空数组
                            records_without_links += 1
                            update_batch.append((label_id, []))
                        
                        # 当批次达到指定大小时执行批量更新
                        if len(update_batch) >= BATCH_SIZE:
                            batch_success, batch_errors = batch_update_links_with_retry(db, update_batch)
                            if batch_success > 0:
                                write_output(f"✅ 批量更新成功: {batch_success} 条记录")
                            if batch_errors > 0:
                                error_count += batch_errors
                                write_output(f"❌ 批量更新失败: {batch_errors} 条记录")
                            
                            # 清空批次
                            update_batch = []
                    
                    # 清空缓冲区
                    buffer = ""
                    
                elif in_label:
                    buffer += line

                # 显示进度并保存检查点
                if total_records_processed % CHECKPOINT_INTERVAL == 0 and total_records_processed > 0:
                    elapsed_time = time.time() - start_time
                    current_total = db_processed_count + total_records_processed
                    write_output(f"📊 进度: 已处理 {current_total:,}/2,261,324 条记录 "
                               f"({current_total/2261324*100:.1f}%), "
                               f"本次新增: {total_records_processed:,}, "
                               f"有links: {records_with_links:,}, "
                               f"无links: {records_without_links:,}, "
                               f"错误: {error_count:,}, "
                               f"耗时: {elapsed_time:.1f}秒")

                    # 保存进度
                    save_progress(total_records_processed, records_with_links, 
                                records_without_links, error_count)

            # 处理剩余的批次
            if update_batch:
                batch_success, batch_errors = batch_update_links_with_retry(db, update_batch)
                if batch_success > 0:
                    write_output(f"✅ 最终批量更新成功: {batch_success} 条记录")
                if batch_errors > 0:
                    error_count += batch_errors
                    write_output(f"❌ 最终批量更新失败: {batch_errors} 条记录")

    except Exception as e:
        write_output(f"❌ 处理过程中发生错误: {e}", True, "ERROR")
        raise

    finally:
        # 关闭数据库连接
        client.close()

        # 计算总耗时
        total_time = time.time() - start_time

        # 保存最终进度
        save_progress(total_records_processed, records_with_links, 
                     records_without_links, error_count)

        # 输出最终统计
        write_output("\n" + "="*60)
        write_output("📈 Links 断点续传任务统计")
        write_output("="*60)
        write_output(f"📊 本次处理记录数: {total_records_processed:,}")
        write_output(f"🔗 包含links的记录: {records_with_links:,}")
        write_output(f"📝 设置为空数组的记录: {records_without_links:,}")
        write_output(f"❌ 处理失败的记录: {error_count:,}")
        write_output(f"⏱️ 总耗时: {total_time:.2f} 秒")
        
        if total_time > 0:
            write_output(f"🚀 平均处理速度: {total_records_processed/total_time:.1f} 记录/秒")

        final_total = db_processed_count + total_records_processed
        write_output(f"📊 累计处理记录数: {final_total:,}/2,261,324")
        write_output(f"📊 总体完成度: {final_total/2261324*100:.1f}%")
        
        if final_total >= 2261324:
            write_output(f"🎉 所有记录处理完成！")
        else:
            remaining = 2261324 - final_total
            write_output(f"⚠️ 还需处理 {remaining:,} 条记录")

if __name__ == "__main__":
    resume_links_processing()
