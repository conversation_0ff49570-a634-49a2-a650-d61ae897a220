#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def check_collections():
    """检查数据库中的集合"""
    print("🔍 检查数据库中的集合...")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    
    try:
        # 获取所有集合名称
        collections = db.list_collection_names()
        print(f"📊 数据库 '{DB_NAME}' 中的集合:")
        
        for collection_name in collections:
            collection = db[collection_name]
            count = collection.count_documents({})
            print(f"  - {collection_name}: {count:,} 条记录")
            
            # 如果是label相关的集合，显示一些示例记录
            if 'label' in collection_name.lower():
                print(f"    📋 {collection_name} 示例记录:")
                sample_records = collection.find({}).limit(3)
                for i, record in enumerate(sample_records, 1):
                    fields = list(record.keys())[:10]  # 显示前10个字段
                    print(f"      {i}. 字段: {fields}")
                    print(f"      {i}. ID: {record.get('id', 'N/A')}, Name: {record.get('name', 'N/A')[:30]}...")

                    # 检查是否有parent_label字段
                    if 'parent_label' in record:
                        parent_label = record['parent_label']
                        print(f"         parent_label: {parent_label}")
                    else:
                        print(f"         parent_label: 缺失")
                print()
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
    
    finally:
        # 关闭数据库连接
        client.close()

if __name__ == "__main__":
    check_collections()
