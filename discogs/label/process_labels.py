#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import glob
from pymongo import MongoClient
from datetime import datetime
import re
import sys
import os
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source, DeleteStatus
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status, Source, DeleteStatus

def find_xml_file(module_type):
    """
    从 data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 尝试多个可能的数据目录路径
    possible_dirs = ['data', 'deploy/windows/data', '../deploy/windows/data']

    for data_dir in possible_dirs:
        if os.path.exists(data_dir):
            pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
            found_files = glob.glob(pattern)
            if found_files:
                break
    else:
        found_files = []

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

# 配置参数
XML_FILE = find_xml_file('labels')

if not XML_FILE:
    print("❌ 错误: 无法找到 labels 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_labels.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，设置为0表示处理全部数据

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')

# 进度文件路径
PROGRESS_FILE = os.getenv('PROGRESS_FILE', 'progress/labels_progress.json')

# 断点续传配置
CHECKPOINT_INTERVAL = int(os.getenv('CHECKPOINT_INTERVAL', '1000'))  # 每处理多少条记录保存一次进度

def load_progress():
    """从进度文件加载上次的处理位置"""
    import json

    if not os.path.exists(PROGRESS_FILE):
        return 0  # 如果没有进度文件，从头开始

    try:
        with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
            resume_from = progress_data.get('resume_from_record', 0)
            print(f"📖 从进度文件读取：上次处理到第 {resume_from} 条记录")
            return resume_from
    except Exception as e:
        print(f"⚠️ 读取进度文件失败: {e}，从头开始处理")
        return 0

# 动态加载断点续传位置
RESUME_FROM_RECORD = load_progress()


# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

def save_progress(processed_count, total_records_found):
    """保存处理进度到文件"""
    import json
    import os

    # 确保progress目录存在
    progress_dir = os.path.dirname(PROGRESS_FILE)
    if progress_dir and not os.path.exists(progress_dir):
        os.makedirs(progress_dir)

    progress_data = {
        'processed_count': processed_count,
        'total_processed': RESUME_FROM_RECORD + processed_count,
        'total_records_found': total_records_found,
        'last_update': datetime.now().isoformat(),
        'resume_from_record': RESUME_FROM_RECORD + processed_count
    }

    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        write_output(f"保存进度失败: {e}", False)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def test_database_connectivity(db, debug_mode=True):
    """测试数据库连接和写入权限"""
    try:
        # 测试集合创建权限
        test_collection_name = 'labels_new'
        if test_collection_name not in db.list_collection_names():
            db.create_collection(test_collection_name)
            if debug_mode:
                print(f"✅ 成功创建集合: {test_collection_name}")
        else:
            if debug_mode:
                print(f"✅ 集合已存在: {test_collection_name}")

        # 测试写入权限
        test_doc = {
            'id': 'test_connectivity',
            'test_field': 'test_value',
            'created_at': datetime.now()
        }

        test_collection = db[test_collection_name]
        result = test_collection.insert_one(test_doc)

        if result.acknowledged and result.inserted_id:
            if debug_mode:
                print(f"✅ 数据库写入测试成功，inserted_id: {result.inserted_id}")

            # 清理测试数据
            test_collection.delete_one({'id': 'test_connectivity'})
            if debug_mode:
                print(f"✅ 测试数据已清理")

            return True
        else:
            print(f"❌ 数据库写入测试失败")
            return False

    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

def verify_database_operation(operation_result, operation_type, record_id, collection=None, debug_mode=False):
    """验证数据库操作是否成功"""
    try:
        success = False

        if operation_type == "update_one":
            if operation_result.acknowledged:
                if operation_result.upserted_id or operation_result.modified_count > 0:
                    success = True
                    if debug_mode:
                        write_output(f"✅ 更新操作成功 - ID: {record_id}, modified: {operation_result.modified_count}, upserted: {operation_result.upserted_id}", False)
                else:
                    write_output(f"⚠️ 数据库操作无效果 - ID: {record_id}", False)
            else:
                write_output(f"❌ 数据库操作未确认 - ID: {record_id}", False)
        elif operation_type == "insert_one":
            if operation_result.acknowledged and operation_result.inserted_id:
                success = True
                if debug_mode:
                    write_output(f"✅ 插入操作成功 - ID: {record_id}, inserted_id: {operation_result.inserted_id}", False)
            else:
                write_output(f"❌ 数据插入失败 - ID: {record_id}", False)

        # 额外验证：查询数据库确认记录确实存在
        if success and collection and debug_mode:
            try:
                verification_doc = collection.find_one({'id': record_id})
                if verification_doc:
                    write_output(f"✅ 数据库验证成功 - ID: {record_id} 确实存在于数据库中", False)
                else:
                    write_output(f"❌ 数据库验证失败 - ID: {record_id} 在数据库中未找到", False)
                    success = False
            except Exception as verify_e:
                write_output(f"⚠️ 数据库验证查询失败 - ID: {record_id}, 错误: {verify_e}", False)

        return success
    except Exception as e:
        write_output(f"❌ 验证数据库操作时出错 - ID: {record_id}, 错误: {e}", False)
        return False

def get_max_id_from_collection(collection):
    """获取集合中的最大ID值"""
    try:
        # 尝试将ID转换为数字进行排序，获取最大值
        pipeline = [
            {
                '$addFields': {
                    'id_numeric': {
                        '$toInt': '$id'
                    }
                }
            },
            {
                '$sort': {
                    'id_numeric': -1
                }
            },
            {
                '$limit': 1
            },
            {
                '$project': {
                    'id': 1
                }
            }
        ]

        result = list(collection.aggregate(pipeline))
        if result:
            max_id = result[0]['id']
            write_output(f"📊 数据库中最大ID: {max_id}", False)
            return int(max_id) if max_id else 0
        else:
            write_output(f"📊 数据库为空，最大ID设为0", False)
            return 0
    except Exception as e:
        write_output(f"⚠️ 获取最大ID失败，使用保守策略: {e}", False)
        return float('inf')  # 如果获取失败，使用保守策略（总是update）

def smart_database_operation(collection, document, max_id_in_db):
    """智能选择数据库操作策略"""
    try:
        current_id = int(document['id'])

        # 如果当前ID大于数据库中的最大ID，尝试直接插入
        if current_id > max_id_in_db:
            try:
                result = collection.insert_one(document)
                return result, "insert_one"
            except Exception as insert_error:
                # 插入失败（可能是重复键），降级为update操作
                if "duplicate key" in str(insert_error).lower() or "11000" in str(insert_error):
                    write_output(f"🔄 ID {current_id} 插入失败，降级为更新操作", False)
                    result = collection.update_one(
                        {'id': document['id']},
                        {'$set': document},
                        upsert=True
                    )
                    return result, "update_one"
                else:
                    raise insert_error
        else:
            # 当前ID小于等于最大ID，直接使用update操作
            result = collection.update_one(
                {'id': document['id']},
                {'$set': document},
                upsert=True
            )
            return result, "update_one"

    except ValueError:
        # ID不是数字，使用保守的update策略
        write_output(f"⚠️ ID '{document['id']}' 不是数字，使用更新操作", False)
        result = collection.update_one(
            {'id': document['id']},
            {'$set': document},
            upsert=True
        )
        return result, "update_one"

def safe_database_operation(collection, operation_type, *args, **kwargs):
    """安全的数据库操作，包含重试机制"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            if operation_type == "update_one":
                result = collection.update_one(*args, **kwargs)
            elif operation_type == "insert_one":
                result = collection.insert_one(*args, **kwargs)
            else:
                raise ValueError(f"不支持的操作类型: {operation_type}")

            return result
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                write_output(f"❌ 数据库操作失败，已重试{max_retries}次: {e}", False)
                raise
            else:
                write_output(f"⚠️ 数据库操作失败，正在重试({retry_count}/{max_retries}): {e}", False)
                time.sleep(1)  # 等待1秒后重试

def optimized_safe_database_operation(collection, document, max_id_in_db, debug_mode=False):
    """优化的安全数据库操作，包含智能策略和重试机制"""
    max_retries = 3
    retry_count = 0

    if debug_mode:
        write_output(f"🔍 数据库操作调试 - ID: {document['id']}, y_id: {document['y_id']}", False)
        write_output(f"🔍 数据库操作调试 - 集合: {collection.name}, 最大ID: {max_id_in_db}", False)

    while retry_count < max_retries:
        try:
            result, operation_used = smart_database_operation(collection, document, max_id_in_db)

            # 详细的操作结果调试
            if debug_mode:
                if operation_used == "insert_one":
                    write_output(f"🔍 插入操作结果 - ID: {document['id']}, inserted_id: {result.inserted_id}, acknowledged: {result.acknowledged}", False)
                elif operation_used == "update_one":
                    write_output(f"🔍 更新操作结果 - ID: {document['id']}, modified_count: {result.modified_count}, upserted_id: {result.upserted_id}, acknowledged: {result.acknowledged}", False)

            return result, operation_used
        except Exception as e:
            retry_count += 1
            error_msg = f"❌ 数据库操作异常 - ID: {document['id']}, 重试: {retry_count}/{max_retries}, 错误: {str(e)}"
            write_output(error_msg, False)

            if retry_count >= max_retries:
                write_output(f"❌ 优化数据库操作失败，已重试{max_retries}次: {e}", False)
                raise
            else:
                time.sleep(1)  # 等待1秒后重试

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def extract_notes(content):
    """提取notes字段"""
    notes = extract_field(content, 'notes')
    return notes.strip() if notes else None

def get_label_table_by_id(db, label_id):
    """从label表中获取相关字段"""
    try:
        # 将label_id转换为string类型进行查询
        label_id_str = str(label_id)
        label_doc = db.labels.find_one({'id': label_id_str})
        if label_doc:
            return label_doc
        return {}
    except Exception as e:
        write_output(f"获取label数据失败 (label_id: {label_id}): {e}", False)
        return {}

def extract_label_id_from_tag(line):
    """从label标签中提取ID"""
    # 匹配 <label id="123"> 格式
    pattern = r'<label\s+id="(\d+)"'
    match = re.search(pattern, line)
    return match.group(1) if match else None

def extract_links(content):
    """提取sites字段（从urls标签中获取所有url）"""
    links = []
    urls_match = re.search(r'<urls>(.*?)</urls>', content, re.DOTALL)
    if not urls_match:
        return links

    urls_content = urls_match.group(1)

    # 使用正则表达式提取所有url标签的内容
    url_pattern = r'<url>(.*?)</url>'
    url_matches = re.findall(url_pattern, urls_content, re.DOTALL)

    if url_matches:
        for url_text in url_matches:
            # 清理URL，去除前后空白字符并处理HTML实体
            clean_url = url_text.strip()
            if clean_url:  # 只添加非空的URL
                # 处理HTML实体转义
                clean_url = clean_url.replace('&amp;', '&')
                links.append(clean_url)

    return links

def process_label_content(buffer, sequential_id, db, debug_mode=False):
    """处理单个label标签的内容"""
    # 首先尝试从标签属性中提取ID
    label_id = None
    for line in buffer.split('\n'):
        if '<label' in line and 'id=' in line:
            label_id = extract_label_id_from_tag(line)
            break

    # 如果从标签属性中没有找到ID，尝试从内容中提取
    if not label_id:
        label_id = extract_field(buffer, 'id')

    if not label_id:
        if debug_mode:
            print(f"⚠️ 无法提取label ID，跳过记录")
        return None

    # 从数据库获取现有label数据
    existing_label_data = get_label_table_by_id(db, label_id)

    # 提取name字段（XML中使用<n>标签，不是<name>标签）
    label_name = extract_field(buffer, 'n') or extract_field(buffer, 'name')

    # 调试：显示字段提取结果
    if debug_mode and sequential_id <= 5:
        print(f"🔍 调试 - ID={label_id}: 原始XML片段: {buffer[:200]}...")
        print(f"🔍 调试 - ID={label_id}: 提取的name='{label_name}'")

    # 创建label文档
    label_doc = {
        'id': label_id,
        'y_id': f"YL{sequential_id}",
        'name': label_name,
        'profile': extract_field(buffer, 'profile'),
        'notes': existing_label_data.get('notes', ''),
        'links': extract_links(buffer),
        'delete_status': DeleteStatus.NOT_DELETED.value,  # 逻辑删除状态，默认未删除
        'deleted_at': None,  # 为软删除功能准备
        'created_at': datetime.now(),
        'updated_at': datetime.now(),
        'source': Source.DISCOGS.value,
        'permissions': Permissions.ALL_VISIBLE.value,
    }

    return label_doc

def process_labels(debug_mode=True):
    """处理XML文件中的label记录"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()

    # 确保labels_new集合存在
    if 'labels_new' not in db.list_collection_names():
        db.create_collection('labels_new')

    # 获取集合
    labels_collection = db['labels']
    labels_new_collection = db['labels_new']

    # 断点续传：不清空集合，继续添加数据
    print(f"🔄 断点续传模式：从第 {RESUME_FROM_RECORD + 1} 条记录开始处理")
    if debug_mode:
        print(f"🔍 调试模式已启用")

    # 测试数据库连接和写入权限
    print(f"🔧 测试数据库连接和写入权限...")
    if not test_database_connectivity(db, debug_mode):
        print(f"❌ 数据库连接测试失败，程序退出")
        return

    # 获取数据库中的最大ID，用于优化插入策略
    max_id_in_db = get_max_id_from_collection(labels_new_collection)
    print(f"🚀 启用智能插入优化，数据库最大ID: {max_id_in_db}")

    processed_count = 0
    notes_count = 0
    total_records_found = 0
    skipped_count = 0  # 跳过的记录数
    all_found_ids = []  # 调试：记录所有找到的ID

    # 性能统计
    insert_count = 0
    update_count = 0

    try:
        # 判断文件是否为gzip压缩格式
        if XML_FILE.endswith('.gz'):
            file_opener = lambda: gzip.open(XML_FILE, 'rt', encoding='utf-8')
        else:
            file_opener = lambda: open(XML_FILE, 'r', encoding='utf-8')

        # 打开XML文件并逐行读取
        with file_opener() as f:
            buffer = ""
            in_label = False

            for line in f:
                # 检查是否包含完整的label记录（在同一行）
                if '<label' in line and 'id=' in line and '</label>' in line:
                    # 完整记录在同一行
                    buffer = line
                    total_records_found += 1

                    # 提取label ID用于调试
                    label_id = extract_label_id_from_tag(line)

                    # 调试：记录所有找到的ID
                    if label_id:
                        all_found_ids.append(label_id)

                        # 调试：显示前20个找到的记录
                        if debug_mode and len(all_found_ids) <= 20:
                            name = extract_field(buffer, 'n') or extract_field(buffer, 'name') or "无名称"
                            print(f"🔍 找到记录 #{len(all_found_ids)}: ID={label_id}, name={name[:30]}...")

                    # 断点续传：跳过已处理的记录
                    if total_records_found <= RESUME_FROM_RECORD:
                        skipped_count += 1
                        buffer = ""
                        continue

                    # 处理label内容，使用连续的序号作为y_id
                    sequential_id = RESUME_FROM_RECORD + processed_count + 1
                    label_doc = process_label_content(buffer, sequential_id, db, debug_mode)
                    if label_doc:
                        # 查询数据库中是否存在该label
                        existing_label = labels_collection.find_one({'id': label_doc['id']})

                        if existing_label and '_id' in existing_label:
                            # 保留原始_id
                            label_doc['_id'] = existing_label['_id']

                        # 使用优化的数据库操作策略
                        try:
                            operation_result, operation_used = optimized_safe_database_operation(
                                labels_new_collection,
                                label_doc,
                                max_id_in_db,
                                debug_mode and processed_count < 5  # 只对前5条记录启用详细调试
                            )

                            # 验证操作是否成功
                            if verify_database_operation(operation_result, operation_used, label_doc['id'],
                                                       labels_new_collection, debug_mode and processed_count < 5):
                                processed_count += 1
                                if label_doc['notes']:
                                    notes_count += 1

                                # 更新性能统计
                                if operation_used == "insert_one":
                                    insert_count += 1
                                else:
                                    update_count += 1

                            else:
                                write_output(f"❌ 数据插入验证失败 - ID: {label_doc['id']}, y_id: {label_doc['y_id']}", False)
                                continue  # 跳过这条记录，不增加processed_count

                        except Exception as e:
                            write_output(f"❌ 数据库操作异常 - ID: {label_doc['id']}, 错误: {e}", False)
                            continue  # 跳过这条记录，不增加processed_count

                        # 显示进度
                        if processed_count % 10 == 0:
                            # 优化的日志格式，包含name信息
                            label_name = label_doc.get('name', '无名称')
                            log_message = (f"处理记录 {processed_count}: y_id={label_doc['y_id']}, "
                                      f"id={label_doc['id']}, name={label_name}")
                            # 只写入文件，不打印到控制台
                            write_output(log_message, False)
                            # 优化的控制台输出格式
                            total_processed = RESUME_FROM_RECORD + processed_count
                            print(f"已处理 {processed_count} 条记录（总计 {total_processed} 条）| ID={label_doc['id']}, name={label_name[:30]}...")

                        # 定期保存进度（只有在数据成功插入后才保存）
                        if processed_count % CHECKPOINT_INTERVAL == 0:
                            save_progress(processed_count, total_records_found)
                            write_output(f"✅ 已保存进度: 成功处理了 {processed_count} 条记录", False)

                        # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                        if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                            break

                    # 清空缓冲区
                    buffer = ""
                else:
                    # 处理跨行的label记录（如果存在）
                    if '<label' in line and 'id=' in line:
                        buffer = line
                        in_label = True
                        total_records_found += 1
                    elif '</label>' in line and in_label:
                        buffer += line
                        in_label = False

                        # 提取label ID用于调试
                        label_id = extract_label_id_from_tag(buffer)

                        # 调试：记录所有找到的ID
                        if label_id:
                            all_found_ids.append(label_id)

                            # 调试：显示前20个找到的记录
                            if debug_mode and len(all_found_ids) <= 20:
                                name = extract_field(buffer, 'n') or extract_field(buffer, 'name') or "无名称"
                                print(f"🔍 找到记录 #{len(all_found_ids)}: ID={label_id}, name={name[:30]}...")

                        # 断点续传：跳过已处理的记录
                        if total_records_found <= RESUME_FROM_RECORD:
                            skipped_count += 1
                            buffer = ""
                            continue

                        # 处理label内容，使用连续的序号作为y_id
                        sequential_id = RESUME_FROM_RECORD + processed_count + 1
                        label_doc = process_label_content(buffer, sequential_id, db, debug_mode)
                        if label_doc:
                            # 查询数据库中是否存在该label
                            existing_label = labels_collection.find_one({'id': label_doc['id']})

                            if existing_label and '_id' in existing_label:
                                # 保留原始_id
                                label_doc['_id'] = existing_label['_id']

                            # 使用优化的数据库操作策略
                            try:
                                operation_result, operation_used = optimized_safe_database_operation(
                                    labels_new_collection,
                                    label_doc,
                                    max_id_in_db,
                                    debug_mode and processed_count < 5  # 只对前5条记录启用详细调试
                                )

                                # 验证操作是否成功
                                if verify_database_operation(operation_result, operation_used, label_doc['id'],
                                                          labels_new_collection, debug_mode and processed_count < 5):
                                    processed_count += 1
                                    if label_doc['notes']:
                                        notes_count += 1

                                    # 更新性能统计
                                    if operation_used == "insert_one":
                                        insert_count += 1
                                    else:
                                        update_count += 1

                                else:
                                    write_output(f"❌ 数据插入验证失败 - ID: {label_doc['id']}, y_id: {label_doc['y_id']}", False)
                                    continue  # 跳过这条记录，不增加processed_count

                            except Exception as e:
                                write_output(f"❌ 数据库操作异常 - ID: {label_doc['id']}, 错误: {e}", False)
                                continue  # 跳过这条记录，不增加processed_count

                            # 显示进度
                            if processed_count % 10 == 0:
                                # 优化的日志格式，包含name信息
                                label_name = label_doc.get('name', '无名称')
                                log_message = (f"处理记录 {processed_count}: y_id={label_doc['y_id']}, "
                                          f"id={label_doc['id']}, name={label_name}")
                                # 只写入文件，不打印到控制台
                                write_output(log_message, False)
                                # 优化的控制台输出格式
                                total_processed = RESUME_FROM_RECORD + processed_count
                                print(f"已处理 {processed_count} 条记录（总计 {total_processed} 条）| ID={label_doc['id']}, name={label_name[:30]}...")

                            # 定期保存进度（只有在数据成功插入后才保存）
                            if processed_count % CHECKPOINT_INTERVAL == 0:
                                save_progress(processed_count, total_records_found)
                                write_output(f"✅ 已保存进度: 成功处理了 {processed_count} 条记录", False)

                            # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                            if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                                break

                        # 清空缓冲区
                        buffer = ""
                    elif in_label:
                        buffer += line
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"本次处理了 {processed_count} 条记录",
            f"跳过了 {skipped_count} 条记录（断点续传）",
            f"总计已处理 {RESUME_FROM_RECORD + processed_count} 条记录",
            f"XML文件中共发现 {total_records_found} 条记录"
        ]

        # 调试信息：显示ID分布
        if debug_mode and all_found_ids:
            # 转换为整数并排序
            numeric_ids = []
            for id_str in all_found_ids:
                try:
                    numeric_ids.append(int(id_str))
                except ValueError:
                    continue

            if numeric_ids:
                numeric_ids.sort()
                min_id = min(numeric_ids)
                max_id = max(numeric_ids)

                stats.extend([
                    f"🔍 调试信息:",
                    f"   - 总共找到 {len(all_found_ids)} 个有效ID",
                    f"   - ID范围: {min_id} - {max_id}",
                    f"   - 前10个ID: {numeric_ids[:10]}",
                    f"   - 是否包含ID=120: {'是' if 120 in numeric_ids else '否'}"
                ])

        if MAX_RECORDS > 0 and MAX_RECORDS < total_records_found:
            stats.append(f"由于设置了最大处理记录数限制 (MAX_RECORDS={MAX_RECORDS})，只处理了部分记录")

        stats.extend([
            f"处理时长: {processing_time:.2f} 秒",
            f"平均每条记录处理时间: {processing_time/processed_count:.4f} 秒" if processed_count > 0 else "平均每条记录处理时间: 0.0000 秒",
            "="*50
        ])

        # 只打印到控制台，不写入文件
        for stat in stats:
            print(stat)

        # 关闭数据库连接
        client.close()

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")

# 确保只在直接运行脚本时执行process_labels函数
if __name__ == "__main__":
    process_labels()