#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def verify_links_status():
    """验证links字段的当前状态"""
    try:
        # 连接MongoDB
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        
        print("🔍 正在检查links字段状态...")
        
        # 统计总记录数
        total_count = db.label_new.count_documents({})
        print(f"📊 数据库总记录数: {total_count:,}")
        
        # 统计有links字段的记录
        has_links_field = db.label_new.count_documents({'links': {'$exists': True}})
        print(f"📊 有links字段的记录: {has_links_field:,}")
        
        # 统计links字段不为空的记录
        has_links_data = db.label_new.count_documents({
            'links': {'$exists': True, '$ne': [], '$ne': None}
        })
        print(f"📊 有links数据的记录: {has_links_data:,}")
        
        # 统计links字段为空数组的记录
        empty_links = db.label_new.count_documents({'links': []})
        print(f"📊 links为空数组的记录: {empty_links:,}")
        
        # 统计没有links字段的记录
        no_links_field = db.label_new.count_documents({'links': {'$exists': False}})
        print(f"📊 没有links字段的记录: {no_links_field:,}")
        
        # 统计links字段为null的记录
        null_links = db.label_new.count_documents({'links': None})
        print(f"📊 links为null的记录: {null_links:,}")
        
        print("\n" + "="*50)
        print("📈 Links字段状态总结")
        print("="*50)
        print(f"✅ 已处理记录: {has_links_field:,} / {total_count:,} ({has_links_field/total_count*100:.1f}%)")
        print(f"🔗 有数据记录: {has_links_data:,} ({has_links_data/has_links_field*100:.1f}% of processed)")
        print(f"📝 空数组记录: {empty_links:,} ({empty_links/has_links_field*100:.1f}% of processed)")
        print(f"❌ 未处理记录: {no_links_field:,} ({no_links_field/total_count*100:.1f}%)")
        
        if no_links_field > 0:
            print(f"\n⚠️  需要继续处理 {no_links_field:,} 条记录")
        else:
            print(f"\n🎉 所有记录都已处理完成！")
        
        # 关闭连接
        client.close()
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")

if __name__ == "__main__":
    verify_links_status()
