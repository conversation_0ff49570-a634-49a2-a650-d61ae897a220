#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import gzip
import re
import os
import html
from pymongo import MongoClient, UpdateOne
from datetime import datetime

# 配置参数
XML_FILE = 'deploy/windows/data/discogs_20250701_labels.xml.gz'
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 输出文件路径
OUTPUT_FILE = 'links_complete_output.txt'

# 分批处理参数
BATCH_SIZE = 5000
CHECKPOINT_INTERVAL = 10000
PROGRESS_FILE = 'links_complete_progress.txt'

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True, log_level="INFO"):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] [{log_level}] {message}"

    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')

    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def extract_contactinfo(content):
    """提取contactinfo信息"""
    try:
        # 匹配 <contactinfo>联系信息内容</contactinfo> 格式
        contactinfo_pattern = r'<contactinfo>(.*?)</contactinfo>'
        match = re.search(contactinfo_pattern, content, re.DOTALL)

        if match:
            contactinfo_content = match.group(1)
            
            # 处理HTML实体编码
            # 首先处理&#13;（回车符）为换行符
            contactinfo_content = contactinfo_content.replace('&#13;', '\n')
            
            # 处理其他HTML实体编码
            contactinfo_content = html.unescape(contactinfo_content)
            
            # 清理多余的空白字符，但保留换行符
            contactinfo_content = contactinfo_content.strip()
            
            return contactinfo_content
        return None
    except Exception as e:
        write_output(f"解析contactinfo时出错: {e}", False, "WARN")
        return None

def extract_label_id_from_tag(line):
    """从label标签中提取ID"""
    # 匹配 <label><id>123</id> 或 <label id="123"> 格式
    pattern1 = r'<label><id>(\d+)</id>'
    pattern2 = r'<label\s+id=["\']?(\d+)["\']?'
    
    match = re.search(pattern1, line)
    if match:
        return match.group(1)
    
    match = re.search(pattern2, line)
    if match:
        return match.group(1)
    
    return None

def save_progress(processed_count, with_contactinfo_count, without_contactinfo_count, error_count):
    """保存处理进度"""
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            f.write(f"{processed_count},{with_contactinfo_count},{without_contactinfo_count},{error_count}")
    except Exception as e:
        write_output(f"保存进度失败: {e}", False, "ERROR")

def load_progress():
    """加载处理进度"""
    try:
        if os.path.exists(PROGRESS_FILE):
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                line = f.read().strip()
                if line:
                    parts = line.split(',')
                    if len(parts) == 4:
                        return int(parts[0]), int(parts[1]), int(parts[2]), int(parts[3])
        return 0, 0, 0, 0
    except Exception as e:
        write_output(f"加载进度失败: {e}", False, "ERROR")
        return 0, 0, 0, 0

def batch_update_contactinfo(db, update_batch):
    """批量更新contactinfo字段"""
    if not update_batch:
        return 0, 0

    success_count = 0
    error_count = 0

    try:
        # 构建批量更新操作
        bulk_operations = []
        for label_id, contactinfo_content in update_batch:
            # contactinfo_content 可能是字符串（有contactinfo）或空字符串（无contactinfo）
            bulk_operations.append(
                UpdateOne(
                    {'id': str(label_id)},
                    {
                        '$set': {
                            'contactinfo': contactinfo_content,
                            'updated_at': datetime.now()
                        }
                    }
                )
            )

        # 执行批量更新
        if bulk_operations:
            result = db.label_new.bulk_write(bulk_operations, ordered=False)
            success_count = result.modified_count

            if success_count != len(update_batch):
                error_count = len(update_batch) - success_count

    except Exception as e:
        write_output(f"批量更新失败: {e}", False, "ERROR")
        error_count = len(update_batch)

    return success_count, error_count

def process_all_contactinfo():
    """处理XML文件中的所有label记录，确保每条记录都有contactinfo字段"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()

    # 加载之前的进度
    prev_processed, prev_with_contactinfo, prev_without_contactinfo, prev_errors = load_progress()

    # 统计变量
    total_records_processed = prev_processed
    records_with_contactinfo = prev_with_contactinfo
    records_without_contactinfo = prev_without_contactinfo
    error_count = prev_errors

    write_output("🚀 开始处理所有label记录的contactinfo字段")
    write_output(f"📁 XML文件: {XML_FILE}")
    write_output(f"🗄️ 数据库: {DB_NAME}")

    if prev_processed > 0:
        write_output(f"📊 恢复之前的进度: 已处理 {prev_processed:,} 条记录")

    # 用于批量更新的缓存
    update_batch = []

    try:
        # 检查XML文件是否存在
        if not os.path.exists(XML_FILE):
            write_output(f"❌ XML文件不存在: {XML_FILE}")
            return

        # 打开gz压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_label = False
            skip_count = prev_processed  # 跳过已处理的记录

            write_output("📖 开始读取XML文件...")

            for line in f:
                # 检查是否包含完整的label记录（在同一行）
                if '<label>' in line and '</label>' in line:
                    # 完整记录在同一行
                    total_records_processed += 1
                    
                    # 如果还在跳过阶段，继续跳过
                    if skip_count > 0:
                        skip_count -= 1
                        continue
                    
                    # 提取label ID
                    label_id = extract_label_id_from_tag(line)
                    
                    if label_id:
                        # 检查XML中是否包含contactinfo信息
                        contactinfo_content = extract_contactinfo(line)
                        
                        if contactinfo_content:
                            # 有contactinfo信息
                            records_with_contactinfo += 1
                            update_batch.append((label_id, contactinfo_content))
                        else:
                            # 没有contactinfo信息，设置为空字符串
                            records_without_contactinfo += 1
                            update_batch.append((label_id, ""))
                        
                        # 当批次达到指定大小时执行批量更新
                        if len(update_batch) >= BATCH_SIZE:
                            batch_success, batch_errors = batch_update_contactinfo(db, update_batch)
                            if batch_success > 0:
                                write_output(f"✅ 批量更新成功: {batch_success} 条记录")
                            if batch_errors > 0:
                                error_count += batch_errors
                                write_output(f"❌ 批量更新失败: {batch_errors} 条记录")
                            
                            # 清空批次
                            update_batch = []
                
                # 处理跨行的label记录（如果有的话）
                elif '<label>' in line and '</label>' not in line:
                    buffer = line
                    in_label = True
                    
                    # 如果还在跳过阶段，继续跳过
                    if skip_count > 0:
                        skip_count -= 1
                        in_label = False
                        buffer = ""
                        continue

                elif '</label>' in line and in_label:
                    buffer += line
                    in_label = False
                    
                    total_records_processed += 1
                    
                    # 提取label ID
                    label_id = extract_label_id_from_tag(buffer)
                    
                    if label_id:
                        # 检查XML中是否包含contactinfo信息
                        contactinfo_content = extract_contactinfo(buffer)
                        
                        if contactinfo_content:
                            # 有contactinfo信息
                            records_with_contactinfo += 1
                            update_batch.append((label_id, contactinfo_content))
                        else:
                            # 没有contactinfo信息，设置为空字符串
                            records_without_contactinfo += 1
                            update_batch.append((label_id, ""))
                        
                        # 当批次达到指定大小时执行批量更新
                        if len(update_batch) >= BATCH_SIZE:
                            batch_success, batch_errors = batch_update_contactinfo(db, update_batch)
                            if batch_success > 0:
                                write_output(f"✅ 批量更新成功: {batch_success} 条记录")
                            if batch_errors > 0:
                                error_count += batch_errors
                                write_output(f"❌ 批量更新失败: {batch_errors} 条记录")
                            
                            # 清空批次
                            update_batch = []
                    
                    # 清空缓冲区
                    buffer = ""
                    
                elif in_label:
                    buffer += line

                # 显示进度并保存检查点
                if total_records_processed % CHECKPOINT_INTERVAL == 0 and total_records_processed > 0:
                    elapsed_time = time.time() - start_time
                    write_output(f"📊 进度: 已处理 {total_records_processed:,} 条记录, "
                               f"有contactinfo: {records_with_contactinfo:,}, "
                               f"无contactinfo: {records_without_contactinfo:,}, "
                               f"错误: {error_count:,}, "
                               f"耗时: {elapsed_time:.1f}秒")

                    # 保存进度
                    save_progress(total_records_processed, records_with_contactinfo, 
                                records_without_contactinfo, error_count)

            # 处理剩余的批次
            if update_batch:
                batch_success, batch_errors = batch_update_contactinfo(db, update_batch)
                if batch_success > 0:
                    write_output(f"✅ 最终批量更新成功: {batch_success} 条记录")
                if batch_errors > 0:
                    error_count += batch_errors
                    write_output(f"❌ 最终批量更新失败: {batch_errors} 条记录")

    except Exception as e:
        write_output(f"❌ 处理过程中发生错误: {e}", True, "ERROR")
        raise

    finally:
        # 关闭数据库连接
        client.close()

        # 计算总耗时
        total_time = time.time() - start_time

        # 保存最终进度
        save_progress(total_records_processed, records_with_contactinfo, 
                     records_without_contactinfo, error_count)

        # 输出最终统计
        write_output("\n" + "="*60)
        write_output("📈 Contactinfo 完整更新任务统计")
        write_output("="*60)
        write_output(f"📊 总处理记录数: {total_records_processed:,}")
        write_output(f"📞 包含contactinfo的记录: {records_with_contactinfo:,}")
        write_output(f"📝 设置为空字符串的记录: {records_without_contactinfo:,}")
        write_output(f"❌ 处理失败的记录: {error_count:,}")
        write_output(f"⏱️ 总耗时: {total_time:.2f} 秒")
        
        if total_time > 0:
            write_output(f"🚀 平均处理速度: {total_records_processed/total_time:.1f} 记录/秒")

        total_updated = records_with_contactinfo + records_without_contactinfo
        if total_updated > 0:
            write_output(f"🎉 成功处理 {total_updated:,} 条记录的contactinfo字段！")
        else:
            write_output("ℹ️ 没有记录被处理")

if __name__ == "__main__":
    process_all_contactinfo()
