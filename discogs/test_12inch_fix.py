#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def test_12inch_fix():
    """测试12英寸JSON修复逻辑"""
    
    # 测试具体的问题JSON - 从CSV中提取的原始格式
    test_json = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    print('原始CSV字段:', test_json)
    
    # 步骤1: 去掉外层引号
    if test_json.startswith('"') and test_json.endswith('"'):
        test_json = test_json[1:-1]
    print('去掉外层引号:', test_json)
    
    # 步骤2: 先修复不完整的空字符串值
    fixed = re.sub(r':\s*"\s*(?=[,}])', r': ""', test_json)
    print('修复空字符串:', fixed)
    
    # 步骤3: 处理双引号转义
    fixed = fixed.replace('""', '"')
    print('处理双引号转义:', fixed)
    
    try:
        parsed = json.loads(fixed)
        print('✅ 成功:', parsed[0])
        return True
    except Exception as e:
        print('❌ 失败:', e)
        return False

def test_alternative_approach():
    """测试替代方法"""
    print('\n--- 测试替代方法 ---')
    
    # 原始CSV字段
    test_json = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    print('原始:', test_json)
    
    # 去掉外层引号
    if test_json.startswith('"') and test_json.endswith('"'):
        test_json = test_json[1:-1]
    
    # 更智能的双引号处理
    # 先处理 """" 这种情况（空字符串）
    fixed = test_json.replace('""""', '""')
    print('处理空字符串转义:', fixed)
    
    # 然后处理其他双引号
    fixed = fixed.replace('""', '"')
    print('处理其他双引号:', fixed)
    
    try:
        parsed = json.loads(fixed)
        print('✅ 成功:', parsed[0])
        return True
    except Exception as e:
        print('❌ 失败:', e)
        return False

if __name__ == '__main__':
    print('🧪 测试12英寸JSON修复')
    print('=' * 50)
    
    success1 = test_12inch_fix()
    success2 = test_alternative_approach()
    
    if success1 or success2:
        print('\n✅ 至少一种方法成功')
    else:
        print('\n❌ 所有方法都失败')
