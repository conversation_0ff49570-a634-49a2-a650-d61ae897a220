#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 递增式Release数据获取器

功能：
1. 从数据库中查询当前最大ID
2. 从最大ID+1开始递增调用Discogs API
3. 支持代理配置以访问外网API
4. 处理不同响应状态：成功、404、429限流
5. 将数据分别存储到对应的数据库表中

特性：
- 内置代理配置支持翻墙访问
- 智能错误处理和状态记录
- API频率限制控制
- 测试模式支持
- 详细的日志记录

作者：AI Assistant
创建时间：2025-08-01
"""

import os
import sys
import time
import json
import requests
import logging
import argparse
from datetime import datetime, timezone
from pymongo import MongoClient
from typing import Dict, Optional

# 导入现有的数据转换函数
try:
    from api_release_补全器 import convert_api_response_to_document
    CONVERT_FUNCTION_AVAILABLE = True
except ImportError:
    CONVERT_FUNCTION_AVAILABLE = False
    print("⚠️ 警告：无法导入convert_api_response_to_document函数，将使用简化版本")

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
TARGET_COLLECTION_NAME = 'release_new'  # 存储成功数据和429状态
COPY_TARGET_COLLECTION_NAME = 'release_copy' 
NOT_FOUND_COLLECTION_NAME = 'release_404'  # 存储404错误

# API配置
API_BASE_URL = 'https://api.discogs.com/releases'
API_RATE_LIMIT = 1.0  # 1秒1次请求
API_TIMEOUT = 30  # 请求超时时间
MAX_RETRIES = 3  # 最大重试次数

# 代理配置
PROXY_CONFIG = {
    'http': 'http://127.0.0.1:65503',
    'https': 'http://127.0.0.1:65503',
    # 'socks5': 'socks5://127.0.0.1:65502'  # 如果需要SOCKS5代理，取消注释
}

# 测试配置
TEST_MODE = True  # 默认测试模式，只处理20条数据
TEST_RECORDS_COUNT = 20

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api_incremental_fetcher.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class ProxyAPIClient:
    """带代理配置的Discogs API客户端"""
    
    def __init__(self, use_proxy=True):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DiscogsIncrementalFetcher/1.0 +https://example.com/contact'
        })
        
        # 设置代理
        if use_proxy:
            self.session.proxies.update(PROXY_CONFIG)
            logger.info(f"🌐 已配置代理: {PROXY_CONFIG}")
        else:
            logger.info("🌐 未使用代理配置")
            
        self.last_request_time = 0
    
    def _wait_for_rate_limit(self):
        """等待满足API频率限制"""
        elapsed = time.time() - self.last_request_time
        if elapsed < API_RATE_LIMIT:
            sleep_time = API_RATE_LIMIT - elapsed
            logger.debug(f"⏱️ 频率控制：等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)
    
    def get_release(self, release_id: int) -> Optional[Dict]:
        """获取release数据"""
        self._wait_for_rate_limit()
        
        url = f"{API_BASE_URL}/{release_id}"
        
        for attempt in range(MAX_RETRIES):
            try:
                self.last_request_time = time.time()
                response = self.session.get(url, timeout=API_TIMEOUT)
                
                if response.status_code == 200:
                    logger.debug(f"✅ API成功 - ID {release_id}")
                    return response.json()
                elif response.status_code == 404:
                    logger.debug(f"⏭️ API 404 - ID {release_id}")
                    return None  # ID不存在
                elif response.status_code == 429:
                    logger.info(f"⏳ API限流 (429) - ID {release_id}")
                    return "429"  # 返回特殊标识表示429状态
                else:
                    logger.warning(f"⚠️ API请求失败 (ID: {release_id}): HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ API请求超时 (ID: {release_id}), 尝试 {attempt + 1}/{MAX_RETRIES}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"🌐 网络错误 (ID: {release_id}): {e}, 尝试 {attempt + 1}/{MAX_RETRIES}")
            
            # 重试前等待
            if attempt < MAX_RETRIES - 1:
                time.sleep(2 ** attempt)  # 指数退避
        
        logger.error(f"❌ API请求最终失败 - ID {release_id}")
        return False  # 表示请求失败

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        db = client[DB_NAME]

        # 确保目标集合存在
        if TARGET_COLLECTION_NAME not in db.list_collection_names():
            db.create_collection(TARGET_COLLECTION_NAME)
            logger.info(f"✅ 创建集合: {TARGET_COLLECTION_NAME}")

        # 确保404集合存在
        if NOT_FOUND_COLLECTION_NAME not in db.list_collection_names():
            db.create_collection(NOT_FOUND_COLLECTION_NAME)
            logger.info(f"✅ 创建集合: {NOT_FOUND_COLLECTION_NAME}")

        logger.info(f"✅ 成功连接到MongoDB")
        logger.info(f"📊 目标表: {DB_NAME}.{TARGET_COLLECTION_NAME}")
        logger.info(f"📊 404表: {DB_NAME}.{NOT_FOUND_COLLECTION_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        raise

def get_max_id_from_database(db) -> int:
    """从数据库中获取当前最大的ID值"""
    try:
        collection = db[TARGET_COLLECTION_NAME]
        
        # 查询最大ID
        result = collection.find({}, {'id': 1}).sort('id', -1).limit(1)
        max_doc = list(result)
        
        if max_doc:
            max_id = int(max_doc[0]['id'])
            logger.info(f"📊 数据库中当前最大ID: {max_id}")
            return max_id
        else:
            logger.info("📊 数据库为空，将从ID 1开始")
            return 0
            
    except Exception as e:
        logger.error(f"❌ 获取最大ID失败: {e}")
        return 0

def insert_404_record(db, release_id: int) -> bool:
    """插入404错误记录到release_404表"""
    try:
        collection = db[NOT_FOUND_COLLECTION_NAME]
        
        # 检查是否已存在
        if collection.find_one({'id': release_id}):
            logger.debug(f"📝 404 ID {release_id} 已存在于数据库")
            return True
        
        # 插入新的404记录
        doc = {
            'id': release_id,
            'created_at': datetime.now(),
            'source': 'api_incremental_fetcher'
        }
        collection.insert_one(doc)
        logger.debug(f"✅ 404记录已插入 - ID {release_id}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 插入404记录失败 (ID: {release_id}): {e}")
        return False

def insert_429_record(db, release_id: int) -> bool:
    """插入429限流记录到release_new表"""
    try:
        collection = db[COPY_TARGET_COLLECTION_NAME]

        # 检查是否已存在
        if collection.find_one({'id': release_id}):
            logger.debug(f"📝 429 ID {release_id} 已存在于数据库")
            return True

        # 插入429状态记录
        doc = {
            'id': release_id,
            'status': 429,
            'created_at': datetime.now(),
            'source': 'api_incremental_fetcher'
        }
        collection.insert_one(doc)
        logger.debug(f"✅ 429记录已插入 - ID {release_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 插入429记录失败 (ID: {release_id}): {e}")
        return False

def insert_success_record(db, api_data: Dict) -> bool:
    """插入成功获取的数据到release_new表"""
    try:
        collection = db[TARGET_COLLECTION_NAME]
        release_id = api_data.get('id')

        logger.info(f"🔍 准备插入数据 - ID {release_id}")

        # 检查是否已存在
        existing = collection.find_one({'id': release_id})
        if existing:
            logger.info(f"📝 成功数据 ID {release_id} 已存在于数据库")
            return True

        # 转换API数据为数据库文档格式
        if CONVERT_FUNCTION_AVAILABLE:
            try:
                doc = convert_api_response_to_document(api_data, db)
                if doc is None:
                    logger.warning(f"⚠️ 数据转换返回None - ID {release_id}，使用简化版本")
                    # 使用简化版本作为备选
                    doc = create_simple_document(api_data)
                else:
                    # 覆盖source字段以标识数据来源
                    doc['source'] = 'api_incremental_fetcher'
                    doc['updated_at'] = datetime.now()
            except Exception as e:
                logger.warning(f"⚠️ 使用现有转换函数失败 - ID {release_id}: {e}")
                # 使用简化版本作为备选
                doc = create_simple_document(api_data)
        else:
            # 简化版本的数据转换
            logger.info(f"🔧 使用简化版本转换数据 - ID {release_id}")
            doc = create_simple_document(api_data)

        logger.info(f"💾 正在插入文档 - ID {release_id}, 字段数: {len(doc)}")
        result = collection.insert_one(doc)
        logger.info(f"✅ 成功数据已插入 - ID {release_id}, MongoDB ID: {result.inserted_id}")
        return True

    except Exception as e:
        logger.error(f"❌ 插入成功数据失败 (ID: {api_data.get('id', 'unknown')}): {e}")
        import traceback
        logger.error(f"❌ 详细错误: {traceback.format_exc()}")
        return False

def create_simple_document(api_data: Dict) -> Dict:
    """创建简化的数据库文档"""
    return {
        'id': api_data.get('id'),
        'title': api_data.get('title', ''),
        'country': api_data.get('country', ''),
        'year': api_data.get('year'),
        'master_id': api_data.get('master_id'),
        'artists': api_data.get('artists', []),
        'labels': api_data.get('labels', []),
        'genres': api_data.get('genres', []),
        'styles': api_data.get('styles', []),
        'formats': api_data.get('formats', []),
        'tracklist': api_data.get('tracklist', []),
        'identifiers': api_data.get('identifiers', []),
        'notes': api_data.get('notes', ''),
        'created_at': datetime.now(),
        'source': 'api_incremental_fetcher'
    }

def process_api_response(db, release_id: int, api_data) -> str:
    """处理API响应并存储到相应的数据库表"""
    if api_data is None:
        # 404错误
        if insert_404_record(db, release_id):
            return "404_inserted"
        else:
            return "404_failed"
    elif api_data == "429":
        # 429限流
        if insert_429_record(db, release_id):
            return "429_inserted"
        else:
            return "429_failed"
    elif api_data is False:
        # API请求失败
        return "api_failed"
    elif isinstance(api_data, dict):
        # 成功获取数据
        if insert_success_record(db, api_data):
            return "success_inserted"
        else:
            return "success_failed"
    else:
        # 未知响应类型
        logger.warning(f"⚠️ 未知响应类型 - ID {release_id}: {type(api_data)}")
        return "unknown_response"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Discogs API 递增式Release数据获取器')
    parser.add_argument('--no-proxy', action='store_true', help='不使用代理')
    parser.add_argument('--production', action='store_true', help='生产模式（处理所有数据）')
    parser.add_argument('--start-id', type=int, help='指定起始ID（覆盖数据库最大ID）')
    parser.add_argument('--max-records', type=int, help='最大处理记录数')

    args = parser.parse_args()

    # 设置模式
    global TEST_MODE
    if args.production:
        TEST_MODE = False
        logger.info("🚀 生产模式：将处理所有数据")
    else:
        logger.info(f"🧪 测试模式：只处理 {TEST_RECORDS_COUNT} 条数据")

    try:
        # 连接数据库
        logger.info("🔗 连接数据库...")
        client, db = connect_to_mongodb()

        # 获取起始ID
        if args.start_id:
            start_id = args.start_id
            logger.info(f"📍 使用指定起始ID: {start_id}")
        else:
            max_id = get_max_id_from_database(db)
            start_id = max_id + 1
            logger.info(f"📍 从数据库最大ID+1开始: {start_id}")

        # 创建API客户端
        logger.info("🌐 初始化API客户端...")
        api_client = ProxyAPIClient(use_proxy=not args.no_proxy)

        # 确定处理数量
        if args.max_records:
            max_records = args.max_records
        elif TEST_MODE:
            max_records = TEST_RECORDS_COUNT
        else:
            max_records = None  # 无限制

        # 开始处理
        logger.info("🚀 开始递增式API调用...")
        logger.info(f"📊 起始ID: {start_id}")
        if max_records:
            logger.info(f"📊 最大处理数量: {max_records}")
            logger.info(f"📊 结束ID: {start_id + max_records - 1}")

        # 统计信息
        stats = {
            'processed': 0,
            'success': 0,
            'not_found_404': 0,
            'rate_limited_429': 0,
            'api_failed': 0,
            'db_failed': 0
        }

        # 记录测试的ID范围
        test_ids = []

        current_id = start_id
        start_time = time.time()

        try:
            while True:
                # 检查是否达到最大处理数量
                if max_records and stats['processed'] >= max_records:
                    break

                # 记录测试ID
                if TEST_MODE:
                    test_ids.append(current_id)

                # 调用API
                logger.info(f"📞 调用API - ID {current_id}")
                api_data = api_client.get_release(current_id)

                # 处理响应
                result = process_api_response(db, current_id, api_data)

                # 更新统计
                stats['processed'] += 1
                if result == "success_inserted":
                    stats['success'] += 1
                elif result == "404_inserted":
                    stats['not_found_404'] += 1
                elif result == "429_inserted":
                    stats['rate_limited_429'] += 1
                elif result == "api_failed":
                    stats['api_failed'] += 1
                else:
                    stats['db_failed'] += 1

                # 显示进度
                if stats['processed'] % 10 == 0 or TEST_MODE:
                    elapsed = time.time() - start_time
                    rate = stats['processed'] / elapsed if elapsed > 0 else 0
                    logger.info(f"📊 进度: {stats['processed']}/{max_records or '∞'} "
                              f"(成功:{stats['success']}, 404:{stats['not_found_404']}, "
                              f"429:{stats['rate_limited_429']}, 失败:{stats['api_failed'] + stats['db_failed']}) "
                              f"速率: {rate:.2f}/秒")

                current_id += 1

        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断处理")

        # 最终统计
        elapsed = time.time() - start_time
        logger.info("\n" + "=" * 60)
        logger.info("📊 处理完成统计")
        logger.info("=" * 60)
        logger.info(f"⏱️ 总耗时: {elapsed:.2f} 秒")
        logger.info(f"📈 处理速率: {stats['processed'] / elapsed:.2f} 条/秒")
        logger.info(f"📊 总处理数量: {stats['processed']}")
        logger.info(f"✅ 成功获取: {stats['success']}")
        logger.info(f"⏭️ 404错误: {stats['not_found_404']}")
        logger.info(f"⏳ 429限流: {stats['rate_limited_429']}")
        logger.info(f"❌ API失败: {stats['api_failed']}")
        logger.info(f"💾 数据库失败: {stats['db_failed']}")

        # 显示测试ID范围
        if TEST_MODE and test_ids:
            logger.info(f"🧪 测试ID范围: {min(test_ids)} - {max(test_ids)}")
            logger.info(f"🧪 测试的具体ID: {test_ids}")

    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        raise
    finally:
        if 'client' in locals():
            client.close()
            logger.info("🔗 数据库连接已关闭")

if __name__ == "__main__":
    main()
