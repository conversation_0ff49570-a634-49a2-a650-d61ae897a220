#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def debug_parse_json_field(field_value: str):
    """调试JSON字段解析"""
    
    print('=== 调试JSON字段解析 ===')
    print(f'输入: {field_value}')
    
    if not field_value or field_value.strip() == '':
        return []
    
    try:
        # 处理CSV中的双引号转义
        if field_value.startswith('"') and field_value.endswith('"'):
            field_value = field_value[1:-1]
        print(f'去掉外层引号: {field_value}')
        
        # 处理CSV转义的双引号
        # 1. 先处理特殊的12英寸模式
        search_pattern = '""12\""""'
        print(f'查找 {search_pattern}: {field_value.find(search_pattern)}')
        field_value = field_value.replace('""12\""""', '"12__QUOTE__"')
        print(f'处理12英寸模式: {field_value}')

        # 2. 修复不完整的空字符串值
        field_value = re.sub(r':\s*"\s*(?=[,}])', r': ""', field_value)
        print(f'修复空字符串: {field_value}')

        # 3. 处理普通的双引号转义
        field_value = field_value.replace('""', '"')
        print(f'处理普通双引号: {field_value}')

        # 4. 恢复特殊标记为正确的JSON转义
        field_value = field_value.replace('__QUOTE__', '\\"')
        print(f'恢复特殊标记: {field_value}')
        
        # 解析JSON
        parsed = json.loads(field_value)
        print(f'✅ 解析成功: {parsed}')
        return parsed if parsed is not None else []
        
    except (json.JSONDecodeError, ValueError) as e:
        print(f'❌ JSON解析失败: {field_value[:100]}... 错误: {e}')
        return []

if __name__ == '__main__':
    # 测试问题JSON
    test_json = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    result = debug_parse_json_field(test_json)
