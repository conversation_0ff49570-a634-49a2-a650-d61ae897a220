#!/usr/bin/env python3
"""
综合CSV兼容性测试脚本
测试修复后的api_release_补全器.py与csv_to_mongodb_importer.py的完整兼容性
包括对实际生产数据的测试
"""

import json
import csv
import os
import sys
import pandas as pd
from datetime import datetime, timezone

# 导入修复后的函数
sys.path.append('.')
from api_release_补全器 import (
    safe_string_value, 
    format_array_for_csv, 
    clean_data_for_json,
    write_releases_to_csv
)
from csv_to_mongodb_importer import CSVToMongoImporter

def test_problematic_data_patterns():
    """测试各种问题数据模式"""
    print("🧪 测试问题数据模式...")
    
    # 模拟batch_3_releases.csv中的实际问题数据
    problematic_data = {
        'id': 26335,
        'y_id': 'YRD17301725',
        'title': 'In Africa 1996 (The 4 Years Later Version)',
        'artists': [
            {
                'artist_id': 22072,
                'name': 'Manitou',
                'role': '',
                'anv': ''
            }
        ],
        'extra_artists': [
            {
                'artist_id': 234065,
                'name': '<PERSON><PERSON>jedaghi',
                'role': 'Producer, Written-By',
                'anv': ''
            },
            {
                'artist_id': 108417,
                'name': 'Rolf Maier-Bode',
                'role': 'Producer, Written-By',
                'anv': ''
            }
        ],
        'labels': [
            {
                'name': 'Urban',
                'catno': '576 485-1',
                'id': '2017'
            }
        ],
        'companies': [],
        'country': 'Germany',
        'formats': [
            {
                'name': 'Vinyl',
                'qty': '1',
                'text': '',
                'descriptions': ['12"']  # 这里包含引号
            }
        ],
        'genres': ['Electronic'],
        'styles': ['Techno'],
        'identifiers': [
            {
                'type': 'Barcode',
                'value': '731457648513',
                'description': ''
            }
        ],
        # 关键问题：包含换行符的tracklist
        'tracklist': [
            {
                'position': 'A',
                'title': 'In Africa 1996 (4 Years Later Version)',
                'duration': '6:54'
            },
            {
                'position': 'B1',
                'title': 'The Arab',
                'duration': '5:34'
            },
            {
                'position': 'B2',
                'title': 'In Africa (1992 Version)',
                'duration': '5:28'
            }
        ],
        'master_id': 82024,
        'discogs_status': '',
        'images': [
            {
                'type': 'primary',
                'uri': './/release055/release_26335_uri_1.jpg',
                'resource_url': 'https://i.discogs.com/Gf-G1Y83Rm6lMXrhwjq23HbMifD4jEB-92t7Cz3-Svw/rs:fit/g:sm/q:90/h:600/w:600/czM6Ly9kaXNjb2dz/LWRhdGFiYXNlLWlt/YWdlcy9SLTI2MzM1/LTE2NzUzNjg4MDct/NTY0Mi5qcGVn.jpeg',
                'uri150': './/release055/release_26335_uri150_1.jpg',
                'width': 600,
                'height': 600,
                'newurlflag': 1
            },
            {
                'type': 'secondary',
                'uri': './/release055/release_26335_uri_2.jpg',
                'resource_url': 'https://i.discogs.com/jgh4V98uydmGGm_5HWizAi6kNjUdxO3Ne4WjgolTUc0/rs:fit/g:sm/q:90/h:596/w:600/czM6Ly9kaXNjb2dz/LWRhdGFiYXNlLWlt/YWdlcy9SLTI2MzM1/LTE2NzUzNjg4MDct/NjUzMy5qcGVn.jpeg',
                'uri150': './/release055/release_26335_uri150_2.jpg',
                'width': 600,
                'height': 596,
                'newurlflag': 1
            }
        ],
        'notes': '',
        'year': 1996,
        'images_permissions': 1,
        'permissions': 1,
        'source': 1,
        'created_at': datetime.now(timezone.utc),
        'updated_at': datetime.now(timezone.utc)
    }
    
    # 测试各个JSON字段的格式化
    json_fields = ['artists', 'extra_artists', 'labels', 'formats', 'tracklist', 'images']
    
    all_passed = True
    for field in json_fields:
        if field in problematic_data:
            json_str = format_array_for_csv(problematic_data[field])
            print(f"🔍 {field}: {json_str[:100]}...")
            
            # 验证JSON有效性
            try:
                parsed = json.loads(json_str)
                print(f"✅ {field}: JSON有效")
            except json.JSONDecodeError as e:
                print(f"❌ {field}: JSON无效 - {e}")
                all_passed = False
            
            # 验证没有换行符
            if '\n' not in json_str and '\r' not in json_str:
                print(f"✅ {field}: 无换行符")
            else:
                print(f"❌ {field}: 包含换行符")
                all_passed = False
            
            print()
    
    return all_passed, problematic_data

def test_csv_generation_and_parsing():
    """测试CSV生成和解析的完整流程"""
    print("🧪 测试CSV生成和解析完整流程...")
    
    # 获取测试数据
    passed, test_data = test_problematic_data_patterns()
    if not passed:
        print("❌ 基础测试失败，跳过CSV测试")
        return False
    
    # 生成CSV文件
    test_filename = 'comprehensive_test.csv'
    write_releases_to_csv([test_data], test_filename, append_mode=False)
    
    if not os.path.exists(test_filename):
        print(f"❌ CSV文件生成失败: {test_filename}")
        return False
    
    print(f"✅ CSV文件生成成功: {test_filename}")
    
    # 验证CSV结构
    with open(test_filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        print(f"📊 CSV文件行数: {len(lines)}")
        
        # 应该只有2行（头部 + 1条数据）+ 可能的空行
        non_empty_lines = [line for line in lines if line.strip()]
        if len(non_empty_lines) == 2:
            print("✅ CSV结构正确")
        else:
            print(f"❌ CSV结构异常，期望2行，实际{len(non_empty_lines)}行")
            return False
    
    # 使用pandas读取测试
    try:
        df = pd.read_csv(test_filename)
        print(f"✅ pandas读取成功: {len(df)} 行")
    except Exception as e:
        print(f"❌ pandas读取失败: {e}")
        return False
    
    # 使用导入器测试
    try:
        importer = CSVToMongoImporter(csv_file_path=test_filename, test_mode=True)
        result = importer.process_csv_file()
        
        if result:
            print("✅ 导入器处理成功")
            return True
        else:
            print("❌ 导入器处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 导入器测试失败: {e}")
        return False

def analyze_existing_csv(csv_filename: str):
    """分析现有CSV文件的问题"""
    print(f"🔍 分析现有CSV文件: {csv_filename}")
    
    if not os.path.exists(csv_filename):
        print(f"❌ 文件不存在: {csv_filename}")
        return
    
    try:
        # 尝试用pandas读取
        df = pd.read_csv(csv_filename)
        print(f"📊 pandas读取: {len(df)} 行, {len(df.columns)} 列")
        
        # 分析JSON字段
        json_fields = ['artists', 'extra_artists', 'labels', 'companies', 
                      'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']
        
        total_json_fields = 0
        successful_json_fields = 0
        failed_examples = []
        
        for index, row in df.iterrows():
            for field in json_fields:
                if field in row and pd.notna(row[field]) and str(row[field]).strip():
                    total_json_fields += 1
                    try:
                        json.loads(str(row[field]))
                        successful_json_fields += 1
                    except json.JSONDecodeError as e:
                        if len(failed_examples) < 5:  # 只收集前5个失败例子
                            failed_examples.append({
                                'row': index + 1,
                                'field': field,
                                'error': str(e),
                                'value': str(row[field])[:100] + '...'
                            })
        
        if total_json_fields > 0:
            success_rate = (successful_json_fields / total_json_fields) * 100
            print(f"📊 JSON解析成功率: {successful_json_fields}/{total_json_fields} ({success_rate:.2f}%)")
            
            if failed_examples:
                print("❌ 失败示例:")
                for example in failed_examples:
                    print(f"   行{example['row']}, {example['field']}: {example['error']}")
                    print(f"   值: {example['value']}")
        else:
            print("⚠️ 没有找到JSON字段")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    print("🚀 综合CSV兼容性测试")
    print("=" * 60)
    
    # 1. 测试修复后的代码
    print("📋 步骤1: 测试修复后的代码")
    code_test_passed = test_csv_generation_and_parsing()
    
    print("\n" + "=" * 60)
    
    # 2. 分析现有的问题文件
    print("📋 步骤2: 分析现有问题文件")
    if os.path.exists('batch_3_releases.csv'):
        analyze_existing_csv('batch_3_releases.csv')
    else:
        print("⚠️ batch_3_releases.csv 文件不存在")
    
    print("\n" + "=" * 60)
    
    # 3. 总结
    print("📋 测试总结:")
    if code_test_passed:
        print("✅ 修复后的代码完全兼容")
        print("✅ 可以安全地用于生产环境")
        print("💡 建议：使用修复后的api_release_补全器.py重新生成CSV文件")
    else:
        print("❌ 修复后的代码仍有问题")
        print("⚠️ 需要进一步调试和优化")

if __name__ == "__main__":
    main()
