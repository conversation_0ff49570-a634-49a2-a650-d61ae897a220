#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
探索searchRuleRelease字段结构脚本
用于分析release表中searchRuleRelease字段的JSON结构，
特别是baseInfo.barcode字段的位置和格式
"""

import os
import json
import time
from pymongo import MongoClient
from datetime import datetime

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
SAMPLE_SIZE = int(os.getenv('SAMPLE_SIZE', '20'))  # 分析的样本数量

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'searchrule_structure_analysis.txt')

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    formatted_message = f"[{timestamp}] {message}"
    
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def analyze_searchrule_structure():
    """分析searchRuleRelease字段的结构"""
    start_time = time.time()
    write_output("🔍 开始分析searchRuleRelease字段结构")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    write_output("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取release集合
        release_collection = db['release']
        
        # 检查集合是否存在
        if 'release' not in db.list_collection_names():
            write_output("❌ 错误: release集合不存在")
            return
        
        # 快速检查集合和字段
        write_output("📊 正在检查数据库结构...")

        # 使用聚合管道快速统计
        pipeline = [
            {"$project": {
                "id": 1,
                "has_searchrule": {"$cond": [{"$ifNull": ["$searchRuleRelease", False]}, True, False]},
                "searchrule_type": {"$type": "$searchRuleRelease"}
            }},
            {"$group": {
                "_id": None,
                "total": {"$sum": 1},
                "has_searchrule": {"$sum": {"$cond": ["$has_searchrule", 1, 0]}},
                "types": {"$addToSet": "$searchrule_type"}
            }}
        ]

        try:
            stats = list(release_collection.aggregate(pipeline, allowDiskUse=True))
            if stats:
                stat = stats[0]
                write_output(f"📊 release表总记录数: {stat['total']:,}")
                write_output(f"📊 包含searchRuleRelease字段的记录数: {stat['has_searchrule']:,}")
                write_output(f"📊 searchRuleRelease字段类型: {stat['types']}")

                if stat['has_searchrule'] == 0:
                    write_output("⚠️ 警告: 没有找到包含searchRuleRelease字段的记录")
                    return
            else:
                write_output("⚠️ 无法获取统计信息，继续分析样本数据...")
        except Exception as e:
            write_output(f"⚠️ 统计查询失败: {e}，继续分析样本数据...")
        
        write_output(f"\n🔬 开始分析前 {SAMPLE_SIZE} 条包含searchRuleRelease字段的记录...")
        write_output("=" * 80)
        
        # 查询样本记录
        sample_records = release_collection.find(
            {"searchRuleRelease": {"$exists": True, "$ne": None, "$ne": ""}},
            {"id": 1, "searchRuleRelease": 1}
        ).limit(SAMPLE_SIZE)
        
        analyzed_count = 0
        barcode_found_count = 0
        structure_types = {}
        
        for record in sample_records:
            analyzed_count += 1
            record_id = record.get('id', 'unknown')
            searchrule_data = record.get('searchRuleRelease')
            
            write_output(f"\n📋 记录 #{analyzed_count} (ID: {record_id})")
            write_output("-" * 40)
            
            if searchrule_data is None:
                write_output("   searchRuleRelease: null")
                continue
            
            # 检查数据类型
            data_type = type(searchrule_data).__name__
            structure_types[data_type] = structure_types.get(data_type, 0) + 1
            write_output(f"   数据类型: {data_type}")
            
            try:
                # 如果是字符串，尝试解析为JSON
                if isinstance(searchrule_data, str):
                    write_output(f"   原始字符串长度: {len(searchrule_data)}")
                    if searchrule_data.strip():
                        try:
                            parsed_data = json.loads(searchrule_data)
                            write_output("   ✅ JSON解析成功")
                            searchrule_data = parsed_data
                        except json.JSONDecodeError as e:
                            write_output(f"   ❌ JSON解析失败: {e}")
                            write_output(f"   原始内容前100字符: {searchrule_data[:100]}")
                            continue
                    else:
                        write_output("   ⚠️ 空字符串")
                        continue
                
                # 如果是字典，分析结构
                if isinstance(searchrule_data, dict):
                    write_output("   📁 JSON结构分析:")
                    
                    # 显示顶级键
                    top_keys = list(searchrule_data.keys())
                    write_output(f"   顶级键: {top_keys}")
                    
                    # 检查baseInfo是否存在
                    if 'baseInfo' in searchrule_data:
                        base_info = searchrule_data['baseInfo']
                        write_output("   ✅ 找到baseInfo字段")
                        
                        if isinstance(base_info, dict):
                            base_info_keys = list(base_info.keys())
                            write_output(f"   baseInfo键: {base_info_keys}")
                            
                            # 检查barcode是否存在
                            if 'barcode' in base_info:
                                barcode_value = base_info['barcode']
                                write_output(f"   🎯 找到barcode: {barcode_value}")
                                barcode_found_count += 1
                            else:
                                write_output("   ❌ baseInfo中未找到barcode字段")
                        else:
                            write_output(f"   ⚠️ baseInfo不是字典类型: {type(base_info).__name__}")
                    else:
                        write_output("   ❌ 未找到baseInfo字段")
                    
                    # 显示完整结构（限制深度）
                    write_output("   📄 完整结构预览:")
                    try:
                        preview = json.dumps(searchrule_data, ensure_ascii=False, indent=2)
                        if len(preview) > 500:
                            preview = preview[:500] + "..."
                        write_output(f"   {preview}")
                    except Exception as e:
                        write_output(f"   ❌ 无法序列化为JSON: {e}")
                
                else:
                    write_output(f"   ⚠️ 非字典类型数据: {searchrule_data}")
                    
            except Exception as e:
                write_output(f"   ❌ 分析过程中出错: {e}")
        
        # 输出统计结果
        write_output("\n" + "=" * 80)
        write_output("📈 分析结果统计")
        write_output("=" * 80)
        write_output(f"✅ 分析记录数: {analyzed_count}")
        write_output(f"🎯 找到barcode的记录数: {barcode_found_count}")
        write_output(f"📊 barcode发现率: {(barcode_found_count/analyzed_count*100):.1f}%" if analyzed_count > 0 else "0%")
        
        write_output("\n📋 数据类型分布:")
        for data_type, count in structure_types.items():
            percentage = (count/analyzed_count*100) if analyzed_count > 0 else 0
            write_output(f"   {data_type}: {count} ({percentage:.1f}%)")
        
        # 计算处理时间
        processing_time = time.time() - start_time
        write_output(f"\n⏱️ 分析耗时: {processing_time:.2f} 秒")
        
    except Exception as e:
        write_output(f"❌ 分析过程中出错: {e}")
        raise
    finally:
        # 关闭数据库连接
        client.close()
        write_output("🔒 已关闭MongoDB连接")
        write_output(f"\n📄 详细分析结果已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    try:
        analyze_searchrule_structure()
        print(f"\n✅ 分析完成！详细结果请查看: {OUTPUT_FILE}")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        exit(1)
