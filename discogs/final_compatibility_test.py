#!/usr/bin/env python3
"""
最终兼容性测试脚本
测试优化后的api_release_补全器.py与csv_to_mongodb_importer.py的完整兼容性
"""

import sys
import os
import pandas as pd
from csv_to_mongodb_importer import CSVToMongoImporter

def test_full_csv_processing():
    """测试完整的CSV处理流程"""
    print("🚀 开始完整CSV处理兼容性测试...")
    print("=" * 60)
    
    csv_filename = 'test_compatibility.csv'
    
    if not os.path.exists(csv_filename):
        print(f"❌ 测试CSV文件不存在: {csv_filename}")
        return False
    
    try:
        # 1. 测试pandas读取
        print("📊 步骤1: 测试pandas读取CSV...")
        df = pd.read_csv(csv_filename)
        print(f"✅ 成功读取 {len(df)} 行数据")
        
        # 2. 测试导入器初始化
        print("\n📊 步骤2: 测试导入器初始化...")
        importer = CSVToMongoImporter(csv_file_path=csv_filename, test_mode=True)
        print("✅ 导入器初始化成功")
        
        # 3. 测试CSV文件处理
        print("\n📊 步骤3: 测试CSV文件处理...")
        try:
            # 使用导入器的process_csv_file方法
            result = importer.process_csv_file()

            if isinstance(result, bool):
                if result:
                    print("✅ CSV文件处理成功")
                    print("🎉 CSV处理兼容性测试通过！")
                    return True
                else:
                    print("❌ CSV文件处理失败")
                    return False
            else:
                # 如果返回的是字典（统计信息）
                print(f"✅ CSV文件处理成功，处理了 {result.get('processed', 0)} 条记录")
                print(f"   成功: {result.get('successful', 0)}")
                print(f"   失败: {result.get('failed', 0)}")
                print(f"   跳过: {result.get('skipped', 0)}")

                # 计算成功率
                total = result.get('processed', 0)
                successful = result.get('successful', 0)
                if total > 0:
                    success_rate = (successful / total) * 100
                    print(f"   成功率: {success_rate:.2f}%")

                    if success_rate >= 95:
                        print("🎉 CSV处理兼容性测试通过！")
                        return True
                    else:
                        print("⚠️ CSV处理成功率低于95%")
                        return False
                else:
                    print("❌ 没有处理任何记录")
                    return False
                
        except Exception as e:
            print(f"❌ CSV文件处理失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_individual_json_fields():
    """测试各个JSON字段的解析"""
    print("\n📊 详细JSON字段解析测试...")
    
    csv_filename = 'test_compatibility.csv'
    
    try:
        df = pd.read_csv(csv_filename)
        importer = CSVToMongoImporter(csv_file_path=csv_filename, test_mode=True)
        
        json_fields = ['artists', 'extra_artists', 'labels', 'companies', 
                      'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']
        
        field_results = {}
        
        for field in json_fields:
            field_results[field] = {'total': 0, 'success': 0, 'errors': []}
            
            for index, row in df.iterrows():
                if field in row and pd.notna(row[field]) and str(row[field]).strip():
                    field_results[field]['total'] += 1
                    try:
                        parsed = importer.parse_json_field(str(row[field]))
                        if parsed is not None:
                            field_results[field]['success'] += 1
                        else:
                            field_results[field]['errors'].append(f"行{index+1}: 返回None")
                    except Exception as e:
                        field_results[field]['errors'].append(f"行{index+1}: {str(e)}")
        
        # 输出结果
        print("\n📊 各字段解析结果:")
        overall_success = 0
        overall_total = 0
        
        for field, result in field_results.items():
            total = result['total']
            success = result['success']
            overall_total += total
            overall_success += success
            
            if total > 0:
                success_rate = (success / total) * 100
                status = "✅" if success_rate == 100 else "⚠️" if success_rate >= 90 else "❌"
                print(f"   {status} {field}: {success}/{total} ({success_rate:.1f}%)")
                
                if result['errors']:
                    for error in result['errors'][:2]:  # 只显示前2个错误
                        print(f"      - {error}")
            else:
                print(f"   ➖ {field}: 无数据")
        
        if overall_total > 0:
            overall_rate = (overall_success / overall_total) * 100
            print(f"\n📊 总体成功率: {overall_success}/{overall_total} ({overall_rate:.2f}%)")
            return overall_rate >= 95
        else:
            print("\n⚠️ 没有JSON字段数据进行测试")
            return False
            
    except Exception as e:
        print(f"❌ JSON字段测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 最终CSV兼容性测试")
    print("=" * 60)
    
    # 检查测试文件
    if not os.path.exists('test_compatibility.csv'):
        print("❌ 请先运行 test_csv_compatibility.py 生成测试文件")
        return
    
    # 执行测试
    test1_passed = test_individual_json_fields()
    test2_passed = test_full_csv_processing()
    
    print("\n" + "=" * 60)
    print("🏁 测试总结:")
    print(f"   JSON字段解析测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   完整CSV处理测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！CSV兼容性优化成功！")
        print("✅ api_release_补全器.py 生成的CSV文件与 csv_to_mongodb_importer.py 完全兼容")
    else:
        print("\n⚠️ 部分测试失败，需要进一步优化")

if __name__ == "__main__":
    main()
