#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证重复数据删除逻辑的正确性

功能：
1. 分析重复数据检测结果CSV文件
2. 验证删除的记录确实比保留的记录更新
3. 统计时间分布和删除逻辑的正确性
4. 生成验证报告

作者：AI Assistant
创建时间：2025-07-28
"""

import csv
import os
from datetime import datetime
from collections import defaultdict

# 文件路径
DUPLICATES_CSV = 'release_new_duplicate_results/release_new_duplicates_to_delete.csv'
VERIFICATION_REPORT = 'release_new_duplicate_results/deletion_logic_verification.txt'

def parse_datetime(date_str):
    """解析日期时间字符串"""
    try:
        if date_str and date_str.strip():
            return datetime.strptime(date_str.strip(), '%Y-%m-%d %H:%M:%S.%f')
        return None
    except ValueError:
        try:
            return datetime.strptime(date_str.strip(), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return None

def analyze_deletion_logic():
    """分析删除逻辑的正确性"""
    print("🔍 开始验证重复数据删除逻辑...")
    
    if not os.path.exists(DUPLICATES_CSV):
        print(f"❌ 找不到CSV文件: {DUPLICATES_CSV}")
        return
    
    # 统计数据
    total_records = 0
    correct_deletions = 0
    incorrect_deletions = 0
    time_analysis = defaultdict(list)
    
    # 按重复组分析
    groups_analysis = defaultdict(list)
    
    with open(DUPLICATES_CSV, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            total_records += 1
            
            # 解析时间
            delete_time = parse_datetime(row['created_at'])
            keep_time = parse_datetime(row['keep_record_created_at'])
            
            if delete_time and keep_time:
                # 检查删除逻辑是否正确（删除的应该比保留的更新）
                if delete_time > keep_time:
                    correct_deletions += 1
                    time_analysis['correct'].append({
                        'id': row['original_id'],
                        'delete_time': delete_time,
                        'keep_time': keep_time,
                        'diff_hours': (delete_time - keep_time).total_seconds() / 3600
                    })
                else:
                    incorrect_deletions += 1
                    time_analysis['incorrect'].append({
                        'id': row['original_id'],
                        'delete_time': delete_time,
                        'keep_time': keep_time,
                        'diff_hours': (keep_time - delete_time).total_seconds() / 3600
                    })
                
                # 按重复组分析
                groups_analysis[row['original_id']].append({
                    'y_id': row['y_id'],
                    'created_at': delete_time,
                    'keep_record_created_at': keep_time,
                    'is_correct': delete_time > keep_time
                })
    
    # 生成验证报告
    with open(VERIFICATION_REPORT, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("重复数据删除逻辑验证报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 总体统计
        f.write("📊 总体统计\n")
        f.write("-" * 40 + "\n")
        f.write(f"总删除记录数: {total_records:,}\n")
        f.write(f"正确删除记录数: {correct_deletions:,}\n")
        f.write(f"错误删除记录数: {incorrect_deletions:,}\n")
        f.write(f"正确率: {(correct_deletions/total_records*100):.2f}%\n\n")
        
        # 时间差分析
        if time_analysis['correct']:
            correct_diffs = [item['diff_hours'] for item in time_analysis['correct']]
            f.write("⏰ 正确删除的时间差分析\n")
            f.write("-" * 40 + "\n")
            f.write(f"平均时间差: {sum(correct_diffs)/len(correct_diffs):.2f} 小时\n")
            f.write(f"最小时间差: {min(correct_diffs):.2f} 小时\n")
            f.write(f"最大时间差: {max(correct_diffs):.2f} 小时\n\n")
        
        # 重复组分析
        f.write("🔍 重复组详细分析\n")
        f.write("-" * 40 + "\n")
        
        # 显示前10个重复组的详细信息
        group_count = 0
        for group_id, records in list(groups_analysis.items())[:10]:
            group_count += 1
            f.write(f"\n重复组 {group_id}:\n")
            
            # 按时间排序显示
            sorted_records = sorted(records, key=lambda x: x['created_at'])
            for i, record in enumerate(sorted_records):
                status = "✅ 正确" if record['is_correct'] else "❌ 错误"
                f.write(f"  记录{i+1}: {record['y_id']} - {record['created_at']} ({status})\n")
            
            f.write(f"  保留记录时间: {sorted_records[0]['keep_record_created_at']}\n")
        
        if len(groups_analysis) > 10:
            f.write(f"\n... 还有 {len(groups_analysis) - 10} 个重复组\n")
        
        # 错误案例分析
        if time_analysis['incorrect']:
            f.write("\n❌ 错误删除案例分析\n")
            f.write("-" * 40 + "\n")
            for item in time_analysis['incorrect'][:5]:  # 显示前5个错误案例
                f.write(f"ID {item['id']}: 删除时间 {item['delete_time']} 早于保留时间 {item['keep_time']}\n")
        
        f.write("\n" + "=" * 80 + "\n")
    
    # 控制台输出
    print(f"📊 验证完成！")
    print(f"总删除记录数: {total_records:,}")
    print(f"正确删除记录数: {correct_deletions:,}")
    print(f"错误删除记录数: {incorrect_deletions:,}")
    print(f"正确率: {(correct_deletions/total_records*100):.2f}%")
    
    if incorrect_deletions > 0:
        print(f"⚠️  发现 {incorrect_deletions} 条错误删除记录")
    else:
        print("✅ 所有删除记录都符合逻辑（删除较新的，保留较早的）")
    
    print(f"📄 详细验证报告已保存到: {VERIFICATION_REPORT}")

def show_sample_data():
    """显示样本数据"""
    print("\n📋 样本数据分析:")
    print("-" * 60)
    
    if not os.path.exists(DUPLICATES_CSV):
        print(f"❌ 找不到CSV文件: {DUPLICATES_CSV}")
        return
    
    with open(DUPLICATES_CSV, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        print("前5条删除记录的时间对比:")
        print("格式: ID | 删除记录时间 | 保留记录时间 | 时间差")
        print("-" * 60)
        
        count = 0
        for row in reader:
            if count >= 5:
                break
            
            delete_time = parse_datetime(row['created_at'])
            keep_time = parse_datetime(row['keep_record_created_at'])
            
            if delete_time and keep_time:
                diff_hours = (delete_time - keep_time).total_seconds() / 3600
                status = "✅" if delete_time > keep_time else "❌"
                
                print(f"{row['original_id']} | {delete_time} | {keep_time} | {diff_hours:.1f}h {status}")
            
            count += 1

if __name__ == "__main__":
    print("🔍 重复数据删除逻辑验证工具")
    print("=" * 60)
    
    # 显示样本数据
    show_sample_data()
    
    # 执行完整验证
    analyze_deletion_logic()
