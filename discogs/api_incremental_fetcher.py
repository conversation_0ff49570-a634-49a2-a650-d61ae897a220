#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 增量数据获取器

功能：
1. 连接到MongoDB数据库，查询现有release_new集合中最大的id值
2. 从最大id+1开始，依次调用Discogs API获取release数据
3. 对于成功返回的数据，进行格式化处理后保存到CSV文件
4. 对于返回404错误的请求，跳过并继续下一个id
5. 当连续遇到20次404错误时，停止脚本执行

特性：
- API频率限制处理（每分钟60次请求）
- 网络超时重试机制（最多3次）
- 连续404计数器和自动停止
- 进度跟踪和断点续传
- 详细的日志记录
- CSV输出格式与现有项目保持一致

作者：AI Assistant
创建时间：2025-07-29
"""

import os
import sys
import time
import json
import csv
import logging
import argparse
from datetime import datetime, timezone
from pymongo import MongoClient
from typing import Dict, Optional

# 尝试导入requests
try:
    import requests
except ImportError:
    print("❌ 缺少 requests 模块")
    print("请安装: pip3 install requests")
    sys.exit(1)

# 导入现有的核心组件
try:
    from api_release_补全器 import (
        DiscogsAPIClient,
        convert_api_response_to_document,
        write_releases_to_csv,
        get_next_yid_counter
    )
    print("✅ 成功导入 api_release_补全器 模块")
except ImportError as e:
    print(f"❌ 导入 api_release_补全器 失败: {e}")
    print("将使用内置的简化版本...")

    # 内置简化的API客户端
    class DiscogsAPIClient:
        def __init__(self):
            self.session = requests.Session()
            self.session.headers.update({
                'User-Agent': 'DiscogsIncremental/1.0'
            })
            self.last_request_time = 0

        def get_release(self, release_id):
            import time
            # 频率控制
            elapsed = time.time() - self.last_request_time
            if elapsed < 1.0:
                time.sleep(1.0 - elapsed)

            url = f"https://api.discogs.com/releases/{release_id}"

            for attempt in range(3):
                try:
                    self.last_request_time = time.time()
                    response = self.session.get(url, timeout=30)

                    if response.status_code == 200:
                        return response.json()
                    elif response.status_code == 404:
                        return None
                    elif response.status_code == 429:
                        wait_times = [5, 15, 60]
                        wait_time = wait_times[min(attempt, len(wait_times) - 1)]
                        print(f"⏳ API频率限制，等待 {wait_time} 秒...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print(f"⚠️ API返回状态码 {response.status_code}")
                        return None

                except Exception as e:
                    print(f"❌ 请求异常: {e}")
                    if attempt < 2:
                        time.sleep(2 ** attempt)

            return None

    def convert_api_response_to_document(data):
        """简化的数据转换"""
        return {
            'id': data.get('id'),
            'title': data.get('title', ''),
            'year': data.get('year', ''),
            'country': data.get('country', ''),
            'created_at': datetime.now().isoformat()
        }

    def write_releases_to_csv(releases, filename):
        """简化的CSV写入"""
        import csv
        if not releases:
            return

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = releases[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(releases)

    def get_next_yid_counter():
        """简化的ID计数器"""
        return 1

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# 处理配置
MAX_CONSECUTIVE_404 = int(os.getenv('MAX_CONSECUTIVE_404', '20'))  # 连续404停止阈值
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '100'))  # CSV批量写入大小
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，0表示无限制

# 文件路径
PROGRESS_FILE = 'api_incremental_progress.json'
LOG_FILE = 'api_incremental_log.txt'
CSV_OUTPUT_FILE = f'api_releases_补全_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        logger.info(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        raise


def get_max_id_from_database(db):
    """获取数据库中最大的ID值"""
    try:
        collection = db[COLLECTION_NAME]
        
        # 使用聚合管道获取最大ID（数字类型）
        pipeline = [
            {
                '$addFields': {
                    'id_numeric': {
                        '$toInt': '$id'
                    }
                }
            },
            {
                '$sort': {
                    'id_numeric': -1
                }
            },
            {
                '$limit': 1
            },
            {
                '$project': {
                    'id': 1,
                    'id_numeric': 1
                }
            }
        ]
        
        result = list(collection.aggregate(pipeline))
        if result:
            max_id = result[0]['id_numeric']
            logger.info(f"📊 数据库中最大ID: {max_id}")
            return max_id
        else:
            logger.info("📊 数据库为空，从ID 1开始")
            return 0
            
    except Exception as e:
        logger.error(f"❌ 获取最大ID失败: {e}")
        return 0


def save_progress(progress_data):
    """保存进度到JSON文件"""
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
        logger.debug(f"💾 进度已保存到 {PROGRESS_FILE}")
    except Exception as e:
        logger.error(f"❌ 保存进度失败: {e}")


def load_progress():
    """加载进度文件"""
    try:
        if os.path.exists(PROGRESS_FILE):
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                progress = json.load(f)
            logger.info(f"📂 从 {PROGRESS_FILE} 加载进度")
            return progress
        else:
            logger.info("📂 未找到进度文件，从头开始")
            return None
    except Exception as e:
        logger.error(f"❌ 加载进度失败: {e}")
        return None


class IncrementalFetcher:
    """增量数据获取器"""
    
    def __init__(self, db):
        self.db = db
        self.api_client = DiscogsAPIClient()
        self.collection = db[COLLECTION_NAME]
        
        # 进度跟踪
        self.progress = {
            'current_id': 0,
            'processed_count': 0,
            'successful_count': 0,
            'skipped_404_count': 0,
            'error_count': 0,
            'consecutive_404_count': 0,
            'start_time': datetime.now().isoformat(),
            'last_update_time': datetime.now().isoformat()
        }
        
        # 批量处理
        self.batch_docs = []
        self.yid_counter = get_next_yid_counter(self.collection)
        
        logger.info(f"🚀 增量获取器初始化完成，下一个YRD编号: YRD{self.yid_counter}")
    
    def load_existing_progress(self):
        """加载现有进度"""
        existing_progress = load_progress()
        if existing_progress:
            self.progress.update(existing_progress)
            logger.info(f"📂 恢复进度: 当前ID={self.progress['current_id']}, "
                       f"已处理={self.progress['processed_count']}")
            return True
        return False
    
    def start_from_max_id(self):
        """从数据库最大ID开始"""
        max_id = get_max_id_from_database(self.db)
        self.progress['current_id'] = max_id + 1
        logger.info(f"🎯 从ID {self.progress['current_id']} 开始增量获取")
    
    def process_single_id(self, release_id):
        """处理单个ID"""
        try:
            # 记录API调用开始时间
            api_start_time = time.time()

            # 调用API
            api_data = self.api_client.get_release(release_id)

            # 记录API调用结束时间
            api_end_time = time.time()
            api_duration = api_end_time - api_start_time

            # 如果API调用时间过长，记录警告
            if api_duration > 30:
                logger.warning(f"⏰ ID {release_id} API调用耗时过长: {api_duration:.2f}秒")

            if api_data is None:
                # 404 - ID不存在
                self.progress['skipped_404_count'] += 1
                self.progress['consecutive_404_count'] += 1
                logger.debug(f"⏭️ ID {release_id} 不存在 (404), 耗时: {api_duration:.2f}秒")

                # 检查连续404停止条件
                if self.progress['consecutive_404_count'] >= MAX_CONSECUTIVE_404:
                    logger.warning(f"🛑 连续遇到 {MAX_CONSECUTIVE_404} 次404错误，停止处理")
                    return False

            elif api_data is False:
                # API请求失败
                self.progress['error_count'] += 1
                self.progress['consecutive_404_count'] = 0  # 重置连续404计数
                logger.warning(f"❌ ID {release_id} API请求失败, 耗时: {api_duration:.2f}秒")
                
            else:
                # 成功获取数据
                self.progress['consecutive_404_count'] = 0  # 重置连续404计数
                
                y_id = f"YRD{self.yid_counter}"
                doc = convert_api_response_to_document(api_data, y_id, self.db)
                
                if doc:
                    self.batch_docs.append(doc)
                    self.yid_counter += 1
                    self.progress['successful_count'] += 1
                    logger.debug(f"✅ ID {release_id} 处理成功 -> {y_id}")
                    
                    # 批量写入CSV
                    if len(self.batch_docs) >= BATCH_SIZE:
                        self.write_batch_to_csv()
                else:
                    self.progress['error_count'] += 1
                    logger.warning(f"❌ ID {release_id} 数据转换失败")
            
            self.progress['processed_count'] += 1
            self.progress['current_id'] = release_id
            self.progress['last_update_time'] = datetime.now().isoformat()
            
            return True
            
        except Exception as e:
            self.progress['error_count'] += 1
            logger.error(f"❌ 处理ID {release_id} 时发生异常: {e}")
            return True  # 继续处理下一个ID
    
    def write_batch_to_csv(self):
        """写入批量数据到CSV"""
        if self.batch_docs:
            write_releases_to_csv(self.batch_docs, CSV_OUTPUT_FILE, append_mode=True)
            logger.info(f"💾 已写入 {len(self.batch_docs)} 条记录到 {CSV_OUTPUT_FILE}")
            self.batch_docs = []
    
    def display_progress(self):
        """显示进度信息"""
        elapsed_time = (datetime.now() - datetime.fromisoformat(self.progress['start_time'])).total_seconds()
        speed = self.progress['processed_count'] / elapsed_time if elapsed_time > 0 else 0
        
        logger.info(f"📊 进度报告:")
        logger.info(f"   当前ID: {self.progress['current_id']}")
        logger.info(f"   已处理: {self.progress['processed_count']:,}")
        logger.info(f"   成功获取: {self.progress['successful_count']:,}")
        logger.info(f"   404跳过: {self.progress['skipped_404_count']:,}")
        logger.info(f"   错误: {self.progress['error_count']:,}")
        logger.info(f"   连续404: {self.progress['consecutive_404_count']}")
        logger.info(f"   处理速度: {speed:.2f} ID/秒")
        logger.info(f"   运行时间: {elapsed_time/60:.1f} 分钟")
    
    def run(self, resume_from_progress=True):
        """运行增量获取"""
        logger.info("🚀 开始增量数据获取")
        logger.info("=" * 60)
        logger.info(f"📊 最大连续404: {MAX_CONSECUTIVE_404}")
        logger.info(f"📦 批量写入大小: {BATCH_SIZE}")
        logger.info(f"📁 CSV输出文件: {CSV_OUTPUT_FILE}")
        logger.info(f"📊 期望API频率: 1 请求/秒")

        # 加载进度或从最大ID开始
        if resume_from_progress and self.load_existing_progress():
            logger.info("📂 从上次进度继续")
        else:
            self.start_from_max_id()

        logger.info(f"🎯 开始从ID {self.progress['current_id']} 进行增量获取")
        
        try:
            while True:
                current_id = self.progress['current_id']
                
                # 检查最大记录数限制
                if MAX_RECORDS > 0 and self.progress['processed_count'] >= MAX_RECORDS:
                    logger.info(f"🎯 已达到最大处理记录数限制 ({MAX_RECORDS})，停止处理")
                    break
                
                # 处理当前ID
                should_continue = self.process_single_id(current_id)
                if not should_continue:
                    break
                
                # 定期保存进度和显示状态
                if self.progress['processed_count'] % 100 == 0:
                    save_progress(self.progress)
                    self.display_progress()
                
                # 移动到下一个ID
                self.progress['current_id'] += 1
                
        except KeyboardInterrupt:
            logger.info("🛑 收到中断信号，正在保存进度...")
        except Exception as e:
            logger.error(f"❌ 运行过程中发生错误: {e}")
        finally:
            # 写入剩余的批量数据
            self.write_batch_to_csv()
            
            # 保存最终进度
            self.progress['end_time'] = datetime.now().isoformat()
            save_progress(self.progress)
            
            # 显示最终统计
            self.display_final_stats()
    
    def display_final_stats(self):
        """显示最终统计信息"""
        logger.info("\n" + "="*60)
        logger.info("📊 最终统计结果")
        logger.info("="*60)
        logger.info(f"✅ 总处理数量: {self.progress['processed_count']:,}")
        logger.info(f"✅ 成功获取: {self.progress['successful_count']:,}")
        logger.info(f"✅ 404跳过: {self.progress['skipped_404_count']:,}")
        logger.info(f"✅ 错误数量: {self.progress['error_count']:,}")
        logger.info(f"✅ CSV输出文件: {CSV_OUTPUT_FILE}")
        logger.info(f"✅ 进度文件: {PROGRESS_FILE}")
        logger.info("="*60)


def validate_environment():
    """验证运行环境"""
    logger.info("🔍 验证运行环境...")

    # 检查数据库连接
    try:
        client, db = connect_to_mongodb()
        collection = db[COLLECTION_NAME]
        count = collection.count_documents({})
        logger.info("✅ 数据库连接成功，%s集合有 %s 条记录", COLLECTION_NAME, f"{count:,}")
        client.close()
    except Exception as e:
        logger.error("❌ 数据库连接失败: %s", e)
        return False

    # 检查API客户端
    try:
        DiscogsAPIClient()  # 只是测试初始化
        logger.info("✅ API客户端初始化成功")
    except Exception as e:
        logger.error("❌ API客户端初始化失败: %s", e)
        return False

    return True


def display_startup_info():
    """显示启动信息"""
    logger.info("🚀 Discogs API 增量数据获取器")
    logger.info("="*60)
    logger.info("📊 配置信息:")
    logger.info("   数据库: %s", DB_NAME)
    logger.info("   集合: %s", COLLECTION_NAME)
    logger.info("   连续404停止阈值: %s", MAX_CONSECUTIVE_404)
    logger.info("   批量写入大小: %s", BATCH_SIZE)
    max_records_display = MAX_RECORDS if MAX_RECORDS > 0 else '无限制'
    logger.info("   最大处理记录数: %s", max_records_display)
    logger.info("   CSV输出文件: %s", CSV_OUTPUT_FILE)
    logger.info("   进度文件: %s", PROGRESS_FILE)
    logger.info("   日志文件: %s", LOG_FILE)
    logger.info("="*60)


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Discogs API 增量数据获取器')
    parser.add_argument('--resume', '-r', action='store_true',
                       help='从上次进度继续（如果存在进度文件）')
    parser.add_argument('--fresh', '-f', action='store_true',
                       help='强制从数据库最大ID开始（忽略进度文件）')
    parser.add_argument('--max-records', '-m', type=int, default=0,
                       help='最大处理记录数（0表示无限制）')
    args = parser.parse_args()

    # 设置最大记录数
    if args.max_records > 0:
        global MAX_RECORDS
        MAX_RECORDS = args.max_records

    display_startup_info()

    # 验证运行环境
    if not validate_environment():
        logger.error("❌ 环境验证失败，程序退出")
        sys.exit(1)

    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 创建增量获取器
        fetcher = IncrementalFetcher(db)

        # 确定是否从进度继续
        if args.fresh:
            resume_from_progress = False
            logger.info("🔄 强制从数据库最大ID开始")
        elif args.resume:
            resume_from_progress = True
            logger.info("📂 尝试从上次进度继续")
        else:
            # 默认行为：如果有进度文件就继续，没有就从最大ID开始
            resume_from_progress = os.path.exists(PROGRESS_FILE)
            if resume_from_progress:
                logger.info("📂 发现进度文件，从上次进度继续")
            else:
                logger.info("🆕 没有进度文件，从数据库最大ID开始")

        # 运行增量获取
        fetcher.run(resume_from_progress=resume_from_progress)

        # 关闭数据库连接
        client.close()

        logger.info("🎉 程序执行完成")

    except Exception as e:
        logger.error("❌ 程序执行失败: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    # IDE直接运行模式：设置测试参数

    # 检查是否有命令行参数，如果没有则使用默认测试参数
    if len(sys.argv) == 1:
        print("🎯 IDE直接运行模式：使用默认测试参数")
        print("📊 参数：--max-records 10 (获取10条记录用于测试)")
        print("💡 如需修改参数，请编辑脚本末尾的 sys.argv 设置")
        print("=" * 50)

        # 设置默认测试参数
        sys.argv = [
            'api_incremental_fetcher.py',
            '--max-records', '10',  # 只获取10条记录用于测试
            '--fresh'  # 从数据库最大ID开始
        ]

    main()
