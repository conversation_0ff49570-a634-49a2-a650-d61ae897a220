#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终CSV导入测试脚本
验证修复后的CSV解析器能否正确处理完整的CSV文件
"""

import os
import sys
import pandas as pd
import csv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from csv_to_mongodb_importer import CSVToMongoImporter

def test_pandas_reading():
    """测试pandas读取CSV文件"""
    print("🧪 测试pandas读取CSV文件...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        # 使用与导入器相同的参数读取CSV
        df = pd.read_csv(
            csv_file,
            encoding='utf-8',
            quoting=csv.QUOTE_ALL,
            na_filter=False,
            dtype=str
        )
        
        print(f"✅ 成功读取CSV文件")
        print(f"📊 总行数: {len(df)}")
        print(f"📋 列数: {len(df.columns)}")
        
        # 检查是否有包含换行符的notes字段
        notes_with_newlines = 0
        for i, row in df.iterrows():
            if row['notes'] and '\n' in str(row['notes']):
                notes_with_newlines += 1
                if notes_with_newlines <= 3:  # 只显示前3个
                    print(f"📝 发现包含换行符的notes (行{i+1}): {repr(row['notes'][:100])}")
        
        print(f"📈 包含换行符的notes字段数量: {notes_with_newlines}")
        return True
        
    except Exception as e:
        print(f"❌ pandas读取失败: {e}")
        return False

def test_document_conversion():
    """测试文档转换功能"""
    print("\n🧪 测试文档转换功能...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        # 创建导入器实例
        importer = CSVToMongoImporter(csv_file, test_mode=True)
        
        # 读取CSV文件
        df = pd.read_csv(
            csv_file,
            encoding='utf-8',
            quoting=csv.QUOTE_ALL,
            na_filter=False,
            dtype=str
        )
        
        success_count = 0
        json_parse_errors = 0
        
        # 测试前20行的转换
        for i in range(min(20, len(df))):
            row = df.iloc[i]
            row_dict = row.to_dict()
            
            try:
                doc = importer.convert_csv_row_to_document(row_dict)
                if doc:
                    success_count += 1
                    # 检查关键字段
                    if doc.get('id') and doc.get('y_id') and doc.get('title'):
                        print(f"✅ 行 {i+1} 转换成功 - ID: {doc['id']}, Title: {doc['title'][:30]}...")
                    else:
                        print(f"⚠️ 行 {i+1} 转换成功但缺少关键字段")
                else:
                    print(f"❌ 行 {i+1} 转换失败")
            except Exception as e:
                print(f"❌ 行 {i+1} 转换异常: {e}")
        
        print(f"\n📊 转换测试结果: {success_count}/20 行转换成功")
        
        if success_count >= 18:  # 允许少量JSON解析错误
            print("✅ 文档转换测试通过")
            return True
        else:
            print("❌ 文档转换测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 文档转换测试异常: {e}")
        return False

def test_memory_efficiency():
    """测试内存效率"""
    print("\n🧪 测试内存效率...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"📊 初始内存使用: {initial_memory:.1f} MB")
        
        # 创建导入器并读取文件
        importer = CSVToMongoImporter(csv_file, test_mode=True)
        df = pd.read_csv(
            csv_file,
            encoding='utf-8',
            quoting=csv.QUOTE_ALL,
            na_filter=False,
            dtype=str
        )
        
        current_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = current_memory - initial_memory
        
        print(f"📊 读取后内存使用: {current_memory:.1f} MB")
        print(f"📈 内存增长: {memory_increase:.1f} MB")
        
        # 对于760行数据，内存增长应该在合理范围内（<100MB）
        if memory_increase < 100:
            print("✅ 内存效率测试通过")
            return True
        else:
            print("⚠️ 内存使用较高，但仍可接受")
            return True
            
    except ImportError:
        print("⚠️ psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"❌ 内存效率测试异常: {e}")
        return True  # 不因为内存测试失败而影响整体结果

def main():
    """主测试函数"""
    print("🚀 开始最终CSV导入测试")
    print("=" * 60)
    
    tests = [
        ("pandas CSV读取", test_pandas_reading),
        ("文档转换功能", test_document_conversion),
        ("内存效率", test_memory_efficiency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 最终测试总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！CSV解析器已成功修复")
        print("✅ 可以安全地使用修复后的csv_to_mongodb_importer.py处理CSV文件")
        return True
    elif passed >= total - 1:
        print("✅ 大部分测试通过，CSV解析器基本可用")
        return True
    else:
        print("⚠️ 多个测试失败，需要进一步调试")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
