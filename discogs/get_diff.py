#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discogs 数据比较统一接口
提供简单的API来获取不同类型的diff文件
"""

import os
import sys
import subprocess
from pathlib import Path


class DiffAPI:
    """Discogs数据比较统一接口"""
    
    def __init__(self):
        """初始化API配置"""
        self.base_dir = Path(__file__).parent
        
        # 配置各类型的比较工具和输出文件
        self.config = {
            'release': {
                'script_dir': 'release_comparison',
                'script_name': 'compare_releases.py',
                'output_file': 'release_comparison_diff.csv',
                'description': 'Release数据比较'
            },
            'master': {
                'script_dir': 'master_comparison', 
                'script_name': 'compare_master.py',
                'output_file': 'master_comparison_diff.csv',
                'description': 'Master数据比较'
            },
            'artists': {
                'script_dir': 'artists_comparison',
                'script_name': 'compare_artists.py', 
                'output_file': 'artists_comparison_diff.csv',
                'description': 'Artists数据比较'
            },
            'label': {
                'script_dir': 'label_comparison',
                'script_name': 'compare_labels.py',
                'output_file': 'label_comparison_diff.csv', 
                'description': 'Label数据比较'
            }
        }
    
    def get_diff(self, data_type, force_regenerate=False):
        """
        获取指定类型的diff文件
        
        Args:
            data_type (str): 数据类型 ('release', 'master', 'artists', 'label')
            force_regenerate (bool): 是否强制重新生成diff文件
            
        Returns:
            str: diff文件的绝对路径，如果失败返回None
        """
        # 验证数据类型
        if data_type not in self.config:
            print(f"错误: 不支持的数据类型 '{data_type}'")
            print(f"支持的类型: {', '.join(self.config.keys())}")
            return None
        
        config = self.config[data_type]
        script_dir = self.base_dir / config['script_dir']
        output_file = script_dir / config['output_file']
        
        # 检查diff文件是否已存在
        if output_file.exists() and not force_regenerate:
            print(f"找到现有的{config['description']}diff文件: {output_file}")
            return str(output_file)
        
        # 检查比较脚本是否存在
        script_path = script_dir / config['script_name']
        if not script_path.exists():
            print(f"错误: 比较脚本不存在: {script_path}")
            return None
        
        # 运行比较脚本生成diff文件
        print(f"正在生成{config['description']}diff文件...")
        try:
            # 切换到脚本目录并运行
            result = subprocess.run(
                [sys.executable, config['script_name']], 
                cwd=script_dir,
                capture_output=True,
                text=True,
                timeout=7200  # 2小时超时
            )
            
            if result.returncode == 0:
                if output_file.exists():
                    print(f"✅ {config['description']}diff文件生成成功: {output_file}")
                    return str(output_file)
                else:
                    print(f"❌ 脚本执行成功但未找到输出文件: {output_file}")
                    return None
            else:
                print(f"❌ 脚本执行失败:")
                print(f"错误输出: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"❌ 脚本执行超时（超过2小时）")
            return None
        except Exception as e:
            print(f"❌ 执行脚本时出错: {e}")
            return None
    
    def list_available_types(self):
        """列出所有可用的数据类型"""
        print("可用的数据类型:")
        for data_type, config in self.config.items():
            print(f"  - {data_type}: {config['description']}")
    
    def check_diff_status(self):
        """检查所有diff文件的状态"""
        print("Diff文件状态检查:")
        print("-" * 50)
        
        for data_type, config in self.config.items():
            script_dir = self.base_dir / config['script_dir']
            output_file = script_dir / config['output_file']
            
            if output_file.exists():
                # 获取文件大小和修改时间
                size_mb = output_file.stat().st_size / 1024 / 1024
                mtime = output_file.stat().st_mtime
                import datetime
                mtime_str = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                
                print(f"✅ {data_type}: 存在 ({size_mb:.2f}MB, 修改时间: {mtime_str})")
            else:
                print(f"❌ {data_type}: 不存在")


def main():
    """命令行接口"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python get_diff.py <type>                    # 获取指定类型的diff文件")
        print("  python get_diff.py <type> --force            # 强制重新生成diff文件")
        print("  python get_diff.py --list                    # 列出所有可用类型")
        print("  python get_diff.py --status                  # 检查所有diff文件状态")
        print()
        print("示例:")
        print("  python get_diff.py release                   # 获取release的diff文件")
        print("  python get_diff.py master --force            # 强制重新生成master的diff文件")
        return
    
    api = DiffAPI()
    
    if sys.argv[1] == '--list':
        api.list_available_types()
        return
    
    if sys.argv[1] == '--status':
        api.check_diff_status()
        return
    
    data_type = sys.argv[1]
    force_regenerate = '--force' in sys.argv
    
    # 获取diff文件
    diff_file = api.get_diff(data_type, force_regenerate)
    
    if diff_file:
        print(f"\n📁 Diff文件路径: {diff_file}")
    else:
        print(f"\n❌ 获取{data_type}的diff文件失败")
        sys.exit(1)


if __name__ == '__main__':
    main()
