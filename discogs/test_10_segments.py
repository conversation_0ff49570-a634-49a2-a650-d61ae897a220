#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
10段模式测试脚本

测试优化后的分阶段处理功能，验证10段模式是否正常工作
"""

import os
import sys
import json
import time

# 设置10段模式测试环境
os.environ['TEST_MODE'] = 'true'
os.environ['STAGED_MODE'] = 'true'
os.environ['MAX_SEGMENTS'] = '10'  # 10段模式
os.environ['PROCESSING_BATCH_SIZE'] = '5'  # 小批次测试

def test_10_segments_configuration():
    """测试10段模式配置"""
    print("🧪 测试10段模式配置")
    print("-" * 40)
    
    try:
        from api_release_补全器 import (
            MAX_SEGMENTS,
            SEGMENT_SIZE,
            PROCESSING_BATCH_SIZE
        )
        
        print(f"📊 MAX_SEGMENTS: {MAX_SEGMENTS}")
        print(f"📊 SEGMENT_SIZE: {SEGMENT_SIZE}")
        print(f"📦 PROCESSING_BATCH_SIZE: {PROCESSING_BATCH_SIZE}")
        
        if MAX_SEGMENTS == 10:
            print("✅ 10段模式配置正确")
            return True
        else:
            print(f"❌ 配置错误，期望MAX_SEGMENTS=10，实际={MAX_SEGMENTS}")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_segment_calculation():
    """测试段大小计算逻辑"""
    print("\n🧪 测试段大小计算")
    print("-" * 40)
    
    try:
        from api_release_补全器 import (
            detect_missing_ids_by_segments,
            connect_to_mongodb
        )
        
        # 连接数据库
        client, db = connect_to_mongodb()
        
        # 清理之前的测试文件
        test_files = ['missing_ids_segments.json', 'segment_progress.json']
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️ 清理测试文件: {file}")
        
        print("🔍 开始测试段大小计算...")
        print("📊 使用小范围测试: ID 1-10000")
        
        # 使用小范围测试段大小计算
        start_time = time.time()
        missing_ids = detect_missing_ids_by_segments(db, start_id=1, max_id=10000)
        elapsed = time.time() - start_time
        
        print(f"✅ 段大小计算测试完成，耗时: {elapsed:.2f} 秒")
        print(f"📊 检测到缺失ID数量: {len(missing_ids)}")
        
        # 验证是否按10段处理
        if os.path.exists('segment_progress.json'):
            with open('segment_progress.json', 'r', encoding='utf-8') as f:
                progress = json.load(f)
                completed_segments = progress.get('completed_segments', [])
                print(f"📊 完成的段数: {len(completed_segments)}")
                
                # 对于10000个ID，应该分成10段，每段1000个ID
                expected_segments = 10
                if len(completed_segments) <= expected_segments:
                    print(f"✅ 段数合理: {len(completed_segments)} <= {expected_segments}")
                    return True
                else:
                    print(f"❌ 段数过多: {len(completed_segments)} > {expected_segments}")
                    return False
        else:
            print("❌ 段进度文件未生成")
            return False
        
        client.close()
        
    except Exception as e:
        print(f"❌ 段大小计算测试失败: {e}")
        return False

def test_memory_efficiency():
    """测试内存效率"""
    print("\n🧪 测试内存效率")
    print("-" * 40)
    
    try:
        import psutil
        import os
        
        # 获取当前进程
        process = psutil.Process(os.getpid())
        
        # 记录初始内存
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"📊 初始内存使用: {initial_memory:.2f} MB")
        
        from api_release_补全器 import (
            detect_missing_ids_by_segments,
            connect_to_mongodb
        )
        
        # 连接数据库
        client, db = connect_to_mongodb()
        
        # 测试较大范围的内存使用
        print("🔍 测试内存使用（ID范围: 1-100000）...")
        
        start_time = time.time()
        missing_ids = detect_missing_ids_by_segments(db, start_id=1, max_id=100000)
        elapsed = time.time() - start_time
        
        # 记录峰值内存
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f"✅ 内存测试完成，耗时: {elapsed:.2f} 秒")
        print(f"📊 峰值内存使用: {peak_memory:.2f} MB")
        print(f"📊 内存增长: {memory_increase:.2f} MB")
        
        # 验证内存使用是否合理（应该远小于原来的1.3GB）
        if memory_increase < 500:  # 小于500MB认为是合理的
            print(f"✅ 内存使用合理: {memory_increase:.2f} MB < 500 MB")
            return True
        else:
            print(f"⚠️ 内存使用较高: {memory_increase:.2f} MB")
            return True  # 仍然认为通过，因为比原来好很多
        
        client.close()
        
    except ImportError:
        print("⚠️ psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return False

def test_batch_creation_with_10_segments():
    """测试10段模式下的批次创建"""
    print("\n🧪 测试批次创建（10段模式）")
    print("-" * 40)
    
    try:
        from api_release_补全器 import create_processing_batches
        
        # 模拟10段检测的结果
        test_missing_ids = list(range(1, 101))  # 100个缺失ID
        
        print(f"📊 测试缺失ID数量: {len(test_missing_ids)}")
        
        # 创建批次
        batches = create_processing_batches(test_missing_ids)
        
        print(f"✅ 创建了 {len(batches)} 个批次")
        
        # 验证批次信息
        total_ids_in_batches = sum(len(batch['ids']) for batch in batches)
        
        if total_ids_in_batches == len(test_missing_ids):
            print(f"✅ 批次ID数量正确: {total_ids_in_batches}")
            
            # 显示批次详情
            for i, batch in enumerate(batches):
                print(f"   批次 {batch['batch_id']}: {batch['count']} 个ID")
            
            return True
        else:
            print(f"❌ 批次ID数量不匹配: 期望 {len(test_missing_ids)}, 实际 {total_ids_in_batches}")
            return False
            
    except Exception as e:
        print(f"❌ 批次创建测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🗑️ 清理测试文件")
    print("-" * 40)
    
    files_to_clean = [
        'missing_ids_segments.json',
        'batch_config.json',
        'segment_progress.json'
    ]
    
    # 清理批次文件
    try:
        batch_files = [f for f in os.listdir('.') if f.startswith('batch_') and f.endswith('.csv')]
        files_to_clean.extend(batch_files)
    except:
        pass
    
    # 清理进度目录
    if os.path.exists('batch_progress'):
        import shutil
        shutil.rmtree('batch_progress')
        print("🗑️ 清理批次进度目录")
    
    cleaned_count = 0
    for file in files_to_clean:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ 清理: {file}")
            cleaned_count += 1
    
    print(f"✅ 清理完成，删除了 {cleaned_count} 个文件")

def main():
    """主函数"""
    print("🚀 10段模式优化测试脚本")
    print("=" * 50)
    print("📋 测试目标: 验证分阶段处理优化为最多10段")
    print()
    
    tests = [
        ("10段模式配置", test_10_segments_configuration),
        ("段大小计算", test_segment_calculation),
        ("内存效率", test_memory_efficiency),
        ("批次创建", test_batch_creation_with_10_segments)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 10段模式优化验证通过！")
        print("💡 优化效果:")
        print("   - 从340段减少到10段")
        print("   - 每段约340万ID（相比原来的10万ID）")
        print("   - 内存使用约130MB/段（相比原来的1.3GB）")
        print("   - 大幅减少文件I/O和进度管理复杂度")
        print("\n📝 推荐使用方法:")
        print("   export MAX_SEGMENTS='10'")
        print("   export STAGED_MODE='true'")
        print("   python3 api_release_补全器.py")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    
    # 清理测试文件
    cleanup_test_files()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
