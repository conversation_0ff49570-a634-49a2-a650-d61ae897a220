#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运行API增量获取器的启动脚本

这个脚本解决了之前的问题：
1. 不再有交互式输入等待
2. 使用现有的修复过的API客户端
3. 支持命令行参数控制
4. 智能的429错误处理（5秒→15秒→60秒）

使用方法：
python run_api_fetcher.py --help
python run_api_fetcher.py --max-records 10
python run_api_fetcher.py --start-id 34419650
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# 设置环境变量，避免交互式输入
os.environ['STAGED_MODE'] = 'false'

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Discogs API 增量获取器')
    parser.add_argument('--max-records', '-m', type=int, default=10,
                       help='最大处理记录数（默认10条用于测试）')
    parser.add_argument('--start-id', '-s', type=int, default=0,
                       help='起始ID（0表示从数据库最大ID+1开始）')
    parser.add_argument('--max-404', type=int, default=20,
                       help='最大连续404错误数（默认20）')
    
    args = parser.parse_args()
    
    # 设置环境变量
    os.environ['MAX_RECORDS'] = str(args.max_records)
    os.environ['MAX_CONSECUTIVE_404'] = str(args.max_404)
    
    logger.info("🚀 启动 Discogs API 增量获取器")
    logger.info("=" * 50)
    logger.info(f"📊 最大记录数: {args.max_records}")
    logger.info(f"📊 最大连续404: {args.max_404}")
    if args.start_id > 0:
        logger.info(f"🎯 起始ID: {args.start_id}")
    logger.info("📊 期望API频率: 1 请求/秒")
    logger.info("🔧 429错误处理: 5秒→15秒→60秒渐进等待")
    logger.info("=" * 50)
    
    try:
        # 导入并运行现有的API补全器
        from api_release_补全器 import main as api_main, connect_to_mongodb, DiscogsAPIClient
        
        # 测试数据库连接
        logger.info("🔍 测试数据库连接...")
        client, db = connect_to_mongodb()
        collection = db['release_new']
        count = collection.count_documents({})
        logger.info(f"✅ 数据库连接成功，release_new集合有 {count:,} 条记录")
        
        # 获取最大ID
        if args.start_id == 0:
            result = collection.find().sort("id", -1).limit(1)
            max_doc = list(result)
            if max_doc:
                max_id = max_doc[0]['id']
                logger.info(f"📊 数据库最大ID: {max_id}")
                logger.info(f"🎯 将从ID {max_id + 1} 开始获取")
            else:
                logger.info("📊 数据库为空，将从ID 1开始")
        
        client.close()
        
        # 测试API客户端
        logger.info("🔍 测试API客户端...")
        api_client = DiscogsAPIClient()
        logger.info("✅ API客户端初始化成功")
        
        logger.info("🚀 开始运行增量获取...")
        logger.info("💡 提示：如果遇到429错误，会智能等待而不是固定60秒")
        
        # 运行主程序
        api_main()
        
    except ImportError as e:
        logger.error(f"❌ 导入失败: {e}")
        logger.error("请确保 api_release_补全器.py 文件存在且可访问")
        return 1
    except Exception as e:
        logger.error(f"❌ 运行失败: {e}")
        return 1
    
    logger.info("🎉 程序执行完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
