#!/bin/bash

# Discogs API 增量获取器启动脚本
# 解决环境问题的独立运行脚本

echo "🚀 启动 Discogs API 增量获取器"
echo "=================================="

# 设置工作目录
cd "$(dirname "$0")"
echo "📁 工作目录: $(pwd)"

# 检查Python环境
echo "🔍 检查Python环境..."

# 尝试多个Python命令
PYTHON_CMD=""
for cmd in python3 python /usr/bin/python3 /usr/local/bin/python3; do
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "✅ 找到Python: $cmd"
        PYTHON_VERSION=$($cmd --version 2>&1)
        echo "📊 版本: $PYTHON_VERSION"
        PYTHON_CMD="$cmd"
        break
    fi
done

if [ -z "$PYTHON_CMD" ]; then
    echo "❌ 未找到Python解释器"
    exit 1
fi

# 检查依赖
echo "🔍 检查依赖模块..."
$PYTHON_CMD -c "import requests; print('✅ requests 可用')" 2>/dev/null || {
    echo "❌ requests 模块缺失"
    echo "📦 尝试安装..."
    $PYTHON_CMD -m pip install requests
}

$PYTHON_CMD -c "import pymongo; print('✅ pymongo 可用')" 2>/dev/null || {
    echo "❌ pymongo 模块缺失"
    echo "📦 尝试安装..."
    $PYTHON_CMD -m pip install pymongo
}

# 检查脚本文件
if [ ! -f "api_incremental_fetcher.py" ]; then
    echo "❌ 找不到 api_incremental_fetcher.py 文件"
    exit 1
fi

echo "✅ 环境检查完成"
echo "=================================="

# 运行脚本
echo "🚀 启动增量获取器..."

if [ $# -eq 0 ]; then
    # 默认参数：获取10条记录用于测试
    echo "📊 使用默认参数：--max-records 10"
    $PYTHON_CMD api_incremental_fetcher.py --max-records 10
else
    # 使用用户提供的参数
    echo "📊 使用参数: $@"
    $PYTHON_CMD api_incremental_fetcher.py "$@"
fi

echo "🎉 脚本执行完成"
