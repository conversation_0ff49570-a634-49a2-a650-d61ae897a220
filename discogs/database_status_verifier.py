#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库状态验证器
验证当前数据库状态，确认实际记录数量和数据完整性

功能：
1. 连接数据库并统计各表的记录数量
2. 验证数据完整性和一致性
3. 确认是否存在需要处理的额外数据
4. 生成详细的数据状态报告

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import time
from datetime import datetime
from pymongo import MongoClient

# 数据库配置
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 输出文件
OUTPUT_FILE = 'database_status_report.txt'

def write_output(message, to_console=True):
    """写入输出文件并可选择打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"
    
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_output(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def get_collection_stats(db, collection_name):
    """获取集合的详细统计信息"""
    try:
        collection = db[collection_name]
        
        # 检查集合是否存在
        if collection_name not in db.list_collection_names():
            return {
                'exists': False,
                'total_count': 0,
                'error': f'集合 {collection_name} 不存在'
            }
        
        # 获取总记录数
        total_count = collection.count_documents({})
        
        # 获取ID范围
        min_id_doc = list(collection.find({}, {'id': 1}).sort('id', 1).limit(1))
        max_id_doc = list(collection.find({}, {'id': 1}).sort('id', -1).limit(1))
        
        min_id = min_id_doc[0]['id'] if min_id_doc else None
        max_id = max_id_doc[0]['id'] if max_id_doc else None
        
        # 获取样本记录
        sample_docs = list(collection.find({}, {'id': 1, 'y_id': 1, 'created_at': 1}).limit(3))
        
        return {
            'exists': True,
            'total_count': total_count,
            'min_id': min_id,
            'max_id': max_id,
            'sample_docs': sample_docs
        }
        
    except Exception as e:
        return {
            'exists': False,
            'total_count': 0,
            'error': str(e)
        }

def analyze_database_status(db):
    """分析数据库状态"""
    write_output("📊 开始分析数据库状态...")
    
    # 要检查的集合列表
    collections_to_check = [
        'release_new',
        'release',
        'artists_new', 
        'artists',
        'labels_new',
        'labels',
        'masters_new',
        'masters'
    ]
    
    results = {}
    
    for collection_name in collections_to_check:
        write_output(f"\n🔍 检查集合: {collection_name}")
        stats = get_collection_stats(db, collection_name)
        results[collection_name] = stats
        
        if stats['exists']:
            write_output(f"   ✅ 总记录数: {stats['total_count']:,}")
            if stats['min_id'] and stats['max_id']:
                write_output(f"   📊 ID范围: {stats['min_id']} - {stats['max_id']}")
            
            if stats['sample_docs']:
                write_output(f"   📋 样本记录:")
                for i, doc in enumerate(stats['sample_docs'], 1):
                    y_id = doc.get('y_id', 'N/A')
                    created_at = doc.get('created_at', 'N/A')
                    write_output(f"      {i}. ID: {doc.get('id')}, Y_ID: {y_id}, 创建时间: {created_at}")
        else:
            write_output(f"   ❌ {stats.get('error', '未知错误')}")
    
    return results

def generate_data_consistency_report(results):
    """生成数据一致性报告"""
    write_output("\n📈 数据一致性分析:")
    
    # 分析release数据
    release_new_count = results.get('release_new', {}).get('total_count', 0)
    release_count = results.get('release', {}).get('total_count', 0)
    
    write_output(f"\n🎵 Release数据分析:")
    write_output(f"   release_new表: {release_new_count:,} 条记录")
    write_output(f"   release表: {release_count:,} 条记录")
    
    if release_new_count > 0:
        write_output(f"   ✅ release_new表包含数据，这是主要的数据表")
        if release_count > 0:
            write_output(f"   ⚠️ release表也包含 {release_count:,} 条记录")
            write_output(f"   💡 建议确认两个表的关系和用途")
    elif release_count > 0:
        write_output(f"   ✅ release表包含数据")
        write_output(f"   ⚠️ release_new表为空，可能需要数据迁移")
    else:
        write_output(f"   ❌ 两个release表都为空，需要导入数据")
    
    # 分析其他模块数据
    modules = ['artists', 'labels', 'masters']
    for module in modules:
        new_table = f"{module}_new"
        old_table = module
        
        new_count = results.get(new_table, {}).get('total_count', 0)
        old_count = results.get(old_table, {}).get('total_count', 0)
        
        write_output(f"\n🎭 {module.title()}数据分析:")
        write_output(f"   {new_table}表: {new_count:,} 条记录")
        write_output(f"   {old_table}表: {old_count:,} 条记录")

def check_for_additional_data_sources():
    """检查是否存在其他数据源"""
    write_output("\n🔍 检查其他数据源...")
    
    import glob
    
    # 检查XML文件
    xml_patterns = [
        '*_releases.xml.gz',
        '*_artists.xml.gz', 
        '*_labels.xml.gz',
        '*_masters.xml.gz',
        'data/*_releases.xml.gz',
        'data/*_artists.xml.gz',
        'data/*_labels.xml.gz', 
        'data/*_masters.xml.gz'
    ]
    
    found_files = []
    for pattern in xml_patterns:
        found_files.extend(glob.glob(pattern))
    
    if found_files:
        write_output(f"📁 发现 {len(found_files)} 个XML数据文件:")
        for file_path in sorted(found_files):
            file_size = os.path.getsize(file_path) / (1024**3)  # GB
            write_output(f"   📄 {file_path} ({file_size:.2f} GB)")
    else:
        write_output("📁 未发现XML数据文件")
    
    # 检查CSV文件
    csv_patterns = ['*.csv', 'data/*.csv', 'compare/*.csv']
    csv_files = []
    for pattern in csv_patterns:
        csv_files.extend(glob.glob(pattern))
    
    if csv_files:
        write_output(f"📊 发现 {len(csv_files)} 个CSV文件:")
        for file_path in sorted(csv_files)[:10]:  # 只显示前10个
            write_output(f"   📄 {file_path}")
        if len(csv_files) > 10:
            write_output(f"   ... 还有 {len(csv_files) - 10} 个文件")

def generate_recommendations(results):
    """生成处理建议"""
    write_output("\n💡 处理建议:")
    
    release_new_count = results.get('release_new', {}).get('total_count', 0)
    
    if release_new_count >= 7900000:  # 接近预期的7,935,059
        write_output("✅ 数据库状态良好:")
        write_output("   1. release_new表包含完整的数据")
        write_output("   2. 数据量符合预期（约793万条记录）")
        write_output("   3. 无需额外的数据插入操作")
        write_output("   4. 建议专注于修复XML解析逻辑")
    elif release_new_count > 0:
        write_output("⚠️ 数据库部分完整:")
        write_output(f"   1. 当前有 {release_new_count:,} 条记录")
        write_output("   2. 可能需要补充缺失的数据")
        write_output("   3. 建议先修复解析逻辑，再考虑数据同步")
    else:
        write_output("❌ 数据库为空:")
        write_output("   1. 需要从XML文件导入数据")
        write_output("   2. 建议先修复解析逻辑")
        write_output("   3. 然后执行完整的数据导入")

def main():
    """主函数"""
    # 清空输出文件
    if os.path.exists(OUTPUT_FILE):
        os.remove(OUTPUT_FILE)
    
    write_output("🚀 数据库状态验证器启动")
    write_output("=" * 60)
    start_time = time.time()
    
    try:
        # 1. 连接数据库
        client, db = connect_to_mongodb()
        
        # 2. 分析数据库状态
        results = analyze_database_status(db)
        
        # 3. 生成一致性报告
        generate_data_consistency_report(results)
        
        # 4. 检查其他数据源
        check_for_additional_data_sources()
        
        # 5. 生成处理建议
        generate_recommendations(results)
        
        # 关闭数据库连接
        client.close()
        
        elapsed_time = time.time() - start_time
        write_output(f"\n⏱️ 验证完成，耗时: {elapsed_time:.2f} 秒")
        write_output(f"📄 详细报告已保存到: {OUTPUT_FILE}")
        
    except Exception as e:
        write_output(f"❌ 验证过程中发生错误: {e}")
        raise

if __name__ == "__main__":
    main()
