#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查找包含非空barcode的记录
用于验证数据库中是否存在有效的barcode数据
"""

import os
import json
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def find_non_empty_barcode():
    """查找包含非空barcode的记录"""
    print("🔍 查找包含非空barcode的记录...")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    print("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取release集合
        release_collection = db['release']
        
        print("📊 正在搜索非空barcode记录...")
        
        # 使用聚合管道查找非空barcode
        pipeline = [
            {"$match": {"searchRuleRelease": {"$exists": True}}},
            {"$project": {
                "id": 1,
                "searchRuleRelease": 1,
                "barcode": {
                    "$cond": {
                        "if": {"$and": [
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", None]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", ""]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", " "]}
                        ]},
                        "then": "$searchRuleRelease.baseinfo.barcode",
                        "else": None
                    }
                }
            }},
            {"$match": {"barcode": {"$ne": None}}},
            {"$limit": 10}
        ]
        
        results = list(release_collection.aggregate(pipeline))
        
        if results:
            print(f"✅ 找到 {len(results)} 条包含非空barcode的记录:")
            
            for i, record in enumerate(results, 1):
                print(f"\n📋 记录 #{i}")
                print(f"   ID: {record.get('id')}")
                print(f"   Barcode: {record.get('barcode')}")
                
                # 显示完整的searchRuleRelease结构
                searchrule = record.get('searchRuleRelease', {})
                if isinstance(searchrule, dict):
                    baseinfo = searchrule.get('baseinfo', {})
                    print(f"   BaseInfo: {json.dumps(baseinfo, ensure_ascii=False, indent=2)}")
        else:
            print("❌ 没有找到包含非空barcode的记录")
            
            # 尝试查找任何包含barcode字段的记录（包括空值）
            print("\n🔍 查找所有包含barcode字段的记录（包括空值）...")
            
            sample_records = list(release_collection.find(
                {"searchRuleRelease.baseinfo.barcode": {"$exists": True}},
                {"id": 1, "searchRuleRelease.baseinfo": 1}
            ).limit(5))
            
            if sample_records:
                print(f"✅ 找到 {len(sample_records)} 条包含barcode字段的记录:")
                for i, record in enumerate(sample_records, 1):
                    baseinfo = record.get('searchRuleRelease', {}).get('baseinfo', {})
                    barcode = baseinfo.get('barcode', 'N/A')
                    print(f"   记录 #{i} (ID: {record.get('id')}): barcode='{barcode}'")
            else:
                print("❌ 没有找到任何包含barcode字段的记录")
        
        # 统计barcode字段的分布
        print("\n📊 统计barcode字段分布...")
        
        barcode_stats_pipeline = [
            {"$match": {"searchRuleRelease": {"$exists": True}}},
            {"$project": {
                "barcode_exists": {"$ne": ["$searchRuleRelease.baseinfo.barcode", None]},
                "barcode_empty": {"$eq": ["$searchRuleRelease.baseinfo.barcode", ""]},
                "barcode_value": "$searchRuleRelease.baseinfo.barcode"
            }},
            {"$group": {
                "_id": None,
                "total": {"$sum": 1},
                "has_barcode_field": {"$sum": {"$cond": ["$barcode_exists", 1, 0]}},
                "empty_barcode": {"$sum": {"$cond": ["$barcode_empty", 1, 0]}},
                "non_empty_barcode": {"$sum": {"$cond": [
                    {"$and": [
                        "$barcode_exists",
                        {"$ne": ["$barcode_value", ""]},
                        {"$ne": ["$barcode_value", " "]}
                    ]}, 1, 0
                ]}}
            }}
        ]
        
        stats = list(release_collection.aggregate(barcode_stats_pipeline))
        if stats:
            stat = stats[0]
            print(f"   总记录数: {stat['total']}")
            print(f"   有barcode字段: {stat['has_barcode_field']}")
            print(f"   空barcode: {stat['empty_barcode']}")
            print(f"   非空barcode: {stat['non_empty_barcode']}")
        
    except Exception as e:
        print(f"❌ 查找过程中出错: {e}")
        raise
    finally:
        client.close()
        print("\n🔒 已关闭MongoDB连接")

if __name__ == "__main__":
    try:
        find_non_empty_barcode()
        print("\n✅ 查找完成！")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        exit(1)
