#!/usr/bin/env python3
"""
测试JSON解析修复的最终版本
"""

import json
import re

def parse_json_field(field_value: str):
    """解析JSON字段，处理CSV中的JSON字符串"""
    if not field_value or field_value.strip() == '':
        return []
    
    try:
        # 处理CSV中的双引号转义
        if field_value.startswith('"') and field_value.endswith('"'):
            field_value = field_value[1:-1]
        
        # 处理CSV转义的双引号
        import re
        
        # 1. 先处理特殊的引号+反斜杠模式
        # ""12\"" -> "12\""（在JSON中正确表示包含双引号的字符串）
        # 使用临时标记避免后续双引号处理的干扰
        field_value = field_value.replace('""12\""""', '"12__QUOTE__"')
        # 处理单独的 "12\" 模式
        field_value = field_value.replace('"12\\"', '"12__QUOTE__"')
        
        # 2. 先处理普通的双引号转义，但保护特殊标记
        field_value = field_value.replace('""', '"')

        # 3. 修复不完整的空字符串值和未终止的字符串
        # 先处理最常见的空字符串模式: ": ", -> ": "",
        field_value = re.sub(r':\s*"\s*,', r': "",', field_value)
        # 处理字符串末尾的空值: ": "} -> ": ""}
        field_value = re.sub(r':\s*"\s*}', r': ""}', field_value)
        # 处理数组末尾的空值: ": "] -> ": ""]
        field_value = re.sub(r':\s*"\s*]', r': ""]', field_value)

        # 修复未终止的字符串 - 更精确的模式
        # 匹配 "key": "value} 并修复为 "key": "value"}
        field_value = re.sub(r':\s*"([^"]*)"?\s*}', r': "\1"}', field_value)
        # 匹配 "key": "value] 并修复为 "key": "value"]
        field_value = re.sub(r':\s*"([^"]*)"?\s*]', r': "\1"]', field_value)

        # 3. 恢复特殊标记为正确的JSON转义
        field_value = field_value.replace('__QUOTE__', '\\"')
        
        # 解析JSON
        parsed = json.loads(field_value)
        return parsed if parsed is not None else []
        
    except (json.JSONDecodeError, ValueError) as e:
        print(f"⚠️ JSON解析失败: {field_value[:100]}... 错误: {e}")
        return []

# 测试用例
test_cases = [
    # 原始的12英寸问题
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\""]}]',
    
    # 空字符串问题
    '[{"artist_id": 22072, "name": "Manitou", "role": ", "anv": "}]',
    '[{"artist_id": 234065, "name": "Farid Gharadjedaghi", "role": "Producer, Written-By", "anv": "}, {"artist_id": 1, "name": "Test"}]',
    
    # 未终止字符串问题
    '[{"type": "Barcode", "value": "731457648513", "description": "}]',
    '[{"position": "A1", "title": "Test", "duration": "}]',
    
    # text字段空值问题
    '[{"name": "Vinyl", "qty": "1", "text": ", "descriptions": ["12\""]}]',
    
    # position字段空值问题
    '[{"position": ", "title": "Latitude", "duration": ""}]',
    
    # 复合问题
    '[{"artist_id": 1, "name": "Test", "role": ", "anv": "}, {"artist_id": 2, "name": "Test2", "role": "Producer", "anv": "}]',

    # 更多12英寸变体
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\", "33 ⅓ RPM"]}]',
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["10\""]}]',
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["7\", "45 RPM"]}]'
]

print("🧪 测试JSON解析修复...")
print("=" * 60)

for i, test_case in enumerate(test_cases, 1):
    print(f"\n测试 {i}: {test_case[:80]}...")
    result = parse_json_field(test_case)
    if result:
        print(f"✅ 成功解析: {result}")
    else:
        print(f"❌ 解析失败")

print("\n" + "=" * 60)
print("🎯 测试完成！")
