#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def test_json_formats():
    """测试不同的JSON格式"""
    
    # 测试不同的转义方式
    test_cases = [
        # 原始尝试
        r'[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\""]}]',
        # 双重转义
        r'[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\\\""]}]',
        # 正确的JSON转义
        r'[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\""]}]',
        # 使用原始字符串
        '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\\""]}]',
    ]
    
    for i, test_json in enumerate(test_cases, 1):
        print(f'测试 {i}: {test_json}')
        try:
            parsed = json.loads(test_json)
            print(f'✅ 成功: {parsed[0]["descriptions"]}')
            print(f'实际值: {repr(parsed[0]["descriptions"][0])}')
        except Exception as e:
            print(f'❌ 失败: {e}')
        print()

if __name__ == '__main__':
    test_json_formats()
