#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分阶段处理功能测试脚本

功能：
1. 测试分段ID检测
2. 测试批次创建
3. 测试批次处理
4. 验证文件输出

作者：AI Assistant
创建时间：2025-07-29
"""

import os
import sys
import json
import time

# 设置测试环境变量
os.environ['TEST_MODE'] = 'true'
os.environ['STAGED_MODE'] = 'true'
os.environ['SEGMENT_SIZE'] = '1000'  # 小段测试
os.environ['PROCESSING_BATCH_SIZE'] = '10'  # 小批次测试
os.environ['MAX_RECORDS'] = '20'

def test_segment_detection():
    """测试分段检测功能"""
    print("🧪 测试分段ID检测")
    print("-" * 40)
    
    try:
        from api_release_补全器 import (
            detect_missing_ids_by_segments,
            connect_to_mongodb,
            SEGMENT_SIZE,
            MISSING_IDS_FILE
        )
        
        # 连接数据库
        client, db = connect_to_mongodb()
        
        # 清理之前的测试文件
        test_files = [MISSING_IDS_FILE, 'segment_progress.json']
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️ 清理测试文件: {file}")
        
        print(f"📊 测试分段大小: {SEGMENT_SIZE}")
        print("🔍 开始分段检测...")
        
        start_time = time.time()
        missing_ids = detect_missing_ids_by_segments(db, start_id=1, max_id=2000)  # 更小范围测试
        elapsed = time.time() - start_time
        
        print(f"✅ 分段检测完成，耗时: {elapsed:.2f} 秒")
        print(f"📊 检测到缺失ID数量: {len(missing_ids)}")
        
        # 验证文件是否生成
        if os.path.exists(MISSING_IDS_FILE):
            with open(MISSING_IDS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"📁 缺失ID文件已生成，包含 {data['total_count']} 个ID")
                print(f"📅 生成时间: {data['generated_at']}")
        else:
            print("❌ 缺失ID文件未生成")
            return False
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 分段检测测试失败: {e}")
        return False

def test_batch_creation():
    """测试批次创建功能"""
    print("\n🧪 测试批次创建")
    print("-" * 40)
    
    try:
        from api_release_补全器 import (
            create_processing_batches,
            PROCESSING_BATCH_SIZE,
            BATCH_CONFIG_FILE
        )
        
        # 创建测试数据
        test_missing_ids = list(range(1, 51))  # 50个测试ID
        print(f"📊 测试ID数量: {len(test_missing_ids)}")
        print(f"📦 批次大小: {PROCESSING_BATCH_SIZE}")
        
        # 清理之前的配置文件
        if os.path.exists(BATCH_CONFIG_FILE):
            os.remove(BATCH_CONFIG_FILE)
        
        # 创建批次
        batches = create_processing_batches(test_missing_ids)
        
        print(f"✅ 创建了 {len(batches)} 个批次")
        
        # 验证批次信息
        for i, batch in enumerate(batches[:3]):  # 只显示前3个批次
            print(f"   批次 {batch['batch_id']}: {batch['count']} 个ID "
                  f"(范围: {batch['start_id']}-{batch['end_id']})")
        
        if len(batches) > 3:
            print(f"   ... 还有 {len(batches) - 3} 个批次")
        
        # 验证配置文件
        if os.path.exists(BATCH_CONFIG_FILE):
            with open(BATCH_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"📁 批次配置文件已生成，包含 {config['total_batches']} 个批次")
        else:
            print("❌ 批次配置文件未生成")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 批次创建测试失败: {e}")
        return False

def test_batch_processing():
    """测试批次处理功能"""
    print("\n🧪 测试批次处理")
    print("-" * 40)
    
    try:
        from api_release_补全器 import (
            process_batch,
            connect_to_mongodb,
            DiscogsAPIClient,
            BATCH_PROGRESS_DIR
        )
        
        # 连接数据库
        client, db = connect_to_mongodb()
        api_client = DiscogsAPIClient()
        
        # 创建测试批次
        test_batch = {
            'batch_id': 999,
            'ids': [49, 50, 51],  # 使用已知存在的ID进行测试
            'start_id': 49,
            'end_id': 51,
            'count': 3,
            'status': 'pending'
        }
        
        print(f"📦 测试批次: {test_batch['count']} 个ID")
        print(f"📊 ID列表: {test_batch['ids']}")
        
        # 清理之前的进度文件
        if os.path.exists(BATCH_PROGRESS_DIR):
            import shutil
            shutil.rmtree(BATCH_PROGRESS_DIR)
        
        # 处理批次
        start_time = time.time()
        result = process_batch(test_batch, db, api_client)
        elapsed = time.time() - start_time
        
        print(f"✅ 批次处理完成，耗时: {elapsed:.2f} 秒")
        print(f"📊 处理结果:")
        print(f"   处理数量: {result['processed']}")
        print(f"   成功数量: {result['successful']}")
        print(f"   404跳过: {result['skipped_404']}")
        print(f"   错误数量: {result['errors']}")
        
        # 验证CSV文件
        csv_file = f"batch_{test_batch['batch_id']}_releases.csv"
        if os.path.exists(csv_file):
            import csv
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = list(reader)
                print(f"📁 CSV文件已生成: {len(rows)} 行 (包含表头)")
            
            # 清理测试文件
            os.remove(csv_file)
            print("🗑️ 清理测试CSV文件")
        else:
            print("⚠️ CSV文件未生成（可能所有ID都是404）")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 批次处理测试失败: {e}")
        return False

def test_staged_main():
    """测试完整的分阶段处理流程"""
    print("\n🧪 测试完整分阶段流程")
    print("-" * 40)
    
    try:
        from api_release_补全器 import main_staged
        
        print("🚀 启动分阶段处理...")
        
        # 设置更小的测试范围
        os.environ['SEGMENT_SIZE'] = '100'
        os.environ['PROCESSING_BATCH_SIZE'] = '5'
        
        start_time = time.time()
        main_staged()
        elapsed = time.time() - start_time
        
        print(f"✅ 分阶段处理完成，总耗时: {elapsed:.2f} 秒")
        
        # 检查生成的文件
        files_to_check = [
            'missing_ids_segments.json',
            'batch_config.json',
            'segment_progress.json'
        ]
        
        for file in files_to_check:
            if os.path.exists(file):
                print(f"📁 ✅ {file} 已生成")
            else:
                print(f"📁 ❌ {file} 未生成")
        
        # 检查批次CSV文件
        batch_files = [f for f in os.listdir('.') if f.startswith('batch_') and f.endswith('.csv')]
        if batch_files:
            print(f"📁 ✅ 生成了 {len(batch_files)} 个批次CSV文件")
            for file in batch_files[:3]:  # 只显示前3个
                print(f"   - {file}")
            if len(batch_files) > 3:
                print(f"   ... 还有 {len(batch_files) - 3} 个文件")
        else:
            print("📁 ⚠️ 未生成批次CSV文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n🗑️ 清理测试文件")
    print("-" * 40)
    
    files_to_clean = [
        'missing_ids_segments.json',
        'batch_config.json',
        'segment_progress.json'
    ]
    
    # 清理批次文件
    batch_files = [f for f in os.listdir('.') if f.startswith('batch_') and f.endswith('.csv')]
    files_to_clean.extend(batch_files)
    
    # 清理进度目录
    if os.path.exists('batch_progress'):
        import shutil
        shutil.rmtree('batch_progress')
        print("🗑️ 清理批次进度目录")
    
    cleaned_count = 0
    for file in files_to_clean:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ 清理: {file}")
            cleaned_count += 1
    
    print(f"✅ 清理完成，删除了 {cleaned_count} 个文件")

def main():
    """主函数"""
    print("🚀 分阶段处理功能测试脚本启动")
    print("=" * 50)
    print()
    
    tests_passed = 0
    total_tests = 4
    
    # 测试1: 分段检测
    if test_segment_detection():
        tests_passed += 1
        print("✅ 分段检测测试通过")
    else:
        print("❌ 分段检测测试失败")
    
    # 测试2: 批次创建
    if test_batch_creation():
        tests_passed += 1
        print("✅ 批次创建测试通过")
    else:
        print("❌ 批次创建测试失败")
    
    # 测试3: 批次处理
    if test_batch_processing():
        tests_passed += 1
        print("✅ 批次处理测试通过")
    else:
        print("❌ 批次处理测试失败")
    
    # 测试4: 完整流程
    if test_staged_main():
        tests_passed += 1
        print("✅ 完整流程测试通过")
    else:
        print("❌ 完整流程测试失败")
    
    print(f"\n📊 测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有分阶段处理功能测试通过！")
        print("💡 分阶段处理模式已准备就绪")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    
    # 清理测试文件
    cleanup_test_files()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
