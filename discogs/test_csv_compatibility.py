#!/usr/bin/env python3
"""
CSV兼容性测试脚本
测试api_release_补全器.py生成的CSV文件与csv_to_mongodb_importer.py的兼容性
"""

import json
import csv
import os
import sys
from datetime import datetime, timezone

# 导入修复后的函数
sys.path.append('.')
from api_release_补全器 import (
    safe_string_value, 
    format_array_for_csv, 
    clean_data_for_json,
    write_releases_to_csv,
    validate_csv_json_compatibility
)

def create_test_data():
    """创建包含各种边界情况的测试数据"""
    test_cases = [
        {
            'id': 1,
            'y_id': 'YRD1',
            'title': 'Test Album with "Quotes"',
            'artists': [
                {
                    'artist_id': 204431,
                    'name': '<PERSON>',
                    'role': 'Engineer [Remix"]',  # 方括号内的引号
                    'anv': '<PERSON>'
                }
            ],
            'extra_artists': [
                {
                    'artist_id': 180209,
                    'name': '<PERSON>',
                    'role': 'Compiled By, Edited By',
                    'anv': '<PERSON> "The'  # 未终止的引号
                }
            ],
            'labels': [
                {
                    'name': 'Test Label',
                    'catno': 'TL001',
                    'id': '123'
                }
            ],
            'companies': [],
            'country': 'US',
            'formats': [
                {
                    'name': 'Vinyl',
                    'qty': '1',
                    'text': '',
                    'descriptions': ['12"', '33 ⅓ RPM']  # 引号问题
                }
            ],
            'genres': ['Electronic', 'Hip Hop'],
            'styles': ['Breakbeat', 'Turntablism'],
            'identifiers': [
                {
                    'type': 'Barcode',
                    'value': '0 90204 24652 6',
                    'description': 'Text'
                }
            ],
            'tracklist': [
                {
                    'position': '',
                    'title': 'Logo Side: "',  # 标题中的引号
                    'duration': ''
                },
                {
                    'position': 'A1',
                    'title': 'Test Track',
                    'duration': '3:45'
                }
            ],
            'master_id': 12345,
            'discogs_status': '',
            'images': [],
            'notes': 'Test notes with\nnewlines and\ttabs',  # 换行符和制表符
            'year': 2023,
            'images_permissions': 1,
            'permissions': 1,
            'source': 1,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        },
        {
            'id': 2,
            'y_id': 'YRD2',
            'title': 'Another Test',
            'artists': [
                {
                    'artist_id': 10402,
                    'name': '[Love"] Tattoo',  # 复杂的引号组合
                    'role': '',
                    'anv': ''
                }
            ],
            'extra_artists': [],
            'labels': [],
            'companies': [],
            'country': '',
            'formats': [],
            'genres': [],
            'styles': [],
            'identifiers': [],
            'tracklist': [],
            'master_id': None,
            'discogs_status': '',
            'images': [],
            'notes': '',
            'year': None,
            'images_permissions': 1,
            'permissions': 1,
            'source': 1,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }
    ]
    
    return test_cases

def test_safe_string_value():
    """测试字符串安全处理函数"""
    print("🧪 测试 safe_string_value 函数...")
    
    test_cases = [
        ('Normal string', 'Normal string'),
        ('String with "quotes"', 'String with "quotes"'),
        ('String with\nnewlines', 'String with newlines'),
        ('String with\ttabs', 'String with tabs'),
        ('String with\r\nCRLF', 'String with CRLF'),
        ('   Trimmed   ', 'Trimmed'),
        ('', ''),
        (None, ''),
        ('String with [brackets"]', 'String with [brackets"]'),
        ('String with trailing"', 'String with trailing"'),
    ]
    
    for i, (input_val, expected_pattern) in enumerate(test_cases, 1):
        result = safe_string_value(input_val)
        print(f"测试 {i:2d}: {repr(input_val)} -> {repr(result)}")
        
        # 基本验证
        if result is not None and '\n' not in result and '\t' not in result:
            print(f"✅ 测试 {i} 通过")
        else:
            print(f"❌ 测试 {i} 失败")

def test_json_formatting():
    """测试JSON格式化函数"""
    print("\n🧪 测试 format_array_for_csv 函数...")
    
    test_data = create_test_data()[0]  # 使用第一个测试用例
    
    json_fields = ['artists', 'extra_artists', 'formats', 'tracklist', 'identifiers']
    
    for field in json_fields:
        if field in test_data:
            print(f"\n测试字段: {field}")
            result = format_array_for_csv(test_data[field])
            print(f"结果: {result[:100]}...")
            
            # 验证JSON有效性
            try:
                parsed = json.loads(result)
                print(f"✅ {field} JSON有效")
            except json.JSONDecodeError as e:
                print(f"❌ {field} JSON无效: {e}")

def test_csv_generation():
    """测试CSV生成"""
    print("\n🧪 测试CSV生成...")
    
    test_data = create_test_data()
    test_filename = 'test_compatibility.csv'
    
    # 生成测试CSV
    write_releases_to_csv(test_data, test_filename, append_mode=False)
    
    if os.path.exists(test_filename):
        print(f"✅ CSV文件生成成功: {test_filename}")
        
        # 验证CSV兼容性
        validation_result = validate_csv_json_compatibility(test_filename)
        
        print(f"📊 验证结果:")
        print(f"   总行数: {validation_result['total_rows']}")
        print(f"   JSON解析错误: {validation_result['json_parse_errors']}")
        print(f"   成功率: {validation_result['success_rate']:.2f}%")
        
        if validation_result['error_details']:
            print(f"❌ 发现错误:")
            for error in validation_result['error_details'][:5]:  # 只显示前5个错误
                print(f"   行 {error['row']}, 字段 {error['field']}: {error['error']}")
        
        return test_filename
    else:
        print(f"❌ CSV文件生成失败")
        return None

def test_with_importer(csv_filename):
    """使用修复后的导入器测试CSV文件"""
    if not csv_filename or not os.path.exists(csv_filename):
        print("❌ 没有有效的CSV文件进行导入测试")
        return
    
    print(f"\n🧪 使用csv_to_mongodb_importer测试 {csv_filename}...")
    
    try:
        # 导入修复后的导入器
        from csv_to_mongodb_importer import CSVToMongoDBImporter
        
        # 创建导入器实例（不连接数据库，只测试解析）
        importer = CSVToMongoDBImporter(
            csv_file=csv_filename,
            connect_to_db=False  # 只测试解析，不连接数据库
        )
        
        # 测试CSV解析
        print("📊 开始解析测试...")
        success_count = 0
        error_count = 0
        
        import pandas as pd
        df = pd.read_csv(csv_filename)
        
        for index, row in df.iterrows():
            try:
                # 测试JSON字段解析
                json_fields = ['artists', 'extra_artists', 'labels', 'companies', 
                              'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']
                
                for field in json_fields:
                    if field in row and pd.notna(row[field]):
                        parsed = importer.parse_json_field(str(row[field]))
                        if parsed is not None:
                            success_count += 1
                        else:
                            error_count += 1
                            
            except Exception as e:
                error_count += 1
                print(f"❌ 行 {index + 1} 解析失败: {e}")
        
        total_tests = success_count + error_count
        if total_tests > 0:
            success_rate = (success_count / total_tests) * 100
            print(f"📊 导入器测试结果:")
            print(f"   成功解析: {success_count}")
            print(f"   解析失败: {error_count}")
            print(f"   成功率: {success_rate:.2f}%")
        
    except ImportError:
        print("⚠️ 无法导入csv_to_mongodb_importer，跳过导入器测试")
    except Exception as e:
        print(f"❌ 导入器测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始CSV兼容性测试...")
    print("=" * 60)
    
    # 测试字符串处理
    test_safe_string_value()
    
    # 测试JSON格式化
    test_json_formatting()
    
    # 测试CSV生成
    csv_filename = test_csv_generation()
    
    # 测试与导入器的兼容性
    test_with_importer(csv_filename)
    
    print("\n" + "=" * 60)
    print("🏁 测试完成！")

if __name__ == "__main__":
    main()
