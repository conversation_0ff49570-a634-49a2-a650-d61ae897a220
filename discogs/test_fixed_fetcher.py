#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的增量获取器
"""

import os
import sys
import subprocess

def test_help():
    """测试帮助信息"""
    print("🧪 测试1: 帮助信息")
    try:
        result = subprocess.run([sys.executable, 'api_incremental_fetcher.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ 帮助信息正常")
            print("输出预览:")
            print(result.stdout[:200] + "...")
            return True
        else:
            print(f"❌ 帮助信息失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_import():
    """测试模块导入"""
    print("\n🧪 测试2: 模块导入")
    try:
        from api_incremental_fetcher import IncrementalFetcher, connect_to_mongodb
        print("✅ 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试3: 数据库连接")
    try:
        from api_incremental_fetcher import connect_to_mongodb
        client, db = connect_to_mongodb()
        
        # 测试基本查询
        collection = db['release_new']
        count = collection.count_documents({})
        print(f"✅ 数据库连接成功，release_new集合有 {count:,} 条记录")
        
        client.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_fetcher_init():
    """测试获取器初始化"""
    print("\n🧪 测试4: 获取器初始化")
    try:
        from api_incremental_fetcher import IncrementalFetcher, connect_to_mongodb
        
        client, db = connect_to_mongodb()
        fetcher = IncrementalFetcher(db)
        
        print("✅ 增量获取器初始化成功")
        print(f"   API客户端: {type(fetcher.api_client).__name__}")
        print(f"   数据库: {fetcher.db.name}")
        
        client.close()
        return True
    except Exception as e:
        print(f"❌ 获取器初始化失败: {e}")
        return False

def test_dry_run():
    """测试干运行（只处理1条记录）"""
    print("\n🧪 测试5: 干运行测试")
    try:
        # 设置环境变量
        os.environ['MAX_RECORDS'] = '1'
        os.environ['MAX_CONSECUTIVE_404'] = '5'
        
        result = subprocess.run([
            sys.executable, 'api_incremental_fetcher.py', 
            '--fresh', '--max-records', '1'
        ], capture_output=True, text=True, timeout=30)
        
        if "程序执行完成" in result.stdout or "已达到最大处理记录数限制" in result.stdout:
            print("✅ 干运行测试成功")
            print("输出预览:")
            lines = result.stdout.split('\n')
            for line in lines[-10:]:  # 显示最后10行
                if line.strip():
                    print(f"   {line}")
            return True
        else:
            print(f"❌ 干运行测试失败")
            print("STDOUT:", result.stdout[-500:])
            print("STDERR:", result.stderr[-500:])
            return False
    except subprocess.TimeoutExpired:
        print("❌ 干运行测试超时")
        return False
    except Exception as e:
        print(f"❌ 干运行测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始测试修复后的增量获取器")
    print("=" * 50)
    
    tests = [
        test_help,
        test_import,
        test_database_connection,
        test_fetcher_init,
        test_dry_run
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增量获取器可以正常使用")
        print("\n📖 使用方法:")
        print("   python api_incremental_fetcher.py --help")
        print("   python api_incremental_fetcher.py --fresh --max-records 100")
        print("   python api_incremental_fetcher.py --resume")
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
