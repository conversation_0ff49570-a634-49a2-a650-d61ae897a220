#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs XML记录统计工具

用于统计discogs XML文件中的记录数量，支持大型文件的内存高效处理。

功能特点：
1. 自动查找XML文件或手动指定文件名
2. 使用流式解析，内存效率高
3. 实时进度显示
4. 准确的记录统计
5. 完善的错误处理

使用方法：
    python count_xml_records.py                    # 自动查找releases XML文件
    python count_xml_records.py filename.xml.gz    # 指定文件名

作者：AI Assistant
创建时间：2025-07-29
"""

import gzip
import time
import os
import glob
import sys
from datetime import datetime


def write_output(message):
    """输出带时间戳的消息"""
    timestamp = datetime.now().strftime('[%Y-%m-%d %H:%M:%S]')
    print(f"{timestamp} {message}")


def find_xml_file(filename=None):
    """
    查找XML文件
    
    Args:
        filename: 指定的文件名，如果为None则自动查找releases文件
        
    Returns:
        找到的文件路径，如果没找到返回None
    """
    if filename:
        # 如果指定了文件名，直接检查是否存在
        if os.path.exists(filename):
            write_output(f"✅ 使用指定文件: {filename}")
            return filename
        else:
            write_output(f"❌ 指定文件不存在: {filename}")
            return None
    
    # 自动查找releases XML文件
    write_output("🔍 自动查找releases XML文件...")
    
    patterns = [
        'discogs_*_releases.xml.gz',
        '*_releases.xml.gz',
        'discogs_*_releases.xml',
        '*_releases.xml'
    ]
    
    found_files = []
    for pattern in patterns:
        found_files.extend(glob.glob(pattern))
    
    if not found_files:
        write_output("❌ 未找到releases XML文件")
        write_output("   支持的文件格式: discogs_YYYYMMDD_releases.xml.gz")
        return None
    
    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        write_output(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        write_output(f"✅ 找到XML文件: {selected_file}")
    
    return selected_file


def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        secs = seconds % 60
        return f"{int(minutes)}分{secs:.1f}秒"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{int(hours)}小时{int(minutes)}分{secs:.1f}秒"


def display_progress(count, start_time, interval=100000):
    """显示处理进度"""
    elapsed = time.time() - start_time
    speed = count / elapsed if elapsed > 0 else 0
    
    write_output(f"📈 已统计 {count:,} 条记录，"
                f"速度: {speed:.0f} 记录/秒，"
                f"用时: {format_time(elapsed)}")


def get_file_info(file_path):
    """获取文件基本信息"""
    try:
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024**2)
        file_size_gb = file_size / (1024**3)
        
        if file_size_gb >= 1:
            size_str = f"{file_size_gb:.2f} GB"
        else:
            size_str = f"{file_size_mb:.1f} MB"
            
        write_output(f"📊 文件大小: {size_str} ({file_size:,} 字节)")
        return file_size
    except Exception as e:
        write_output(f"⚠️ 无法获取文件信息: {e}")
        return 0


def count_release_records(xml_file):
    """
    统计XML文件中的release记录数量
    
    Args:
        xml_file: XML文件路径
        
    Returns:
        记录数量，如果出错返回-1
    """
    write_output(f"🔍 开始统计记录数量: {xml_file}")
    start_time = time.time()
    
    # 获取文件信息
    file_size = get_file_info(xml_file)
    
    release_count = 0
    line_count = 0
    
    try:
        # 判断是否为gzip文件
        is_gzip = xml_file.endswith('.gz')
        
        if is_gzip:
            file_opener = lambda: gzip.open(xml_file, 'rt', encoding='utf-8')
            write_output("📦 检测到gzip压缩文件，使用gzip解压")
        else:
            file_opener = lambda: open(xml_file, 'r', encoding='utf-8')
            write_output("📄 检测到普通文本文件")
        
        with file_opener() as f:
            buffer = ""
            in_release = False
            
            for line in f:
                line_count += 1
                
                # 检测release记录开始
                if '<release ' in line:
                    in_release = True
                    buffer = line
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    release_count += 1
                    
                    # 显示进度
                    if release_count % 100000 == 0:
                        display_progress(release_count, start_time)
                    
                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line
                
                # 每处理100万行显示一次行数进度
                if line_count % 1000000 == 0:
                    elapsed = time.time() - start_time
                    write_output(f"📄 已处理 {line_count:,} 行，用时: {format_time(elapsed)}")
        
        # 计算最终统计
        total_time = time.time() - start_time
        avg_speed = release_count / total_time if total_time > 0 else 0
        
        # 输出最终结果
        write_output("\n" + "="*60)
        write_output("📊 统计结果")
        write_output("="*60)
        write_output(f"✅ XML文件: {xml_file}")
        write_output(f"✅ 文件大小: {file_size:,} 字节")
        write_output(f"✅ 总行数: {line_count:,}")
        write_output(f"✅ Release记录数: {release_count:,}")
        write_output(f"✅ 处理时间: {format_time(total_time)}")
        write_output(f"✅ 平均速度: {avg_speed:.0f} 记录/秒")
        write_output("="*60)
        
        return release_count
        
    except FileNotFoundError:
        write_output(f"❌ 文件不存在: {xml_file}")
        return -1
    except gzip.BadGzipFile:
        write_output(f"❌ 无效的gzip文件: {xml_file}")
        return -1
    except UnicodeDecodeError as e:
        write_output(f"❌ 文件编码错误: {e}")
        return -1
    except MemoryError:
        write_output(f"❌ 内存不足，无法处理文件: {xml_file}")
        return -1
    except Exception as e:
        write_output(f"❌ 处理过程中出错: {e}")
        return -1


def main():
    """主函数"""
    print("🔍 Discogs XML记录统计工具")
    print("="*60)
    
    # 检查命令行参数
    filename = None
    if len(sys.argv) > 1:
        filename = sys.argv[1]
        write_output(f"📝 使用命令行指定的文件: {filename}")
    
    # 查找XML文件
    xml_file = find_xml_file(filename)
    if not xml_file:
        write_output("❌ 无法找到XML文件，程序退出")
        write_output("\n使用方法:")
        write_output("  python count_xml_records.py                    # 自动查找")
        write_output("  python count_xml_records.py filename.xml.gz    # 指定文件")
        sys.exit(1)
    
    # 统计记录数量
    record_count = count_release_records(xml_file)
    
    if record_count >= 0:
        write_output(f"\n🎯 最终结果: XML文件包含 {record_count:,} 条release记录")
        write_output("✅ 统计完成")
    else:
        write_output("❌ 统计失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
