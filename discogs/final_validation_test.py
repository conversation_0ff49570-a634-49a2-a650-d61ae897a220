#!/usr/bin/env python3
"""
最终验证测试 - 验证CSV解析修复的完整性
"""

import json
import re

def parse_json_field(field_value: str):
    """解析JSON字段，处理CSV中的JSON字符串"""
    if not field_value or field_value.strip() == '':
        return []
    
    try:
        # 处理CSV中的双引号转义
        if field_value.startswith('"') and field_value.endswith('"'):
            field_value = field_value[1:-1]
        
        # 处理CSV转义的双引号
        import re
        
        # 1. 先处理特殊的引号+反斜杠模式
        field_value = field_value.replace('""12\""""', '"12__QUOTE__"')
        # 处理各种尺寸的引号模式
        field_value = field_value.replace('"12\\"', '"12__QUOTE__"')
        field_value = field_value.replace('"10\\"', '"10__QUOTE__"')
        field_value = field_value.replace('"7\\"', '"7__QUOTE__"')
        # 处理方括号内的引号问题
        field_value = re.sub(r'\[([^\]]*)"([^\]]*)\]', r'[\1__QUOTE__\2]', field_value)
        # 处理字符串值中的引号
        field_value = re.sub(r':\s*"([^"]*)\\"([^"]*)"', r': "\1__QUOTE__\2"', field_value)
        # 处理标题中的引号
        field_value = re.sub(r':\s*"([^"]*)""\s*([,}])', r': "\1__QUOTE__"\2', field_value)
        
        # 2. 先处理普通的双引号转义，但保护特殊标记
        field_value = field_value.replace('""', '"')

        # 3. 修复不完整的空字符串值和未终止的字符串
        field_value = re.sub(r':\s*"\s*,', r': "",', field_value)
        field_value = re.sub(r':\s*"\s*}', r': ""}', field_value)
        field_value = re.sub(r':\s*"\s*]', r': ""]', field_value)
        
        # 修复未终止的字符串
        field_value = re.sub(r':\s*"([^"]*)"?\s*}', r': "\1"}', field_value)
        field_value = re.sub(r':\s*"([^"]*)"?\s*]', r': "\1"]', field_value)
        
        # 4. 恢复特殊标记为正确的JSON转义
        field_value = field_value.replace('__QUOTE__', '\\"')
        
        # 解析JSON
        parsed = json.loads(field_value)
        return parsed if parsed is not None else []
        
    except (json.JSONDecodeError, ValueError) as e:
        print(f"⚠️ JSON解析失败: {field_value[:100]}... 错误: {e}")
        return []

# 测试用例 - 包含所有已知的问题模式
test_cases = [
    # 基本的12英寸问题
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\""]}]',
    
    # 其他尺寸
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["10\""]}]',
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["7\", "45 RPM"]}]',
    
    # 复合描述
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\", "33 ⅓ RPM"]}]',
    '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["10\", "45 RPM", "EP", "Stereo"]}]',
    
    # 空字符串问题
    '[{"artist_id": 22072, "name": "Manitou", "role": ", "anv": "}]',
    '[{"position": ", "title": "Latitude", "duration": ""}]',
    
    # 未终止字符串
    '[{"type": "Barcode", "value": "731457648513", "description": "}]',
    '[{"position": "A1", "title": "Test", "duration": "}]',
    
    # 方括号内的引号
    '[{"artist_id": 204431, "name": "Steve Spapperi", "role": "Engineer [Remix"]", "anv": ""}]',
    '[{"artist_id": 527543, "name": "Klaas-Jan Jongsma", "role": "Producer [All Tracks By"]", "anv": ""}]',
    '[{"artist_id": 2138, "name": "DJ Duke", "role": "Remix [Uncredited"]", "anv": ""}]',
    
    # 字符串值中的引号
    '[{"artist_id": 180209, "name": "Lenny Logan", "role": "Compiled By, Edited By", "anv": "Lenny \\"The"}]',
    
    # 复杂的组合问题
    '[{"artist_id": 10402, "name": "[Love"] Tattoo", "role": "", "anv": ""}]',
    '[{"position": "", "title": "Logo Side: "", "duration": ""}]',
]

print("🧪 最终验证测试...")
print("=" * 80)

success_count = 0
total_count = len(test_cases)

for i, test_case in enumerate(test_cases, 1):
    print(f"\n测试 {i:2d}: {test_case[:70]}...")
    result = parse_json_field(test_case)
    if result:
        print(f"✅ 成功解析: {result}")
        success_count += 1
    else:
        print(f"❌ 解析失败")

print("\n" + "=" * 80)
print(f"🎯 测试完成！")
print(f"📊 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")

if success_count == total_count:
    print("🎉 所有测试用例都通过了！")
else:
    print(f"⚠️  还有 {total_count - success_count} 个测试用例需要修复")
