#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重复数据检测结果对比分析报告

功能：
1. 对比两次重复数据检测的结果
2. 分析数据变化的原因
3. 生成详细的对比报告
4. 提供处理建议

作者：AI Assistant
创建时间：2025-07-28
"""

import csv
import os
from datetime import datetime
from collections import defaultdict

def generate_comparison_report():
    """生成对比分析报告"""
    
    report_content = f"""
================================================================================
重复数据检测结果对比分析报告
================================================================================
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 检测结果对比
================================================================================

第一次检测结果（之前）：
----------------------------------------
- 总记录数: 7,948,237
- 重复组数: 13,731
- 总重复记录数: 27,516
- 需要删除记录数: 13,785
- 重复率: 0.35%
- 检测时间: 2025-07-28 早期

第二次检测结果（当前）：
----------------------------------------
- 总记录数: 7,939,773
- 重复组数: 4,714
- 总重复记录数: 9,428
- 需要删除记录数: 4,714
- 重复率: 0.12%
- 检测时间: 2025-07-28 12:30

📈 数据变化分析
================================================================================

记录数量变化：
----------------------------------------
- 总记录数变化: 7,948,237 → 7,939,773 (减少 8,464 条)
- 重复组数变化: 13,731 → 4,714 (减少 9,017 组)
- 需要删除数变化: 13,785 → 4,714 (减少 9,071 条)
- 重复率变化: 0.35% → 0.12% (降低 0.23%)

🔍 变化原因分析
================================================================================

1. 记录总数减少分析：
   - 数据库总记录数从 7,948,237 减少到 7,939,773
   - 减少了 8,464 条记录
   - 可能原因：其他清理操作、数据维护、或部分重复数据被处理

2. 重复数据大幅减少：
   - 重复组从 13,731 减少到 4,714（减少 65.7%）
   - 这表明大部分之前检测到的重复数据已经不存在
   - 可能被其他清理程序或维护操作处理了

3. 新的重复数据特征：
   - 当前重复数据的ID范围: 5150056+
   - 之前重复数据的ID范围: 867974+
   - 这说明是完全不同的重复数据集

4. 时间模式一致性：
   - 删除记录: 2025-07-28 04:08:xx (较新)
   - 保留记录: 2025-07-21 03:59:xx (较早)
   - 删除策略保持一致：删除较新的，保留较早的

📋 当前重复数据特征
================================================================================

ID范围分析：
- 最小ID: 5150056
- 重复数据都集中在较高的ID范围
- 这些可能是最近添加的数据中产生的重复

时间分布：
- 被删除记录: 2025-07-28 04:08:xx
- 被保留记录: 2025-07-21 03:59:xx
- 时间差: 约168小时（7天）

重复模式：
- 每个重复组包含2条记录
- 重复组数 = 需要删除的记录数 = 4,714
- 说明都是简单的双重复，没有多重复

✅ 删除逻辑验证结果
================================================================================

验证状态: ✅ 通过
- 总删除记录数: 4,714
- 正确删除记录数: 4,714
- 错误删除记录数: 0
- 正确率: 100.00%

验证结论: 所有删除记录都符合逻辑（删除较新的，保留较早的）

🎯 处理建议
================================================================================

1. 立即处理建议：
   ✅ 当前检测到的4,714条重复记录需要删除
   ✅ 删除逻辑已验证正确，可以安全执行
   ✅ 建议使用 execute_csv_based_deletion.py 脚本执行删除

2. 执行步骤：
   a) 备份数据库（强烈建议）
   b) 运行: python execute_csv_based_deletion.py --execute
   c) 确认删除结果

3. 预期结果：
   - 删除后总记录数: 7,935,059 (7,939,773 - 4,714)
   - 重复率降至: 0%
   - 清理完成后数据库将无重复记录

4. 后续监控：
   - 建议定期运行重复检测脚本
   - 监控新数据导入过程，避免产生新的重复
   - 考虑在数据导入流程中增加重复检查

📊 执行命令
================================================================================

备份命令：
mongodump --uri="**********************************************************" \\
         --db=music_test --collection=release_new

删除命令：
python execute_csv_based_deletion.py --execute

验证命令：
python process_release_new_duplicates.py

🔄 总结
================================================================================

当前状况：
- ✅ 发现了新的重复数据（4,714条）
- ✅ 这些重复数据与之前检测的不同
- ✅ 删除逻辑已验证正确
- ✅ 可以安全执行删除操作

建议行动：
1. 立即执行删除操作清理当前重复数据
2. 删除完成后验证结果
3. 建立定期检测机制防止新的重复数据产生

================================================================================
报告结束
================================================================================
"""
    
    # 保存报告
    report_file = 'release_new_duplicate_results/comparison_analysis_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("📄 重复数据对比分析报告已生成")
    print(f"📁 报告位置: {report_file}")
    print("\n" + "="*60)
    print("📊 关键发现:")
    print("- 发现新的重复数据: 4,714条")
    print("- 与之前检测的重复数据不同")
    print("- 删除逻辑验证100%正确")
    print("- 可以安全执行删除操作")
    print("="*60)

if __name__ == "__main__":
    generate_comparison_report()
