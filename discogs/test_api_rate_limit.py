#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API频率限制测试脚本

功能：
1. 测试API调用的实际频率
2. 检查是否遇到429错误
3. 验证1秒1次的频率限制是否正常工作

作者：AI Assistant
创建时间：2025-07-29
"""

import time
from api_release_补全器 import DiscogsAPIClient

def test_api_rate_limit():
    """测试API频率限制"""
    print("🧪 测试API频率限制")
    print("=" * 50)
    
    # 创建API客户端
    api_client = DiscogsAPIClient()
    
    # 测试ID列表（一些已知存在的ID）
    test_ids = [49, 50, 51, 52, 53]
    
    start_time = time.time()
    
    for i, test_id in enumerate(test_ids):
        print(f"\n🔍 测试 {i+1}/{len(test_ids)}: ID {test_id}")
        
        # 记录请求开始时间
        request_start = time.time()
        
        # 调用API
        result = api_client.get_release(test_id)
        
        # 记录请求结束时间
        request_end = time.time()
        request_duration = request_end - request_start
        
        # 分析结果
        if result is None:
            print(f"   结果: 404 (ID不存在)")
        elif result is False:
            print(f"   结果: API请求失败")
        else:
            title = result.get('title', 'N/A')[:50]
            print(f"   结果: 成功 - {title}")
        
        print(f"   请求耗时: {request_duration:.2f}秒")
        
        # 计算从开始到现在的平均频率
        if i > 0:
            elapsed = time.time() - start_time
            avg_rate = (i + 1) / elapsed
            print(f"   平均频率: {avg_rate:.2f} 请求/秒")
    
    total_time = time.time() - start_time
    total_requests = len(test_ids)
    final_rate = total_requests / total_time
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    print(f"总请求数: {total_requests}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均频率: {final_rate:.2f} 请求/秒")
    print(f"期望频率: 1.0 请求/秒")
    
    if 0.8 <= final_rate <= 1.2:
        print("✅ 频率控制正常")
    else:
        print("❌ 频率控制异常")
        if final_rate > 1.2:
            print("   可能原因: 频率限制过松")
        else:
            print("   可能原因: 频率限制过严或遇到429错误")

def test_single_api_call():
    """测试单次API调用的详细信息"""
    print("\n🧪 测试单次API调用详情")
    print("=" * 50)
    
    api_client = DiscogsAPIClient()
    test_id = 49
    
    print(f"🔍 测试ID: {test_id}")
    print(f"📊 API_RATE_LIMIT: {api_client.__class__.__module__}")
    
    # 检查初始状态
    print(f"⏰ 初始last_request_time: {api_client.last_request_time}")
    
    # 第一次调用
    print("\n📞 第一次API调用...")
    start_time = time.time()
    result1 = api_client.get_release(test_id)
    end_time = time.time()
    
    print(f"   耗时: {end_time - start_time:.2f}秒")
    print(f"   结果: {'成功' if result1 and result1 is not False else '失败'}")
    print(f"   last_request_time: {api_client.last_request_time}")
    
    # 立即第二次调用
    print("\n📞 立即第二次API调用...")
    start_time = time.time()
    result2 = api_client.get_release(test_id)
    end_time = time.time()
    
    print(f"   耗时: {end_time - start_time:.2f}秒")
    print(f"   结果: {'成功' if result2 and result2 is not False else '失败'}")
    print(f"   last_request_time: {api_client.last_request_time}")
    
    if end_time - start_time > 0.8:
        print("✅ 频率限制正常工作（等待了约1秒）")
    else:
        print("❌ 频率限制可能有问题（没有等待足够时间）")

if __name__ == "__main__":
    try:
        test_single_api_call()
        test_api_rate_limit()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
