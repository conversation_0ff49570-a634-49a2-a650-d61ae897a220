#!/usr/bin/env python3
"""
CSV结构修复测试脚本
测试修复后的api_release_补全器.py是否能正确处理包含换行符的数据
"""

import json
import csv
import os
import sys
from datetime import datetime, timezone

# 导入修复后的函数
sys.path.append('.')
from api_release_补全器 import (
    safe_string_value, 
    format_array_for_csv, 
    clean_data_for_json,
    write_releases_to_csv
)

def create_problematic_test_data():
    """创建包含换行符等问题字符的测试数据"""
    test_cases = [
        {
            'id': 26335,
            'y_id': 'YRD17301725',
            'title': 'In Africa 1996 (The 4 Years Later Version)',
            'artists': [
                {
                    'artist_id': 22072,
                    'name': 'Manitou',
                    'role': '',
                    'anv': ''
                }
            ],
            'extra_artists': [
                {
                    'artist_id': 234065,
                    'name': '<PERSON><PERSON>',
                    'role': 'Producer, Written-By',
                    'anv': ''
                }
            ],
            'labels': [
                {
                    'name': 'Urban',
                    'catno': '576 485-1',
                    'id': '2017'
                }
            ],
            'companies': [],
            'country': 'Germany',
            'formats': [
                {
                    'name': 'Vinyl',
                    'qty': '1',
                    'text': '',
                    'descriptions': ['12"']
                }
            ],
            'genres': ['Electronic'],
            'styles': ['Techno'],
            'identifiers': [
                {
                    'type': 'Barcode',
                    'value': '731457648513',
                    'description': ''
                }
            ],
            # 这是问题的关键：包含换行符的tracklist
            'tracklist': [
                {
                    'position': 'A',
                    'title': 'In Africa 1996 (4 Years Later Version)',
                    'duration': '6:54'
                },
                {
                    'position': 'B1', 
                    'title': 'The Arab',
                    'duration': '5:34'
                },
                {
                    'position': 'B2',
                    'title': 'In Africa (1992 Version)', 
                    'duration': '5:28'
                }
            ],
            'master_id': 82024,
            'discogs_status': '',
            'images': [
                {
                    'type': 'primary',
                    'uri': './/release055/release_26335_uri_1.jpg',
                    'resource_url': 'https://example.com/image1.jpg',
                    'uri150': './/release055/release_26335_uri150_1.jpg',
                    'width': 600,
                    'height': 600,
                    'newurlflag': 1
                }
            ],
            'notes': 'Test notes with\nnewlines\rand\ttabs',  # 包含各种控制字符
            'year': 1996,
            'images_permissions': 1,
            'permissions': 1,
            'source': 1,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }
    ]
    
    return test_cases

def test_string_cleaning():
    """测试字符串清理功能"""
    print("🧪 测试字符串清理功能...")
    
    test_strings = [
        'Normal string',
        'String with\nnewlines',
        'String with\r\nCRLF',
        'String with\ttabs',
        'String with\x00null\x01chars',
        'String with "quotes"',
        'Multi\nline\rstring\twith\x0Bvarious\x0Ccontrol\x1Fchars',
    ]
    
    for i, test_str in enumerate(test_strings, 1):
        cleaned = safe_string_value(test_str)
        print(f"测试 {i}: {repr(test_str)} -> {repr(cleaned)}")
        
        # 验证没有控制字符
        has_control_chars = any(ord(c) < 32 or ord(c) == 127 for c in cleaned)
        if not has_control_chars:
            print(f"✅ 测试 {i} 通过 - 无控制字符")
        else:
            print(f"❌ 测试 {i} 失败 - 仍有控制字符")
        print()

def test_json_formatting():
    """测试JSON格式化功能"""
    print("🧪 测试JSON格式化功能...")
    
    test_data = create_problematic_test_data()[0]
    
    # 测试tracklist字段（这是主要问题）
    tracklist_json = format_array_for_csv(test_data['tracklist'])
    print(f"Tracklist JSON: {tracklist_json}")
    
    # 验证JSON有效性
    try:
        parsed = json.loads(tracklist_json)
        print("✅ Tracklist JSON有效")
    except json.JSONDecodeError as e:
        print(f"❌ Tracklist JSON无效: {e}")
    
    # 验证没有换行符
    if '\n' not in tracklist_json and '\r' not in tracklist_json:
        print("✅ Tracklist JSON无换行符")
    else:
        print("❌ Tracklist JSON仍包含换行符")
    
    print()

def test_csv_generation():
    """测试CSV生成"""
    print("🧪 测试CSV生成...")
    
    test_data = create_problematic_test_data()
    test_filename = 'test_structure_fix.csv'
    
    # 生成测试CSV
    write_releases_to_csv(test_data, test_filename, append_mode=False)
    
    if os.path.exists(test_filename):
        print(f"✅ CSV文件生成成功: {test_filename}")
        
        # 验证CSV结构
        with open(test_filename, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            print(f"📊 CSV文件行数: {len(lines)}")
            
            # 检查是否有意外的换行
            expected_lines = 2  # 头部 + 1条数据
            if len([line for line in lines if line.strip()]) == expected_lines:
                print("✅ CSV结构正确")
            else:
                print(f"❌ CSV结构异常，期望 {expected_lines} 行，实际 {len([line for line in lines if line.strip()])} 行")
        
        return test_filename
    else:
        print(f"❌ CSV文件生成失败")
        return None

def test_csv_parsing():
    """测试CSV解析"""
    test_filename = 'test_structure_fix.csv'
    
    if not os.path.exists(test_filename):
        print("❌ 测试CSV文件不存在")
        return
    
    print("🧪 测试CSV解析...")
    
    try:
        import pandas as pd
        
        # 使用pandas读取
        df = pd.read_csv(test_filename)
        print(f"✅ pandas读取成功: {len(df)} 行, {len(df.columns)} 列")
        
        # 验证数据完整性
        for index, row in df.iterrows():
            print(f"行 {index+1}:")
            print(f"  ID: {row['id']}")
            print(f"  Y_ID: {row['y_id']}")
            print(f"  Title: {row['title']}")
            
            # 测试JSON字段解析
            json_fields = ['artists', 'extra_artists', 'tracklist', 'images']
            for field in json_fields:
                if field in row and pd.notna(row[field]):
                    try:
                        parsed = json.loads(str(row[field]))
                        print(f"  ✅ {field}: JSON解析成功")
                    except json.JSONDecodeError as e:
                        print(f"  ❌ {field}: JSON解析失败 - {e}")
            print()
            
    except Exception as e:
        print(f"❌ CSV解析失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始CSV结构修复测试...")
    print("=" * 60)
    
    # 测试字符串清理
    test_string_cleaning()
    
    # 测试JSON格式化
    test_json_formatting()
    
    # 测试CSV生成
    csv_filename = test_csv_generation()
    
    # 测试CSV解析
    test_csv_parsing()
    
    print("=" * 60)
    print("🏁 测试完成！")

if __name__ == "__main__":
    main()
