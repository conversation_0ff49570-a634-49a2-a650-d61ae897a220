#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json

def test_correct_json():
    """测试正确的JSON格式"""
    
    # 在JSON中，要表示包含双引号的字符串 12"，需要转义
    correct_json = r'[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\""]}]'
    print('正确的JSON:', correct_json)
    
    try:
        parsed = json.loads(correct_json)
        print('✅ 成功解析:', parsed[0]['descriptions'])
        print('实际字符串值:', repr(parsed[0]['descriptions'][0]))
        return True
    except Exception as e:
        print('❌ 失败:', e)
        return False

def test_csv_to_json_conversion():
    """测试CSV到JSON的转换"""
    
    # CSV中的原始字段
    csv_field = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    print('\nCSV字段:', csv_field)
    
    # 去掉外层引号
    field_value = csv_field[1:-1]
    print('去掉外层引号:', field_value)
    
    # 正确的转换：""12\"" 应该变成 "12\""
    field_value = field_value.replace('""12\""""', '"12\\""')
    print('修复12英寸:', field_value)
    
    # 处理其他双引号
    field_value = field_value.replace('""', '"')
    print('处理其他双引号:', field_value)
    
    try:
        parsed = json.loads(field_value)
        print('✅ 转换成功:', parsed[0]['descriptions'])
        print('实际字符串值:', repr(parsed[0]['descriptions'][0]))
        return True
    except Exception as e:
        print('❌ 转换失败:', e)
        return False

if __name__ == '__main__':
    print('🧪 测试正确的JSON格式')
    print('=' * 50)
    
    success1 = test_correct_json()
    success2 = test_csv_to_json_conversion()
    
    if success1 and success2:
        print('\n✅ 所有测试成功')
    else:
        print('\n❌ 部分测试失败')
