#!/usr/bin/env python3
print("Python 测试脚本")
print("当前工作目录:", __file__)

try:
    import sys
    print("Python 版本:", sys.version)
    
    import os
    print("当前目录:", os.getcwd())
    print("文件列表:", [f for f in os.listdir('.') if f.endswith('.py')][:5])
    
    # 测试导入
    try:
        import requests
        print("✅ requests 模块可用")
    except ImportError:
        print("❌ requests 模块不可用")
    
    try:
        import pymongo
        print("✅ pymongo 模块可用")
    except ImportError:
        print("❌ pymongo 模块不可用")
        
    print("🎉 Python 环境测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
