#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def analyze_csv_escaping():
    """分析CSV转义模式"""
    
    print('🔍 分析CSV转义模式')
    print('=' * 50)
    
    # 从实际CSV中提取的问题字段
    csv_field = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    print(f'原始CSV字段: {csv_field}')
    print(f'字段长度: {len(csv_field)}')
    
    # 分析每个字符
    print('\n字符分析:')
    for i, char in enumerate(csv_field):
        if char == '"':
            print(f'位置 {i}: "{char}" (双引号)')
        elif char == '\\':
            print(f'位置 {i}: "{char}" (反斜杠)')
    
    # 去掉外层引号
    inner = csv_field[1:-1]
    print(f'\n去掉外层引号: {inner}')
    
    # 分析 ""12\"" 这部分
    problem_part = '""12\""""'
    print(f'\n问题部分: {problem_part}')
    print('这应该表示: "12""')
    print('在JSON中应该是: "12\\""')
    
    # 正确的处理方式
    print('\n正确的处理步骤:')
    print('1. ""12\"" -> "12\\"')
    print('2. 但是我们需要在JSON中表示12"这个字符串')
    print('3. 所以应该是: "12\\""')
    
    # 测试正确的JSON
    correct_json = '[{"name": "Vinyl", "qty": "1", "text": "", "descriptions": ["12\\""]}]'
    print(f'\n正确的JSON: {correct_json}')
    
    try:
        parsed = json.loads(correct_json)
        print('✅ 正确JSON解析成功:', parsed[0]['descriptions'])
    except Exception as e:
        print('❌ 正确JSON解析失败:', e)

def test_correct_fix():
    """测试正确的修复方法"""
    print('\n🔧 测试正确的修复方法')
    print('=' * 50)
    
    csv_field = '"[{""name"": ""Vinyl"", ""qty"": ""1"", ""text"": """", ""descriptions"": [""12\""""]}]"'
    
    # 去掉外层引号
    field_value = csv_field[1:-1]
    print(f'去掉外层引号: {field_value}')
    
    # 特殊处理：先处理 ""12\"" 这种模式
    # ""12\"" 应该变成 "12\""
    field_value = re.sub(r'""([^"]*)\\"""""', r'"\1\\""', field_value)
    print(f'处理特殊模式: {field_value}')
    
    # 然后处理普通的双引号转义
    field_value = field_value.replace('""', '"')
    print(f'处理普通双引号: {field_value}')
    
    try:
        parsed = json.loads(field_value)
        print('✅ 修复成功:', parsed[0]['descriptions'])
        return True
    except Exception as e:
        print('❌ 修复失败:', e)
        return False

if __name__ == '__main__':
    analyze_csv_escaping()
    test_correct_fix()
