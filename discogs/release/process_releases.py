#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import re
import os
import glob
import sys
from pymongo import MongoClient
from datetime import datetime, timezone
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status

def find_xml_file(module_type):
    """
    从 deploy/windows/data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    data_dir = 'deploy/windows/data'
    pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    found_files = glob.glob(pattern)

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

# 配置参数
XML_FILE = find_xml_file('releases')

if not XML_FILE:
    print("❌ 错误: 无法找到 releases 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_releases.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '1000'))  # 最大处理记录数，设置为0表示处理全部数据

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')
    
    if print_to_console:
        print(message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    client = MongoClient(MONGO_URI)
    return client, client[DB_NAME]

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    # 支持带属性的标签，如 <master_id attr="value">content</master_id>
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    # 匹配标签及其属性，支持多种属性顺序
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []

    # 提取主要艺术家 (artists)
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)

    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extraartists)"""
    extra_artists = []

    # 提取额外艺术家 (extraartists)
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)

    return extra_artists



def extract_labels(content):
    """提取labels字段"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if not labels_match:
        return labels
    
    labels_content = labels_match.group(1)
    label_pattern = r'<label[^>]*name="([^"]*)"[^>]*catno="([^"]*)"[^>]*id="([^"]*)"[^>]*/?>'
    label_matches = re.findall(label_pattern, labels_content)
    
    for name, catno, label_id in label_matches:
        if name:
            labels.append({
                'name': name,
                'catno': catno,
                'id': label_id
            })
    
    return labels

def extract_companies(content):
    """提取companies字段"""
    companies = []
    companies_match = re.search(r'<companies>(.*?)</companies>', content, re.DOTALL)
    if not companies_match:
        return companies

    companies_content = companies_match.group(1)
    company_pattern = r'<company>(.*?)</company>'
    company_matches = re.findall(company_pattern, companies_content, re.DOTALL)

    for company_content in company_matches:
        company_id = extract_field(company_content, 'id')
        # 支持 <name> 和 <n> 两种标签格式
        name = extract_field(company_content, 'name') or extract_field(company_content, 'n')
        entity_type = extract_field(company_content, 'entity_type')
        entity_type_name = extract_field(company_content, 'entity_type_name')
        resource_url = extract_field(company_content, 'resource_url')

        if name:  # 只要有名称就添加
            company_doc = {
                'name': name
            }

            # 添加可选字段
            if company_id:
                company_doc['id'] = company_id
            if entity_type:
                company_doc['entity_type'] = entity_type
            if entity_type_name:
                company_doc['entity_type_name'] = entity_type_name
            if resource_url:
                company_doc['resource_url'] = resource_url

            companies.append(company_doc)

    return companies

def extract_formats(content):
    """提取formats字段"""
    formats = []
    formats_match = re.search(r'<formats>(.*?)</formats>', content, re.DOTALL)
    if not formats_match:
        return formats
    
    formats_content = formats_match.group(1)
    format_pattern = r'<format[^>]*name="([^"]*)"[^>]*qty="([^"]*)"[^>]*text="([^"]*)"[^>]*>(.*?)</format>'
    format_matches = re.findall(format_pattern, formats_content, re.DOTALL)
    
    for name, qty, text, format_inner in format_matches:
        format_doc = {
            'name': name,
            'qty': qty,
            'text': text,
            'descriptions': []
        }
        
        # 提取descriptions
        desc_pattern = r'<description>([^<]*)</description>'
        descriptions = re.findall(desc_pattern, format_inner)
        format_doc['descriptions'] = descriptions
        
        formats.append(format_doc)
    
    return formats

def extract_list_field(content, field_name, item_name):
    """提取列表字段（如genres, styles）"""
    items = []
    field_match = re.search(f'<{field_name}>(.*?)</{field_name}>', content, re.DOTALL)
    if not field_match:
        return items
    
    field_content = field_match.group(1)
    item_pattern = f'<{item_name}>([^<]*)</{item_name}>'
    items = re.findall(item_pattern, field_content)
    
    return items

def extract_identifiers(content):
    """提取identifiers字段"""
    identifiers = []
    identifiers_match = re.search(r'<identifiers>(.*?)</identifiers>', content, re.DOTALL)
    if not identifiers_match:
        return identifiers
    
    identifiers_content = identifiers_match.group(1)
    identifier_pattern = r'<identifier[^>]*type="([^"]*)"[^>]*value="([^"]*)"[^>]*description="([^"]*)"[^>]*/?>'
    identifier_matches = re.findall(identifier_pattern, identifiers_content)
    
    for type_val, value, description in identifier_matches:
        identifiers.append({
            'type': type_val,
            'value': value,
            'description': description
        })
    
    return identifiers

def extract_tracklist(content):
    """提取tracklist字段"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if not tracklist_match:
        return tracklist
    
    tracklist_content = tracklist_match.group(1)
    track_pattern = r'<track>(.*?)</track>'
    track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)
    
    for track_content in track_matches:
        position = extract_field(track_content, 'position')
        title = extract_field(track_content, 'title')
        duration = extract_field(track_content, 'duration')
        
        if position and title:
            track_doc = {
                'position': position,
                'title': title
            }
            if duration:
                track_doc['duration'] = duration
            tracklist.append(track_doc)
    
    return tracklist

def get_release_table_by_id(db, release_id):
    """从release表中获取images字段"""
    try:
        # 将release_id转换为string类型进行查询
        release_id_str = str(release_id)
        release_doc = db.release.find_one({'id': release_id_str})
        if release_doc and 'images' in release_doc:
            return release_doc
            # //['images']
        return []
    except Exception as e:
        write_output(f"获取images失败 (release_id: {release_id}): {e}", False)
        return []


def process_release_content(buffer, yid_counter, db):
    """处理单个release标签的内容"""
    # 提取release ID
    release_id = extract_release_id(buffer)
    if not release_id:
        return None

    # 生成yId
    y_id = f"YRD{yid_counter}"

    # 提取 discogs_status 并记录调试信息
    discogs_status = extract_attribute(buffer, 'release', 'status') or 'unknown'

    # 调试日志：记录 status 提取结果（仅在前几条记录中记录）
    if yid_counter <= 5:
        write_output(f"调试: release_id={release_id}, discogs_status='{discogs_status}'", False)

    old_release_doc = get_release_table_by_id(db, release_id)
    images = old_release_doc['images'];
    identifiers = old_release_doc['identifiers'];
    notes = old_release_doc['notes'];
    year = old_release_doc['year'];
    

    # 创建release文档
    release_doc = {
        'y_id': y_id,
        'id': release_id,
        'title': extract_field(buffer, 'title'),

        # —— ARTISTS ——
        'artists': extract_artists(buffer),
        'extra_artists': extract_extra_artists(buffer),

        # —— LABELS & COMPANIES ——
        'labels': extract_labels(buffer),
        'companies': extract_companies(buffer),
        'country': extract_field(buffer, 'country'),

        # —— FORMATS / GENRES / STYLES ——
        'formats': extract_formats(buffer),
        'genres': extract_list_field(buffer, 'genres', 'genre'),
        'styles': extract_list_field(buffer, 'styles', 'style'),

        # —— IDENTIFIERS & IMAGES ——
        'identifiers': identifiers,
        'images': images,
        'notes': notes,
        'year': year,

        # —— TRACKLIST ——
        'tracklist': extract_tracklist(buffer),

        # —— 其余字段 ——
        'master_id': extract_field(buffer, 'master_id'),
        'images_permissions': Permissions.ALL_VISIBLE.value,
        'permissions': Permissions.ALL_VISIBLE.value,
        # 'status': Status.ACTIVE.value,
        'source': Source.DISCOGS.value,
        'discogs_status': discogs_status,

        'created_at': datetime.now(timezone.utc),
        'updated_at': datetime.now(timezone.utc)
    }

    # 转换master_id为整数
    if release_doc['master_id']:
        try:
            release_doc['master_id'] = int(release_doc['master_id'])
        except ValueError:
            release_doc['master_id'] = None

    return release_doc

def process_releases():
    """处理XML文件中的release记录"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()

    # 确保release_new集合存在
    if 'release_new' not in db.list_collection_names():
        db.create_collection('release_new')

    # 获取集合
    release_collection = db['release']
    release_new_collection = db['release_new']

    # 清空release_new集合
    release_new_collection.delete_many({})
    write_output("已清空release_new集合")

    processed_count = 0
    total_records_found = 0
    yid_counter = 1

    try:
        write_output(f"开始处理XML文件: {XML_FILE}")

        # 打开gzip文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False

            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records_found += 1
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 处理release内容
                    release_doc = process_release_content(buffer, yid_counter, db)
                    if release_doc:
                        # 插入到release_new集合
                        release_new_collection.insert_one(release_doc)

                        processed_count += 1
                        yid_counter += 1

                        # 记录详细日志
                        title_short = release_doc['title'][:50] if release_doc['title'] else "无标题"
                        log_message = (f"处理记录 {processed_count}: y_id={release_doc['y_id']}, "
                                      f"id={release_doc['id']}, title={title_short}...")
                        write_output(log_message, False)

                        # 显示进度
                        if processed_count % 10 == 0:
                            progress_msg = f"已处理 {processed_count} 条记录..."
                            write_output(progress_msg)

                        # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                        if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                            write_output(f"已达到最大处理记录数限制 ({MAX_RECORDS})，停止处理")
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

                # 如果已达到最大记录数，跳出外层循环（MAX_RECORDS=0表示处理全部数据）
                if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                    break

    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"共处理了 {processed_count} 条记录",
            f"XML文件中共发现 {total_records_found} 条记录",
            f"最后生成的yId: YRD{yid_counter-1}",
            f"处理时长: {processing_time:.2f} 秒",
            (f"平均每条记录处理时间: {processing_time/processed_count:.4f} 秒"
             if processed_count > 0 else "平均处理时间: 0 秒"),
            "="*50
        ]

        # 打印到控制台和写入文件
        for stat in stats:
            write_output(stat)

        # 关闭数据库连接
        client.close()

        write_output(f"\n详细输出已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    process_releases()
