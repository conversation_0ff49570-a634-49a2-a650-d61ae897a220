#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import re
import os
import sys
import glob
from pymongo import MongoClient
from datetime import datetime, timezone
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Source
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Source

# 添加DeleteStatus枚举类
class DeleteStatus:
    NOT_DELETED = 'ACTIVE'
    DELETED = 'DELETED'

def find_xml_file(module_type):
    """
    查找指定模块的XML文件，优先搜索当前目录，然后搜索 deploy/windows/data 目录

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 首先在当前目录搜索（支持.xml和.xml.gz格式）
    current_dir_patterns = [
        f'*_{module_type}.xml.gz',
        f'*_{module_type}.xml',
        f'discogs_*_{module_type}.xml.gz',
        f'discogs_*_{module_type}.xml'
    ]

    found_files = []
    for pattern in current_dir_patterns:
        found_files.extend(glob.glob(pattern))

    if found_files:
        # 如果找到多个文件，选择最新的（按文件名排序）
        if len(found_files) > 1:
            found_files.sort()
            selected_file = found_files[-1]
            print(f"🔍 在当前目录找到多个文件，选择最新的: {selected_file}")
        else:
            selected_file = found_files[0]
            print(f"✅ 在当前目录检测到文件: {selected_file}")
        return selected_file

    # 如果当前目录没找到，搜索 deploy/windows/data 目录
    data_dir = 'deploy/windows/data'
    pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    found_files = glob.glob(pattern)

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   已搜索当前目录和路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 在data目录找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 在data目录检测到文件: {selected_file}")

    return selected_file

# 配置参数
XML_FILE = find_xml_file('releases')

if not XML_FILE:
    print("❌ 错误: 无法找到 releases 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_releases.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，设置为0表示处理全部数据
RESUME_FROM_RECORD = int(os.getenv('RESUME_FROM_RECORD', '0'))  # 断点续传起始位置

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def get_max_id_from_collection(collection):
    """获取集合中的最大ID值"""
    try:
        # 尝试将ID转换为数字进行排序，获取最大值
        pipeline = [
            {
                '$addFields': {
                    'id_numeric': {
                        '$toInt': '$id'
                    }
                }
            },
            {
                '$sort': {
                    'id_numeric': -1
                }
            },
            {
                '$limit': 1
            },
            {
                '$project': {
                    'id': 1
                }
            }
        ]

        result = list(collection.aggregate(pipeline))
        if result:
            max_id = result[0]['id']
            write_output(f"📊 数据库中最大ID: {max_id}", False)
            return int(max_id) if max_id else 0
        else:
            write_output(f"📊 数据库为空，最大ID设为0", False)
            return 0
    except Exception as e:
        write_output(f"⚠️ 获取最大ID失败，使用保守策略: {e}", False)
        return float('inf')  # 如果获取失败，使用保守策略（总是update）

def smart_database_operation(collection, document, max_id_in_db):
    """智能选择数据库操作策略"""
    try:
        current_id = int(document['id'])

        # 如果当前ID大于数据库中的最大ID，尝试直接插入
        if current_id > max_id_in_db:
            try:
                result = collection.insert_one(document)
                return result, "insert_one"
            except Exception as insert_error:
                # 插入失败（可能是重复键），降级为update操作
                if "duplicate key" in str(insert_error).lower() or "11000" in str(insert_error):
                    write_output(f"🔄 ID {current_id} 插入失败，降级为更新操作", False)
                    result = collection.update_one(
                        {'id': document['id']},
                        {'$set': document},
                        upsert=True
                    )
                    return result, "update_one"
                else:
                    raise insert_error
        else:
            # 当前ID小于等于最大ID，直接使用update操作
            result = collection.update_one(
                {'id': document['id']},
                {'$set': document},
                upsert=True
            )
            return result, "update_one"
    except Exception as e:
        write_output(f"❌ 智能数据库操作失败: {e}", False)
        raise

def optimized_safe_database_operation(collection, document, max_id_in_db, debug_mode=False):
    """优化的安全数据库操作，包含智能策略和重试机制"""
    max_retries = 3
    retry_count = 0

    if debug_mode:
        write_output(f"🔍 数据库操作调试 - ID: {document['id']}, y_id: {document['y_id']}", False)
        write_output(f"🔍 数据库操作调试 - 集合: {collection.name}, 最大ID: {max_id_in_db}", False)

    while retry_count < max_retries:
        try:
            result, operation_used = smart_database_operation(collection, document, max_id_in_db)
            if debug_mode:
                write_output(f"🔍 数据库操作成功 - ID: {document['id']}, 操作: {operation_used}", False)
            return result, operation_used
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                write_output(f"❌ 优化数据库操作失败，已重试{max_retries}次: {e}", False)
                raise
            else:
                write_output(f"⚠️ 优化数据库操作失败，正在重试({retry_count}/{max_retries}): {e}", False)
                time.sleep(1)  # 等待1秒后重试

def verify_database_operation(operation_result, operation_type, record_id, collection=None, debug_mode=False):
    """验证数据库操作是否成功"""
    try:
        success = False

        if operation_type == "update_one":
            if operation_result.acknowledged:
                if operation_result.upserted_id or operation_result.modified_count > 0:
                    success = True
                    if debug_mode:
                        write_output(f"✅ Update操作成功 - ID: {record_id}, upserted: {bool(operation_result.upserted_id)}, modified: {operation_result.modified_count}", False)
                else:
                    write_output(f"⚠️ 数据库操作无效果 - ID: {record_id}", False)
            else:
                write_output(f"❌ 数据库操作未确认 - ID: {record_id}", False)
        elif operation_type == "insert_one":
            if operation_result.acknowledged and operation_result.inserted_id:
                success = True
                if debug_mode:
                    write_output(f"✅ Insert操作成功 - ID: {record_id}, inserted_id: {operation_result.inserted_id}", False)
            else:
                write_output(f"❌ 数据插入失败 - ID: {record_id}", False)

        # 额外验证：查询数据库确认记录确实存在
        if success and collection and debug_mode:
            try:
                verification_doc = collection.find_one({'id': record_id})
                if verification_doc:
                    write_output(f"✅ 数据库验证成功 - ID: {record_id} 确实存在于数据库中", False)
                else:
                    write_output(f"❌ 数据库验证失败 - ID: {record_id} 在数据库中未找到", False)
                    success = False
            except Exception as verify_e:
                write_output(f"⚠️ 数据库验证查询失败 - ID: {record_id}, 错误: {verify_e}", False)

        return success
    except Exception as e:
        write_output(f"❌ 验证数据库操作时出错 - ID: {record_id}, 错误: {e}", False)
        return False

def get_release_table_by_id(db, release_id):
    """从release表中获取notes、images、years、identifiers字段"""
    try:
        # 将release_id转换为string类型进行查询
        release_id_str = str(release_id)
        release_doc = db.release.find_one({'id': release_id_str})
        if release_doc:
            return {
                'notes': release_doc.get('notes', []),
                'images': release_doc.get('images', []),
                'years': release_doc.get('year', None),  # 注意：数据库中是year，返回时用years
                'identifiers': release_doc.get('identifiers', [])
            }
        return {
            'notes': [],
            'images': [],
            'years': None,
            'identifiers': []
        }
    except Exception as e:
        write_output(f"获取数据库字段失败 (release_id: {release_id}): {e}", False)
        return {
            'notes': [],
            'images': [],
            'years': None,
            'identifiers': []
        }



def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    # 支持带属性的标签，如 <master_id attr="value">content</master_id>
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    # 匹配标签及其属性，支持多种属性顺序
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []

    # 提取主要艺术家 (artists)
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)

    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extraartists)"""
    extra_artists = []

    # 提取额外艺术家 (extraartists)
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)

    return extra_artists



def extract_labels(content):
    """提取labels字段"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if not labels_match:
        return labels
    
    labels_content = labels_match.group(1)
    label_pattern = r'<label[^>]*name="([^"]*)"[^>]*catno="([^"]*)"[^>]*id="([^"]*)"[^>]*/?>'
    label_matches = re.findall(label_pattern, labels_content)
    
    for name, catno, label_id in label_matches:
        if name:
            labels.append({
                'name': name,
                'catno': catno,
                'id': label_id
            })
    
    return labels

def extract_companies(content):
    """提取companies字段"""
    companies = []
    companies_match = re.search(r'<companies>(.*?)</companies>', content, re.DOTALL)
    if not companies_match:
        return companies

    companies_content = companies_match.group(1)
    company_pattern = r'<company>(.*?)</company>'
    company_matches = re.findall(company_pattern, companies_content, re.DOTALL)

    for company_content in company_matches:
        company_id = extract_field(company_content, 'id')
        # 支持 <name> 和 <n> 两种标签格式
        name = extract_field(company_content, 'name') or extract_field(company_content, 'n')
        entity_type = extract_field(company_content, 'entity_type')
        entity_type_name = extract_field(company_content, 'entity_type_name')
        resource_url = extract_field(company_content, 'resource_url')

        if name:  # 只要有名称就添加
            company_doc = {
                'name': name
            }

            # 添加可选字段
            if company_id:
                company_doc['id'] = company_id
            if entity_type:
                company_doc['entity_type'] = entity_type
            if entity_type_name:
                company_doc['entity_type_name'] = entity_type_name
            if resource_url:
                company_doc['resource_url'] = resource_url

            companies.append(company_doc)

    return companies

def extract_formats(content):
    """提取formats字段"""
    formats = []
    formats_match = re.search(r'<formats>(.*?)</formats>', content, re.DOTALL)
    if not formats_match:
        return formats
    
    formats_content = formats_match.group(1)
    format_pattern = r'<format[^>]*name="([^"]*)"[^>]*qty="([^"]*)"[^>]*text="([^"]*)"[^>]*>(.*?)</format>'
    format_matches = re.findall(format_pattern, formats_content, re.DOTALL)
    
    for name, qty, text, format_inner in format_matches:
        format_doc = {
            'name': name,
            'qty': qty,
            'text': text,
            'descriptions': []
        }
        
        # 提取descriptions
        desc_pattern = r'<description>([^<]*)</description>'
        descriptions = re.findall(desc_pattern, format_inner)
        format_doc['descriptions'] = descriptions
        
        formats.append(format_doc)
    
    return formats

def extract_list_field(content, field_name, item_name):
    """提取列表字段（如genres, styles）"""
    items = []
    field_match = re.search(f'<{field_name}>(.*?)</{field_name}>', content, re.DOTALL)
    if not field_match:
        return items
    
    field_content = field_match.group(1)
    item_pattern = f'<{item_name}>([^<]*)</{item_name}>'
    items = re.findall(item_pattern, field_content)
    
    return items

def extract_identifiers(content):
    """提取identifiers字段"""
    identifiers = []
    identifiers_match = re.search(r'<identifiers>(.*?)</identifiers>', content, re.DOTALL)
    if not identifiers_match:
        return identifiers
    
    identifiers_content = identifiers_match.group(1)
    identifier_pattern = r'<identifier[^>]*type="([^"]*)"[^>]*value="([^"]*)"[^>]*description="([^"]*)"[^>]*/?>'
    identifier_matches = re.findall(identifier_pattern, identifiers_content)
    
    for type_val, value, description in identifier_matches:
        identifiers.append({
            'type': type_val,
            'value': value,
            'description': description
        })
    
    return identifiers

def extract_tracklist(content):
    """提取tracklist字段"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if not tracklist_match:
        return tracklist

    tracklist_content = tracklist_match.group(1)
    track_pattern = r'<track>(.*?)</track>'
    track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)

    for track_content in track_matches:
        position = extract_field(track_content, 'position')
        title = extract_field(track_content, 'title')
        duration = extract_field(track_content, 'duration')

        if position and title:
            track_doc = {
                'position': position,
                'title': title
            }
            if duration:
                track_doc['duration'] = duration
            tracklist.append(track_doc)

    return tracklist

def extract_videos(content):
    """提取videos字段"""
    videos = []
    videos_match = re.search(r'<videos>(.*?)</videos>', content, re.DOTALL)
    if not videos_match:
        return videos

    videos_content = videos_match.group(1)
    video_pattern = r'<video[^>]*duration="([^"]*)"[^>]*embed="([^"]*)"[^>]*src="([^"]*)"[^>]*>(.*?)</video>'
    video_matches = re.findall(video_pattern, videos_content, re.DOTALL)

    for duration, embed, src, video_inner in video_matches:
        video_doc = {
            'duration': duration,
            'embed': embed == 'true',
            'src': src
        }

        # 提取title和description
        title = extract_field(video_inner, 'title')
        description = extract_field(video_inner, 'description')

        if title:
            video_doc['title'] = title
        if description:
            video_doc['description'] = description

        videos.append(video_doc)

    return videos




def process_release_content(buffer, yid_counter, db):
    """处理单个release标签的内容"""
    # 提取release ID
    release_id = extract_release_id(buffer)
    if not release_id:
        return None

    # 生成yId（修改格式为YR{counter}）
    y_id = f"YR{yid_counter}"

    # 提取 discogs_status 并记录调试信息
    discogs_status = extract_attribute(buffer, 'release', 'status') or 'unknown'

    # 调试日志：记录 status 提取结果（仅在前几条记录中记录）
    if yid_counter <= 5:
        write_output(f"调试: release_id={release_id}, discogs_status='{discogs_status}'", False)

    # 从数据库获取额外字段
    db_fields = get_release_table_by_id(db, release_id)

    # 创建release文档（用于数据库插入）
    release_doc = {
        'y_id': y_id,
        'id': str(release_id),  # 确保ID为字符串类型
        'title': extract_field(buffer, 'title'),
        'artists': extract_artists(buffer),
        'extra_artists': extract_extra_artists(buffer),
        'labels': extract_labels(buffer),
        'companies': extract_companies(buffer),
        'country': extract_field(buffer, 'country'),
        'formats': extract_formats(buffer),
        'genres': extract_list_field(buffer, 'genres', 'genre'),
        'styles': extract_list_field(buffer, 'styles', 'style'),
        'identifiers': db_fields['identifiers'],  # 从数据库获取
        'tracklist': extract_tracklist(buffer),
        'master_id': extract_field(buffer, 'master_id'),
        'discogs_status': discogs_status,

        # 从数据库获取的字段
        'notes': db_fields['notes'],
        'images': db_fields['images'],
        'years': db_fields['years'],

        # 从XML提取的视频字段
        'video': extract_videos(buffer),

        # 新增的管理字段
        'delete_status': DeleteStatus.NOT_DELETED,  # 默认为活跃状态
        'delete_at': None,  # 默认为空
        'source': Source.DISCOGS.value,  # 来源为Discogs
        'permission': Permissions.ALL_VISIBLE.value,  # 默认全部可见

        'created_at': datetime.now(timezone.utc),
        'updated_at': datetime.now(timezone.utc)
    }

    # 转换master_id为整数
    if release_doc['master_id']:
        try:
            release_doc['master_id'] = int(release_doc['master_id'])
        except ValueError:
            release_doc['master_id'] = None

    return release_doc

def process_releases(debug_mode=True):
    """处理XML文件中的release记录"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()
    write_output("已连接到MongoDB数据库")

    # 确保releases_new集合存在
    if 'releases_new' not in db.list_collection_names():
        db.create_collection('releases_new')
        write_output("创建releases_new集合")

    # 获取集合
    releases_collection = db['release']
    releases_new_collection = db['releases_new']

    # 断点续传：不清空集合，继续添加数据
    write_output(f"🔄 断点续传模式：从第 {RESUME_FROM_RECORD + 1} 条记录开始处理")
    if debug_mode:
        write_output(f"🔍 调试模式已启用")

    # 获取数据库中的最大ID，用于优化插入策略
    max_id_in_db = get_max_id_from_collection(releases_new_collection)
    write_output(f"🚀 启用智能插入优化，数据库最大ID: {max_id_in_db}")

    processed_count = 0
    total_records_found = 0
    skipped_count = 0  # 跳过的记录数
    all_found_ids = []  # 调试：记录所有找到的ID

    # 性能统计
    insert_count = 0
    update_count = 0

    try:
        write_output(f"开始处理XML文件: {XML_FILE}")

        # 判断文件是否为gzip压缩格式
        if XML_FILE.endswith('.gz'):
            file_opener = lambda: gzip.open(XML_FILE, 'rt', encoding='utf-8')
        else:
            file_opener = lambda: open(XML_FILE, 'r', encoding='utf-8')

        # 打开XML文件并逐行读取
        with file_opener() as f:
            buffer = ""
            in_release = False

            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records_found += 1
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 提取release ID用于调试
                    release_id = extract_release_id(buffer)

                    # 调试：记录所有找到的ID
                    if release_id:
                        all_found_ids.append(str(release_id))

                        # 调试：显示前20个找到的记录
                        if debug_mode and len(all_found_ids) <= 20:
                            title = extract_field(buffer, 'title') or "无标题"
                            write_output(f"🔍 找到记录 #{len(all_found_ids)}: ID={release_id}, title={title[:30]}...", False)

                    # 断点续传：跳过已处理的记录
                    if total_records_found <= RESUME_FROM_RECORD:
                        skipped_count += 1
                        buffer = ""
                        continue

                    # 处理release内容，使用连续的序号作为y_id
                    sequential_id = RESUME_FROM_RECORD + processed_count + 1
                    release_doc = process_release_content(buffer, sequential_id, db)
                    if release_doc:
                        # 查询数据库中是否存在该release
                        existing_release = releases_collection.find_one({'id': str(release_doc['id'])})

                        if existing_release and '_id' in existing_release:
                            # 保留原始_id
                            release_doc['_id'] = existing_release['_id']

                        # 使用优化的数据库操作策略
                        try:
                            operation_result, operation_used = optimized_safe_database_operation(
                                releases_new_collection,
                                release_doc,
                                max_id_in_db,
                                debug_mode and processed_count < 5  # 只对前5条记录启用详细调试
                            )

                            # 验证操作是否成功
                            if verify_database_operation(operation_result, operation_used, str(release_doc['id']),
                                                       releases_new_collection, debug_mode and processed_count < 5):
                                processed_count += 1

                                # 更新性能统计
                                if operation_used == "insert_one":
                                    insert_count += 1
                                else:
                                    update_count += 1

                            else:
                                write_output(f"❌ 数据插入验证失败 - ID: {release_doc['id']}, y_id: {release_doc['y_id']}", False)
                                continue  # 跳过这条记录，不增加processed_count

                        except Exception as e:
                            write_output(f"❌ 数据库操作异常 - ID: {release_doc['id']}, 错误: {e}", False)
                            continue  # 跳过这条记录，不增加processed_count

                        # 显示进度
                        if processed_count % 100 == 0:
                            # 优化的日志格式，包含title信息
                            release_title = release_doc.get('title', '无标题')
                            log_message = (f"处理记录 {processed_count}: y_id={release_doc['y_id']}, "
                                      f"id={release_doc['id']}, title={release_title}")
                            # 只写入文件，不打印到控制台
                            write_output(log_message, False)
                            # 优化的控制台输出格式
                            total_processed = RESUME_FROM_RECORD + processed_count
                            print(f"已处理 {processed_count} 条记录（总计 {total_processed} 条）| ID={release_doc['id']}, title={release_title[:30]}...")

                        # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                        if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                            write_output(f"已达到最大处理记录数限制 ({MAX_RECORDS})，停止处理")
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

                # 每扫描10000条记录显示进度
                if total_records_found % 10000 == 0:
                    print(f"📊 已扫描 {total_records_found} 条记录，处理 {processed_count} 条记录...")

    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 关闭数据库连接
        client.close()
        write_output("已关闭MongoDB连接")

        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*60,
            "📈 处理结果统计",
            "="*60,
            f"📊 扫描记录总数: {total_records_found}",
            f"⏭️  跳过记录数: {skipped_count}",
            f"✅ 成功处理记录: {processed_count}",
            f"📄 数据库集合: releases_new",
            f"🔧 性能统计:",
            f"   - 插入操作: {insert_count}",
            f"   - 更新操作: {update_count}",
            f"⏱️  处理时长: {processing_time:.2f} 秒",
            (f"⚡ 平均每条记录处理时间: {processing_time/processed_count:.4f} 秒"
             if processed_count > 0 else "⚡ 平均处理时间: 0 秒"),
            "="*60
        ]

        # 打印到控制台和写入文件
        for stat in stats:
            write_output(stat)

        write_output(f"\n详细输出已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    # 默认执行数据库处理功能
    process_releases(debug_mode=True)
