# Discogs Release 数据处理工具

## 项目概述

这是一个用于处理 Discogs 音乐数据库 XML 文件的 Python 工具。该工具能够解析 Discogs releases XML 文件（支持 gzip 压缩格式），提取音乐发行信息并将其存储到 MongoDB 数据库中。

### 主要功能

- 🎵 **XML 解析**：支持解析 Discogs releases XML 文件（包括 gzip 压缩文件）
- 🗄️ **数据库存储**：将解析后的数据存储到 MongoDB 数据库
- 🎨 **复杂数据结构**：支持艺术家、标签、公司、格式、流派、曲目列表等复杂数据结构
- 📊 **处理统计**：提供详细的处理进度和统计信息
- ✅ **结果验证**：内置验证工具检查处理结果
- 📝 **详细日志**：生成详细的处理日志文件

## 环境要求

- **Python**: 3.7+
- **MongoDB**: 4.0+
- **操作系统**: Linux, macOS, Windows

## 依赖安装

### 1. 克隆或下载项目

```bash
# 如果是 git 仓库
git clone <repository-url>
cd discogs/release

# 或者直接进入项目目录
cd /path/to/discogs/release
```

### 2. 安装 Python 依赖

```bash
# 使用 pip 安装
pip install -r requirements.txt

# 或者使用 pip3
pip3 install -r requirements.txt

# 如果使用虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 3. 准备数据文件

确保您有 Discogs releases XML 文件：

- 文件名：`discogs_20250501_releases.xml.gz`
- 放置位置：项目根目录下
- 格式：gzip 压缩的 XML 文件

## 配置说明

### 数据库配置

编辑 `process_releases.py` 文件中的数据库配置：

```python
# 配置参数
XML_FILE = 'discogs_20250501_releases.xml.gz'  # XML 文件路径
MONGO_URI = '********************************:port/database'  # MongoDB 连接字符串
DB_NAME = 'music_test'  # 数据库名称
MAX_RECORDS = 100  # 最大处理记录数（用于测试，生产环境可设置更大值）
```

### 重要配置项说明

| 配置项        | 说明               | 默认值                                                       | 建议                                           |
| ------------- | ------------------ | ------------------------------------------------------------ | ---------------------------------------------- |
| `XML_FILE`    | XML 文件路径       | `discogs_20250501_releases.xml.gz`                           | 根据实际文件名修改                             |
| `MONGO_URI`   | MongoDB 连接字符串 | `**********************************************************` | 修改为您的数据库信息                           |
| `DB_NAME`     | 数据库名称         | `music_test`                                                 | 根据需要修改                                   |
| `MAX_RECORDS` | 最大处理记录数     | `100`                                                        | 测试时使用小值，生产时可设置为更大值或删除限制 |

## 使用方法

### 快速开始

1. **运行主处理脚本**：

```bash
python3 run_process.py
```

2. **验证处理结果**：

```bash
python verify_results.py
```

### 详细使用步骤

#### 步骤 1：准备环境

```bash
# 检查 Python 版本
python --version

# 检查依赖是否安装
python -c "import pymongo; print('pymongo 已安装')"

# 检查 XML 文件是否存在
ls -la discogs_20250501_releases.xml.gz
```

#### 步骤 2：配置数据库

```bash
# 测试 MongoDB 连接
python -c "
from pymongo import MongoClient
client = MongoClient('your_mongo_uri')
print('MongoDB 连接成功')
client.close()
"
```

#### 步骤 3：运行处理

```bash
# 运行主处理脚本
python run_process.py

# 或者直接运行处理模块
python process_releases.py
```

#### 步骤 4：验证结果

```bash
# 运行验证脚本
python verify_results.py
```

### 输出文件

- **process_output.txt**：详细的处理日志文件
- **MongoDB 集合**：`release_new` - 存储处理后的数据

## 项目结构

```
discogs/release/
├── README.md                           # 项目说明文档
├── requirements.txt                    # Python 依赖列表
├── run_process.py                      # 主运行脚本
├── process_releases.py                 # 核心处理逻辑
├── verify_results.py                   # 结果验证脚本
├── enums.py                           # 枚举定义
├── ReleaseSchema.py                   # 数据模式定义
├── discogs_20250501_releases.xml.gz   # Discogs XML 数据文件
├── process_output.txt                 # 处理日志输出（运行后生成）
├── test.xml                           # 测试用 XML 文件
├── test1.xml                          # 测试用 XML 文件
└── __pycache__/                       # Python 缓存目录
```

### 核心文件说明

| 文件                  | 功能         | 说明                           |
| --------------------- | ------------ | ------------------------------ |
| `run_process.py`      | 主入口脚本   | 检查环境并启动处理流程         |
| `process_releases.py` | 核心处理逻辑 | XML 解析、数据转换、数据库存储 |
| `verify_results.py`   | 结果验证     | 检查处理结果的完整性和正确性   |
| `enums.py`            | 枚举定义     | 定义权限、状态等枚举值         |
| `ReleaseSchema.py`    | 数据模式     | 定义数据结构和验证规则         |

## 数据结构

### Release 文档结构

```json
{
  "y_id": "YRD1",
  "id": 123456,
  "title": "专辑标题",
  "artists": [
    {
      "artist_id": 789,
      "name": "艺术家名称",
      "role": "Primary",
      "anv": "别名"
    }
  ],
  "extra_artists": [...],
  "labels": [
    {
      "name": "唱片公司",
      "catno": "目录号",
      "id": "标签ID"
    }
  ],
  "companies": ["公司1", "公司2"],
  "country": "国家",
  "formats": [...],
  "genres": ["流派1", "流派2"],
  "styles": ["风格1", "风格2"],
  "identifiers": [...],
  "images": [...],
  "tracklist": [...],
  "master_id": 456789,
  "images_permissions": 1,
  "permissions": 1,
  "status": 1,
  "discogs_status": "Accepted",
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z"
}
```

## 故障排除

### 常见问题

#### 1. 找不到 XML 文件

```
错误: 找不到XML文件 discogs_20250501_releases.xml.gz
```

**解决方案**：

- 确保 XML 文件在项目根目录下
- 检查文件名是否正确
- 如果文件名不同，修改 `process_releases.py` 中的 `XML_FILE` 配置

#### 2. MongoDB 连接失败

```
pymongo.errors.ServerSelectionTimeoutError
```

**解决方案**：

- 检查 MongoDB 服务是否运行
- 验证连接字符串是否正确
- 检查网络连接和防火墙设置
- 确认用户名和密码是否正确

#### 3. 依赖安装失败

```
ModuleNotFoundError: No module named 'pymongo'
```

**解决方案**：

```bash
pip install pymongo
# 或
pip install -r requirements.txt
```

#### 4. 内存不足

如果处理大文件时出现内存问题：

- 减少 `MAX_RECORDS` 的值
- 分批处理数据
- 增加系统内存

#### 5. 权限问题

```
PermissionError: [Errno 13] Permission denied
```

**解决方案**：

- 检查文件和目录权限
- 使用 `chmod` 命令修改权限（Linux/macOS）
- 以管理员身份运行（Windows）

### 调试技巧

1. **启用详细日志**：

   - 查看 `process_output.txt` 文件
   - 在代码中添加更多 `write_output()` 调用

2. **测试小数据集**：

   - 将 `MAX_RECORDS` 设置为较小值（如 10）
   - 使用测试 XML 文件

3. **检查数据库状态**：

   ```bash
   python verify_results.py
   ```

4. **手动检查数据库**：
   ```python
   from pymongo import MongoClient
   client = MongoClient('your_mongo_uri')
   db = client['music_test']
   print(db.release_new.count_documents({}))
   ```

## 性能优化

### 处理大文件的建议

1. **调整批处理大小**：

   - 修改 `MAX_RECORDS` 参数
   - 分批处理大型 XML 文件

2. **数据库优化**：

   - 为常用查询字段创建索引
   - 使用批量插入操作

3. **内存管理**：
   - 监控内存使用情况
   - 适当清理缓冲区

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件至：[<EMAIL>]

## 更新日志

### v1.0.0 (2025-01-01)

- 初始版本发布
- 支持 Discogs releases XML 文件解析
- MongoDB 数据存储功能
- 结果验证工具
