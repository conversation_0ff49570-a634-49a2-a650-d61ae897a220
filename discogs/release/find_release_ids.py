#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import re
import os
import glob
import sys
from datetime import datetime, timezone

def find_xml_file(module_type):
    """
    从当前目录和 deploy/windows/data 目录获取指定模块的XML文件
    
    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')
    
    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 首先在当前目录查找
    current_dir_pattern = f'*_{module_type}.xml.gz'
    current_dir_files = glob.glob(current_dir_pattern)
    
    # 然后在当前目录查找（与脚本同级）
    data_dir = '.'
    data_dir_pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    data_dir_files = glob.glob(data_dir_pattern)
    
    # 合并所有找到的文件
    found_files = current_dir_files + data_dir_files
    
    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径1: {current_dir_pattern}")
        print(f"   搜索路径2: {data_dir_pattern}")
        return None
    
    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")
    
    return selected_file

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []
    
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)
        
        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"
            
            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)
    
    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extraartists)"""
    extra_artists = []
    
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)
        
        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"
            
            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)
    
    return extra_artists

def extract_labels(content):
    """提取labels字段"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if not labels_match:
        return labels
    
    labels_content = labels_match.group(1)
    label_pattern = r'<label[^>]*name="([^"]*)"[^>]*catno="([^"]*)"[^>]*id="([^"]*)"[^>]*/?>'
    label_matches = re.findall(label_pattern, labels_content)
    
    for name, catno, label_id in label_matches:
        if name:
            labels.append({
                'name': name,
                'catno': catno,
                'id': label_id
            })
    
    return labels

def extract_list_field(content, field_name, item_name):
    """提取列表字段（如genres, styles）"""
    items = []
    field_match = re.search(f'<{field_name}>(.*?)</{field_name}>', content, re.DOTALL)
    if not field_match:
        return items
    
    field_content = field_match.group(1)
    item_pattern = f'<{item_name}>([^<]*)</{item_name}>'
    items = re.findall(item_pattern, field_content)
    
    return items

def extract_tracklist(content):
    """提取tracklist字段"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if not tracklist_match:
        return tracklist
    
    tracklist_content = tracklist_match.group(1)
    track_pattern = r'<track>(.*?)</track>'
    track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)
    
    for track_content in track_matches:
        position = extract_field(track_content, 'position')
        title = extract_field(track_content, 'title')
        duration = extract_field(track_content, 'duration')
        
        if position and title:
            track_doc = {
                'position': position,
                'title': title
            }
            if duration:
                track_doc['duration'] = duration
            tracklist.append(track_doc)
    
    return tracklist

def format_release_output(release_data):
    """格式化输出release记录信息"""
    print("=" * 80)
    print(f"🎵 Release ID: {release_data['id']}")
    print(f"📀 标题: {release_data.get('title', '无标题')}")
    print(f"🌍 国家: {release_data.get('country', '未知')}")
    print(f"📅 年份: {release_data.get('year', '未知')}")
    print(f"🎭 状态: {release_data.get('discogs_status', '未知')}")
    
    if release_data.get('master_id'):
        print(f"🎼 Master ID: {release_data['master_id']}")
    
    # 显示艺术家信息
    if release_data.get('artists'):
        print("\n👨‍🎤 主要艺术家:")
        for artist in release_data['artists']:
            print(f"   - {artist['name']} (ID: {artist['artist_id']}, 角色: {artist['role']})")
    
    if release_data.get('extra_artists'):
        print("\n👥 额外艺术家:")
        for artist in release_data['extra_artists']:
            print(f"   - {artist['name']} (ID: {artist['artist_id']}, 角色: {artist['role']})")
    
    # 显示厂牌信息
    if release_data.get('labels'):
        print("\n🏷️ 厂牌:")
        for label in release_data['labels']:
            print(f"   - {label['name']} (目录号: {label['catno']}, ID: {label['id']})")
    
    # 显示风格和类型
    if release_data.get('genres'):
        print(f"\n🎵 类型: {', '.join(release_data['genres'])}")
    
    if release_data.get('styles'):
        print(f"🎨 风格: {', '.join(release_data['styles'])}")
    
    # 显示曲目列表（仅显示前5首）
    if release_data.get('tracklist'):
        print(f"\n📝 曲目列表 (共{len(release_data['tracklist'])}首):")
        for i, track in enumerate(release_data['tracklist'][:5]):
            duration_info = f" ({track['duration']})" if track.get('duration') else ""
            print(f"   {track['position']}. {track['title']}{duration_info}")
        if len(release_data['tracklist']) > 5:
            print(f"   ... 还有 {len(release_data['tracklist']) - 5} 首曲目")
    
    print("=" * 80)
    print()

def find_release_by_ids(target_ids):
    """在XML文件中查找指定的release ID"""
    # 查找XML文件
    xml_file = find_xml_file('releases')
    if not xml_file:
        print("❌ 错误: 无法找到 releases 模块的XML数据文件")
        return
    
    print(f"\n🔍 开始在文件 {xml_file} 中查找 Release IDs: {target_ids}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    found_releases = {}
    processed_count = 0
    start_time = time.time()
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            
            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 提取并检查release ID
                    release_id = extract_release_id(buffer)
                    if release_id and release_id in target_ids:
                        print(f"✅ 找到目标记录: Release ID {release_id}")
                        
                        # 提取完整的release信息
                        release_data = {
                            'id': release_id,
                            'title': extract_field(buffer, 'title'),
                            'country': extract_field(buffer, 'country'),
                            'year': extract_field(buffer, 'year'),
                            'master_id': extract_field(buffer, 'master_id'),
                            'discogs_status': extract_attribute(buffer, 'release', 'status') or 'unknown',
                            'artists': extract_artists(buffer),
                            'extra_artists': extract_extra_artists(buffer),
                            'labels': extract_labels(buffer),
                            'genres': extract_list_field(buffer, 'genres', 'genre'),
                            'styles': extract_list_field(buffer, 'styles', 'style'),
                            'tracklist': extract_tracklist(buffer)
                        }
                        
                        # 转换master_id为整数
                        if release_data['master_id']:
                            try:
                                release_data['master_id'] = int(release_data['master_id'])
                            except ValueError:
                                release_data['master_id'] = None
                        
                        found_releases[release_id] = release_data
                        
                        # 如果找到了所有目标ID，就停止处理
                        if len(found_releases) == len(target_ids):
                            print(f"🎉 已找到所有目标记录，停止处理")
                            break
                    
                    processed_count += 1
                    if processed_count % 10000 == 0:
                        elapsed = time.time() - start_time
                        print(f"⏳ 已处理 {processed_count} 条记录，耗时 {elapsed:.1f} 秒，已找到 {len(found_releases)} 个目标记录")
                    
                    buffer = ""
                elif in_release:
                    buffer += line
                
                # 如果已找到所有目标记录，跳出外层循环
                if len(found_releases) == len(target_ids):
                    break
    
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        return
    
    # 输出结果
    processing_time = time.time() - start_time
    print(f"\n📊 处理完成!")
    print(f"⏱️ 总耗时: {processing_time:.2f} 秒")
    print(f"📈 处理记录数: {processed_count}")
    print(f"🎯 找到记录数: {len(found_releases)}/{len(target_ids)}")
    
    # 显示找到的记录
    if found_releases:
        print(f"\n📋 找到的记录详情:")
        for release_id in target_ids:
            if release_id in found_releases:
                format_release_output(found_releases[release_id])
            else:
                print(f"❌ 未找到 Release ID: {release_id}")
    else:
        print(f"\n❌ 未找到任何目标记录")

def extract_first_40k_releases():
    """提取前40,000条release记录到XML文件"""
    # 查找XML文件
    xml_file = find_xml_file('releases')
    if not xml_file:
        print("❌ 错误: 无法找到 releases 模块的XML数据文件")
        return

    output_file = 'first_40k_releases.xml'
    max_records = 40000

    print(f"\n📦 开始提取前 {max_records:,} 条 Release 记录")
    print(f"📁 源文件: {xml_file}")
    print(f"📄 输出文件: {output_file}")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    extracted_count = 0
    start_time = time.time()

    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as input_f, \
             open(output_file, 'w', encoding='utf-8') as output_f:

            # 写入XML头部
            output_f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            output_f.write('<releases>\n')

            buffer = ""
            in_release = False

            for line in input_f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 写入完整的release记录
                    output_f.write(buffer)
                    extracted_count += 1

                    # 显示进度
                    if extracted_count % 1000 == 0:
                        elapsed = time.time() - start_time
                        print(f"⏳ 已提取 {extracted_count:,} 条记录，耗时 {elapsed:.1f} 秒")

                    # 达到目标数量时停止
                    if extracted_count >= max_records:
                        print(f"🎉 已达到目标数量 {max_records:,} 条记录，停止提取")
                        break

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

            # 写入XML尾部
            output_f.write('</releases>\n')

    except Exception as e:
        print(f"❌ 提取过程中出错: {e}")
        return

    # 输出结果统计
    processing_time = time.time() - start_time
    print(f"\n📊 提取完成!")
    print(f"⏱️ 总耗时: {processing_time:.2f} 秒")
    print(f"📈 提取记录数: {extracted_count:,}")
    print(f"📄 输出文件: {output_file}")
    print(f"📏 平均处理速度: {extracted_count/processing_time:.0f} 条记录/秒")

def main():
    """主函数"""
    print("🎵 Discogs Release 数据处理工具")
    print("=" * 50)
    print("请选择功能:")
    print("1. 查找特定 Release ID")
    print("2. 提取前 40,000 条记录到 XML 文件")

    choice = input("\n请输入选择 (1 或 2): ").strip()

    if choice == "1":
        # 目标release ID列表
        target_ids = [36610, 36608]
        print(f"\n🔍 开始查找 Release IDs: {target_ids}")
        find_release_by_ids(target_ids)
    elif choice == "2":
        extract_first_40k_releases()
    else:
        print("❌ 无效选择，请输入 1 或 2")

if __name__ == "__main__":
    main()
