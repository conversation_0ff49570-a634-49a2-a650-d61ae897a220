#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API补全器测试脚本

功能：
1. 测试API连接和数据获取
2. 验证数据转换功能
3. 测试数据库操作
4. 检查缺失ID检测逻辑

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import sys
import json
import time
from pymongo import MongoClient

# 设置测试模式环境变量
os.environ['TEST_MODE'] = 'true'
os.environ['MAX_RECORDS'] = '5'
os.environ['START_ID'] = '1'

# 导入主模块
try:
    from api_release_补全器 import (
        DiscogsAPIClient, 
        convert_api_response_to_document,
        connect_to_mongodb,
        find_missing_ids,
        get_next_yid_counter
    )
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保 api_release_补全器.py 文件存在且无语法错误")
    sys.exit(1)

def test_api_connection():
    """测试API连接"""
    print("🧪 测试1: API连接测试")
    print("-" * 40)
    
    try:
        client = DiscogsAPIClient()
        
        # 测试一个已知存在的release ID
        test_id = 49  # Discogs的经典测试ID
        print(f"📡 测试API调用: release ID {test_id}")
        
        result = client.get_release(test_id)
        
        if result is None:
            print(f"⚠️ ID {test_id} 不存在 (404)")
        elif result is False:
            print(f"❌ API请求失败")
        else:
            print(f"✅ API调用成功")
            print(f"   标题: {result.get('title', 'N/A')}")
            print(f"   艺术家数量: {len(result.get('artists', []))}")
            print(f"   发行年份: {result.get('year', 'N/A')}")
            print(f"   国家: {result.get('country', 'N/A')}")
            return result
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return None
    
    print()

def test_data_conversion(api_data, db):
    """测试数据转换"""
    print("🧪 测试2: 数据转换测试")
    print("-" * 40)

    if not api_data:
        print("⏭️ 跳过数据转换测试（无API数据）")
        print()
        return None

    try:
        y_id = "YRD999999"  # 测试用的y_id
        doc = convert_api_response_to_document(api_data, y_id, db)
        
        if doc:
            print("✅ 数据转换成功")
            print(f"   Y_ID: {doc.get('y_id')}")
            print(f"   ID: {doc.get('id')}")
            print(f"   标题: {doc.get('title', '')[:50]}...")
            print(f"   艺术家数量: {len(doc.get('artists', []))}")
            print(f"   额外艺术家数量: {len(doc.get('extra_artists', []))}")
            print(f"   标签数量: {len(doc.get('labels', []))}")
            print(f"   格式数量: {len(doc.get('formats', []))}")
            print(f"   曲目数量: {len(doc.get('tracklist', []))}")
            
            # 验证必需字段
            required_fields = ['y_id', 'id', 'title', 'artists', 'created_at']
            missing_fields = [field for field in required_fields if field not in doc]
            
            if missing_fields:
                print(f"⚠️ 缺少必需字段: {missing_fields}")
            else:
                print("✅ 所有必需字段都存在")
            
            return doc
        else:
            print("❌ 数据转换失败")
            return None
            
    except Exception as e:
        print(f"❌ 数据转换测试失败: {e}")
        return None
    
    print()

def test_database_connection():
    """测试数据库连接"""
    print("🧪 测试3: 数据库连接测试")
    print("-" * 40)
    
    try:
        client, db = connect_to_mongodb()
        
        # 测试基本连接
        collections = db.list_collection_names()
        print(f"✅ 数据库连接成功")
        print(f"   数据库名: {db.name}")
        print(f"   集合数量: {len(collections)}")
        
        # 检查release_new集合
        if 'release_new' in collections:
            collection = db['release_new']
            count = collection.count_documents({})
            print(f"   release_new记录数: {count:,}")
            
            # 测试y_id计数器
            next_counter = get_next_yid_counter(collection)
            print(f"   下一个YRD编号: YRD{next_counter}")
        else:
            print("   ⚠️ release_new集合不存在")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False
    
    print()

def test_missing_ids_detection():
    """测试缺失ID检测"""
    print("🧪 测试4: 缺失ID检测测试")
    print("-" * 40)
    
    try:
        client, db = connect_to_mongodb()
        
        # 测试小范围的缺失ID检测
        print("📊 检测ID范围 1-100 的缺失情况...")
        start_time = time.time()
        
        # 模拟检测逻辑（简化版）
        collection = db['release_new']
        existing_ids = set()
        
        cursor = collection.find({'id': {'$gte': 1, '$lte': 100}}, {'id': 1, '_id': 0})
        for doc in cursor:
            existing_ids.add(doc['id'])
        
        all_ids = set(range(1, 101))
        missing_ids = all_ids - existing_ids
        
        elapsed = time.time() - start_time
        
        print(f"✅ 缺失ID检测完成")
        print(f"   检测范围: 1-100")
        print(f"   现有ID数量: {len(existing_ids)}")
        print(f"   缺失ID数量: {len(missing_ids)}")
        print(f"   检测耗时: {elapsed:.3f} 秒")
        
        if missing_ids:
            missing_list = sorted(list(missing_ids))[:10]  # 只显示前10个
            print(f"   前10个缺失ID: {missing_list}")
        
        client.close()
        return len(missing_ids)
        
    except Exception as e:
        print(f"❌ 缺失ID检测测试失败: {e}")
        return 0
    
    print()

def test_full_workflow():
    """测试完整工作流程"""
    print("🧪 测试5: 完整工作流程测试")
    print("-" * 40)
    
    try:
        # 1. API连接测试
        api_data = test_api_connection()

        # 3. 数据库连接测试（需要先连接数据库）
        db_ok = test_database_connection()

        # 获取数据库连接用于数据转换测试
        db = None
        if db_ok:
            try:
                from pymongo import MongoClient
                MONGO_URI = '**********************************************************'
                client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
                db = client['music_test']
            except Exception as e:
                print(f"⚠️ 获取数据库连接失败: {e}")

        # 2. 数据转换测试
        doc = test_data_conversion(api_data, db)
        
        # 4. 缺失ID检测测试
        missing_count = test_missing_ids_detection()
        
        # 5. 综合评估
        print("📊 综合测试结果")
        print("-" * 40)
        
        tests_passed = 0
        total_tests = 4
        
        if api_data:
            print("✅ API连接: 通过")
            tests_passed += 1
        else:
            print("❌ API连接: 失败")
        
        if doc:
            print("✅ 数据转换: 通过")
            tests_passed += 1
        else:
            print("❌ 数据转换: 失败")
        
        if db_ok:
            print("✅ 数据库连接: 通过")
            tests_passed += 1
        else:
            print("❌ 数据库连接: 失败")
        
        if missing_count >= 0:
            print("✅ 缺失ID检测: 通过")
            tests_passed += 1
        else:
            print("❌ 缺失ID检测: 失败")
        
        print(f"\n🎯 测试通过率: {tests_passed}/{total_tests} ({tests_passed/total_tests*100:.1f}%)")
        
        if tests_passed == total_tests:
            print("🎉 所有测试通过！API补全器已准备就绪")
            if missing_count > 0:
                print(f"💡 发现 {missing_count} 个缺失ID可以补全")
        else:
            print("⚠️ 部分测试失败，请检查配置和网络连接")
        
    except Exception as e:
        print(f"❌ 完整工作流程测试失败: {e}")

def main():
    """主函数"""
    print("🚀 API补全器测试脚本启动")
    print("=" * 50)
    print()
    
    # 显示测试配置
    print("📋 测试配置:")
    print(f"   测试模式: {os.getenv('TEST_MODE', 'false')}")
    print(f"   最大记录数: {os.getenv('MAX_RECORDS', '0')}")
    print(f"   起始ID: {os.getenv('START_ID', '1')}")
    print()
    
    # 运行测试
    test_full_workflow()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
