#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CSV输出功能测试脚本

功能：
1. 测试API调用
2. 测试数据转换（包括从release表获取images）
3. 测试CSV输出功能
4. 验证CSV文件格式

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import sys
import json
import csv

# 设置测试模式环境变量
os.environ['TEST_MODE'] = 'true'
os.environ['MAX_RECORDS'] = '3'
os.environ['START_ID'] = '1'

def test_csv_functions():
    """测试CSV相关函数"""
    print("🧪 测试CSV功能")
    print("-" * 40)
    
    try:
        from api_release_补全器 import (
            format_array_for_csv,
            write_releases_to_csv,
            convert_api_response_to_document,
            DiscogsAPIClient,
            connect_to_mongodb
        )
        
        # 测试数组格式化
        print("📝 测试数组格式化...")
        test_arrays = [
            [],
            ["item1", "item2"],
            [{"name": "test", "id": 123}],
            "single_item"
        ]
        
        for i, arr in enumerate(test_arrays):
            result = format_array_for_csv(arr)
            print(f"   数组{i+1}: {arr} -> {result}")
        
        # 测试API和数据转换
        print("\n📡 测试API调用和数据转换...")
        
        # 连接数据库
        client, db = connect_to_mongodb()
        
        # 创建API客户端
        api_client = DiscogsAPIClient()
        
        # 测试API调用
        test_id = 49
        api_data = api_client.get_release(test_id)
        
        if api_data and api_data is not False:
            print(f"✅ API调用成功: {api_data.get('title', 'N/A')}")
            
            # 测试数据转换
            y_id = "YRD999999"
            doc = convert_api_response_to_document(api_data, y_id, db)
            
            if doc:
                print("✅ 数据转换成功")
                print(f"   Y_ID: {doc.get('y_id')}")
                print(f"   标题: {doc.get('title', '')[:50]}...")
                print(f"   Images数量: {len(doc.get('images', []))}")
                
                # 测试CSV写入
                print("\n📄 测试CSV写入...")
                test_csv_file = 'test_output.csv'
                
                # 删除已存在的测试文件
                if os.path.exists(test_csv_file):
                    os.remove(test_csv_file)
                
                # 写入测试数据
                write_releases_to_csv([doc], test_csv_file, append_mode=False)
                
                # 验证CSV文件
                if os.path.exists(test_csv_file):
                    print("✅ CSV文件创建成功")
                    
                    # 读取并显示CSV内容
                    with open(test_csv_file, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        rows = list(reader)
                        
                        print(f"   行数: {len(rows)} (包含表头)")
                        print(f"   列数: {len(rows[0]) if rows else 0}")
                        
                        if len(rows) >= 2:
                            print("   表头字段:")
                            for i, header in enumerate(rows[0][:10]):  # 只显示前10个字段
                                print(f"     {i+1}. {header}")
                            if len(rows[0]) > 10:
                                print(f"     ... 还有 {len(rows[0]) - 10} 个字段")
                            
                            print("   数据示例:")
                            data_row = rows[1]
                            for i, (header, value) in enumerate(zip(rows[0][:5], data_row[:5])):
                                display_value = value[:50] + "..." if len(value) > 50 else value
                                print(f"     {header}: {display_value}")
                    
                    # 清理测试文件
                    os.remove(test_csv_file)
                    print("✅ 测试文件已清理")
                    
                else:
                    print("❌ CSV文件创建失败")
                    return False
            else:
                print("❌ 数据转换失败")
                return False
        else:
            print("❌ API调用失败")
            return False
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_batch_csv_writing():
    """测试批量CSV写入"""
    print("\n🧪 测试批量CSV写入")
    print("-" * 40)
    
    try:
        from api_release_补全器 import write_releases_to_csv
        
        # 创建测试数据
        test_docs = []
        for i in range(5):
            doc = {
                'id': f'test_{i}',
                'y_id': f'YRD{i}',
                'title': f'测试专辑 {i}',
                'artists': [{'name': f'艺术家{i}', 'id': i}],
                'extra_artists': [],
                'labels': [{'name': f'标签{i}', 'catno': f'CAT{i}'}],
                'companies': [f'公司{i}'],
                'country': 'CN',
                'formats': [{'name': 'CD', 'qty': '1'}],
                'genres': ['Test'],
                'styles': ['TestStyle'],
                'identifiers': [],
                'tracklist': [{'position': '1', 'title': f'曲目{i}'}],
                'master_id': None,
                'discogs_status': 'Accepted',
                'images': [],
                'notes': f'测试备注{i}',
                'year': 2025,
                'images_permissions': 1,
                'permissions': 1,
                'source': 1,
                'created_at': '2025-07-28T12:00:00Z',
                'updated_at': '2025-07-28T12:00:00Z'
            }
            test_docs.append(doc)
        
        test_csv_file = 'test_batch_output.csv'
        
        # 删除已存在的测试文件
        if os.path.exists(test_csv_file):
            os.remove(test_csv_file)
        
        # 分批写入测试
        print("📝 分批写入测试...")
        
        # 第一批（创建文件）
        write_releases_to_csv(test_docs[:2], test_csv_file, append_mode=False)
        print("   第一批写入完成")
        
        # 第二批（追加模式）
        write_releases_to_csv(test_docs[2:4], test_csv_file, append_mode=True)
        print("   第二批追加完成")
        
        # 第三批（追加模式）
        write_releases_to_csv(test_docs[4:], test_csv_file, append_mode=True)
        print("   第三批追加完成")
        
        # 验证结果
        if os.path.exists(test_csv_file):
            with open(test_csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = list(reader)
                
                print(f"✅ 批量写入成功")
                print(f"   总行数: {len(rows)} (包含表头)")
                print(f"   数据行数: {len(rows) - 1}")
                print(f"   预期数据行数: {len(test_docs)}")
                
                if len(rows) - 1 == len(test_docs):
                    print("✅ 数据行数匹配")
                else:
                    print("❌ 数据行数不匹配")
                    return False
            
            # 清理测试文件
            os.remove(test_csv_file)
            print("✅ 测试文件已清理")
            return True
        else:
            print("❌ 批量CSV文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 批量写入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 CSV输出功能测试脚本启动")
    print("=" * 50)
    print()
    
    tests_passed = 0
    total_tests = 2
    
    # 测试1: CSV基础功能
    if test_csv_functions():
        tests_passed += 1
        print("✅ CSV基础功能测试通过")
    else:
        print("❌ CSV基础功能测试失败")
    
    # 测试2: 批量CSV写入
    if test_batch_csv_writing():
        tests_passed += 1
        print("✅ 批量CSV写入测试通过")
    else:
        print("❌ 批量CSV写入测试失败")
    
    print(f"\n📊 测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有CSV功能测试通过！")
        print("💡 API补全器的CSV输出功能已准备就绪")
    else:
        print("⚠️ 部分测试失败，请检查配置")
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
