#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于CSV文件执行重复数据删除脚本

功能：
1. 读取重复数据检测生成的CSV文件
2. 基于CSV中的mongo_id列表执行精确删除
3. 提供预览模式和执行模式
4. 分批删除以避免内存问题
5. 详细的删除日志和统计报告

安全策略：
- 默认预览模式，需要明确指定才执行删除
- 删除前显示详细的删除计划
- 要求用户确认后才执行删除
- 分批处理避免内存溢出
- 完整的删除前后统计对比

作者：AI Assistant
创建时间：2025-07-28
"""

import os
import sys
import csv
import time
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'
BATCH_SIZE = 100  # 批删除大小

# 输入和输出文件
INPUT_CSV = 'release_new_duplicate_results/release_new_duplicates_to_delete.csv'
OUTPUT_DIR = 'csv_deletion_results'
LOG_FILE = os.path.join(OUTPUT_DIR, 'csv_based_deletion.log')
DELETION_REPORT = os.path.join(OUTPUT_DIR, 'deletion_execution_report.csv')

# 确保输出目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 清理旧的日志文件
if os.path.exists(LOG_FILE):
    os.remove(LOG_FILE)

def write_log(message, print_to_console=True, level="INFO"):
    """写入日志文件并可选择打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] [{level}] {message}"
    
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_log(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_log(f"❌ MongoDB连接失败: {e}", level="ERROR")
        raise

def load_deletion_list():
    """从CSV文件加载删除列表"""
    write_log("📄 加载CSV删除列表...")
    
    if not os.path.exists(INPUT_CSV):
        write_log(f"❌ 找不到CSV文件: {INPUT_CSV}", level="ERROR")
        return None
    
    deletion_records = []
    
    try:
        with open(INPUT_CSV, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                try:
                    # 验证mongo_id格式
                    mongo_id = ObjectId(row['mongo_id'])
                    deletion_records.append({
                        'mongo_id': mongo_id,
                        'original_id': row['original_id'],
                        'y_id': row['y_id'],
                        'title': row['title'][:50] + '...' if len(row['title']) > 50 else row['title'],
                        'created_at': row['created_at']
                    })
                except Exception as e:
                    write_log(f"⚠️  跳过无效记录: {row.get('mongo_id', 'unknown')} - {e}", level="WARN")
                    continue
        
        write_log(f"✅ 成功加载 {len(deletion_records)} 条删除记录")
        return deletion_records
        
    except Exception as e:
        write_log(f"❌ 读取CSV文件失败: {e}", level="ERROR")
        return None

def preview_deletion_plan(deletion_records):
    """预览删除计划"""
    write_log("🔍 删除计划预览")
    write_log("=" * 60)
    
    # 统计信息
    total_records = len(deletion_records)
    unique_ids = len(set(record['original_id'] for record in deletion_records))
    
    write_log(f"📊 总删除记录数: {total_records:,}")
    write_log(f"📊 涉及的唯一ID数: {unique_ids:,}")
    write_log(f"📊 平均每ID重复数: {total_records/unique_ids:.1f}" if unique_ids > 0 else "📊 平均每ID重复数: 0")
    
    # 显示前10条记录
    write_log("\n📋 前10条删除记录:")
    write_log("-" * 60)
    for i, record in enumerate(deletion_records[:10]):
        write_log(f"{i+1:2d}. ID:{record['original_id']} | Y_ID:{record['y_id']} | {record['title']}")
    
    if total_records > 10:
        write_log(f"... 还有 {total_records - 10} 条记录")
    
    write_log("=" * 60)

def get_collection_stats_before_deletion(db):
    """获取删除前的集合统计"""
    write_log("📊 获取删除前的数据库统计...")
    
    collection = db[COLLECTION_NAME]
    
    try:
        total_count = collection.count_documents({})
        write_log(f"📊 删除前总记录数: {total_count:,}")
        return total_count
    except Exception as e:
        write_log(f"❌ 获取统计失败: {e}", level="ERROR")
        return 0

def execute_deletion(db, deletion_records, dry_run=True):
    """执行删除操作"""
    if dry_run:
        write_log("🔍 预览模式：不会实际删除记录")
        return 0, 0
    
    write_log("🗑️ 开始执行删除操作...")
    write_log(f"⚠️  警告：即将删除 {len(deletion_records)} 条记录")
    
    collection = db[COLLECTION_NAME]
    deleted_count = 0
    error_count = 0
    
    # 分批删除
    total_batches = (len(deletion_records) + BATCH_SIZE - 1) // BATCH_SIZE
    
    for i in range(0, len(deletion_records), BATCH_SIZE):
        batch = deletion_records[i:i + BATCH_SIZE]
        batch_ids = [record['mongo_id'] for record in batch]
        batch_num = i // BATCH_SIZE + 1
        
        try:
            # 执行批量删除
            result = collection.delete_many({'_id': {'$in': batch_ids}})
            deleted_count += result.deleted_count
            
            write_log(f"   批次 {batch_num}/{total_batches}: 成功删除 {result.deleted_count} 条记录")
            
            # 检查是否有记录未被删除
            if result.deleted_count != len(batch_ids):
                missing_count = len(batch_ids) - result.deleted_count
                write_log(f"   ⚠️  批次 {batch_num}: {missing_count} 条记录未找到（可能已被删除）", level="WARN")
            
        except Exception as e:
            error_count += len(batch)
            write_log(f"❌ 批次 {batch_num} 删除失败: {e}", level="ERROR")
            continue
    
    write_log(f"✅ 删除操作完成：成功删除 {deleted_count} 条记录，错误 {error_count} 条")
    return deleted_count, error_count

def get_collection_stats_after_deletion(db):
    """获取删除后的集合统计"""
    write_log("📊 获取删除后的数据库统计...")
    
    collection = db[COLLECTION_NAME]
    
    try:
        total_count = collection.count_documents({})
        write_log(f"📊 删除后总记录数: {total_count:,}")
        return total_count
    except Exception as e:
        write_log(f"❌ 获取统计失败: {e}", level="ERROR")
        return 0

def generate_deletion_report(stats):
    """生成删除执行报告"""
    write_log("📄 生成删除执行报告...")
    
    try:
        with open(DELETION_REPORT, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['metric', 'value', 'description']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            writer.writerow({
                'metric': 'records_before_deletion',
                'value': stats['records_before'],
                'description': '删除前总记录数'
            })
            writer.writerow({
                'metric': 'planned_deletions',
                'value': stats['planned_deletions'],
                'description': '计划删除记录数'
            })
            writer.writerow({
                'metric': 'actual_deletions',
                'value': stats['actual_deletions'],
                'description': '实际删除记录数'
            })
            writer.writerow({
                'metric': 'deletion_errors',
                'value': stats['deletion_errors'],
                'description': '删除错误数'
            })
            writer.writerow({
                'metric': 'records_after_deletion',
                'value': stats['records_after'],
                'description': '删除后总记录数'
            })
            writer.writerow({
                'metric': 'expected_records_after',
                'value': stats['expected_records_after'],
                'description': '预期删除后记录数'
            })
            writer.writerow({
                'metric': 'deletion_success_rate',
                'value': f"{stats['success_rate']:.2f}%",
                'description': '删除成功率'
            })
            writer.writerow({
                'metric': 'processing_time',
                'value': f"{stats['processing_time']:.2f}s",
                'description': '处理耗时'
            })
        
        write_log(f"✅ 删除报告已保存到: {DELETION_REPORT}")
        
    except Exception as e:
        write_log(f"❌ 生成报告失败: {e}", level="ERROR")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='基于CSV文件执行重复数据删除')
    parser.add_argument('--execute', action='store_true',
                       help='执行实际删除（默认为预览模式）')
    parser.add_argument('--confirm', action='store_true',
                       help='跳过确认提示（仅在--execute模式下有效）')
    
    args = parser.parse_args()
    
    # 设置运行模式
    dry_run = not args.execute
    skip_confirmation = args.confirm
    
    write_log("🗑️ 基于CSV文件的重复数据删除工具")
    write_log("=" * 60)
    
    if dry_run:
        write_log("🔍 运行模式：预览模式（不会实际删除记录）")
    else:
        write_log("⚠️  运行模式：执行模式（将实际删除记录）")
    
    try:
        # 1. 加载删除列表
        deletion_records = load_deletion_list()
        if not deletion_records:
            write_log("❌ 无法加载删除列表，退出程序", level="ERROR")
            return
        
        # 2. 连接数据库
        client, db = connect_to_mongodb()
        
        # 3. 获取删除前统计
        records_before = get_collection_stats_before_deletion(db)
        
        # 4. 预览删除计划
        preview_deletion_plan(deletion_records)
        
        # 5. 安全确认
        if not dry_run and not skip_confirmation:
            write_log("⚠️  警告：您即将执行实际的数据删除操作！")
            write_log("⚠️  建议在执行前备份数据库！")
            
            confirmation = input("\n确认要继续吗？输入 'YES' 继续，其他任何输入将取消操作: ")
            if confirmation != 'YES':
                write_log("❌ 操作已取消")
                client.close()
                return
        
        # 6. 执行删除
        start_time = time.time()
        deleted_count, error_count = execute_deletion(db, deletion_records, dry_run)
        processing_time = time.time() - start_time
        
        # 7. 获取删除后统计
        records_after = get_collection_stats_after_deletion(db) if not dry_run else records_before
        
        # 8. 生成统计报告
        stats = {
            'records_before': records_before,
            'planned_deletions': len(deletion_records),
            'actual_deletions': deleted_count,
            'deletion_errors': error_count,
            'records_after': records_after,
            'expected_records_after': records_before - len(deletion_records),
            'success_rate': (deleted_count / len(deletion_records) * 100) if len(deletion_records) > 0 else 0,
            'processing_time': processing_time
        }
        
        if not dry_run:
            generate_deletion_report(stats)
        
        # 9. 最终统计
        write_log("\n" + "=" * 60)
        write_log("📈 删除操作完成统计")
        write_log("=" * 60)
        write_log(f"📊 删除前记录数: {stats['records_before']:,}")
        write_log(f"📊 计划删除数: {stats['planned_deletions']:,}")
        write_log(f"📊 实际删除数: {stats['actual_deletions']:,}")
        write_log(f"📊 删除错误数: {stats['deletion_errors']:,}")
        write_log(f"📊 删除后记录数: {stats['records_after']:,}")
        write_log(f"📊 删除成功率: {stats['success_rate']:.2f}%")
        write_log(f"⏱️  处理耗时: {stats['processing_time']:.2f} 秒")
        
        if not dry_run:
            # 验证删除结果
            expected_after = stats['expected_records_after']
            actual_after = stats['records_after']
            if actual_after == expected_after:
                write_log("✅ 删除结果与预期一致")
            else:
                diff = actual_after - expected_after
                write_log(f"⚠️  删除结果与预期不符，差异: {diff:+d} 条记录", level="WARN")
        
        write_log("=" * 60)
        
        # 关闭数据库连接
        client.close()
        write_log("🔌 数据库连接已关闭")
        
        if dry_run:
            print(f"\n✅ 预览完成！详细日志请查看: {LOG_FILE}")
            print("💡 要执行实际删除，请使用: python execute_csv_based_deletion.py --execute")
        else:
            print(f"\n✅ 删除完成！详细日志请查看: {LOG_FILE}")
            print(f"📄 删除报告: {DELETION_REPORT}")
        
    except KeyboardInterrupt:
        write_log("⚠️ 用户中断了程序执行", level="WARN")
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        write_log(f"❌ 程序执行失败: {e}", level="ERROR")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
