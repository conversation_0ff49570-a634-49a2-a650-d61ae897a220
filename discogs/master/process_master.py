#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import glob
from pymongo import MongoClient
from datetime import datetime
import re
import sys
import os
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source, DeleteStatus
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status, Source, DeleteStatus

def find_xml_file(module_type):
    """
    从 deploy/windows/data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    data_dir = 'deploy/windows/data'
    pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
    found_files = glob.glob(pattern)

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

# 配置参数
XML_FILE = find_xml_file('masters')

if not XML_FILE:
    print("❌ 错误: 无法找到 masters 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_masters.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，设置为0表示处理全部数据

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')


# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    client = MongoClient(MONGO_URI)
    return client, client[DB_NAME]

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def extract_notes(content):
    """提取notes字段"""
    notes = extract_field(content, 'notes')
    return notes.strip() if notes else None

def get_master_table_by_id(db, master_id):
    """从master表中获取images字段"""
    try:
        # 将master_id转换为string类型进行查询
        master_id_str = str(master_id)
        master_doc = db.master.find_one({'id': master_id_str})
        if master_doc:
            return master_doc
        return {}
    except Exception as e:
        write_output(f"获取master数据失败 (master_id: {master_id}): {e}", False)
        return {}

def process_master_content(buffer, sequential_id, db):
    """处理单个master标签的内容"""
    # 提取ID
    master_id = extract_field(buffer, 'id')
    if not master_id:
        return None

    # 从数据库获取现有master数据（用于获取images字段）
    existing_master_data = get_master_table_by_id(db, master_id)

    # 创建master文档
    master_doc = {
        'id': master_id,
        'y_id': f"YM{sequential_id}",
        'key_release': extract_field(buffer, 'main_release'),  # main_release保存为key_release
        'notes': existing_master_data.get('notes', ''),
        'delete_status': DeleteStatus.NOT_DELETED.value,  # 逻辑删除状态，默认未删除
        'deleted_at': None,  # 为软删除功能准备
        'created_at': datetime.now(),
        'updated_at': datetime.now(),
        'source': Source.DISCOGS.value,
        'permissions': Permissions.ALL_VISIBLE.value,
    }

    return master_doc

def process_masters():
    """处理XML文件中的master记录"""
    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()

    # 确保masters_new集合存在
    if 'master_new' not in db.list_collection_names():
        db.create_collection('master_new')

    # 获取集合
    masters_collection = db['masters']
    masters_new_collection = db['master_new']

    # 清空masters_new集合
    masters_new_collection.delete_many({})

    processed_count = 0
    notes_count = 0
    total_records_found = 0

    try:
        # 打开gzip压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_master = False

            for line in f:
                if '<master' in line and 'id=' in line:
                    buffer = line
                    in_master = True
                    total_records_found += 1
                elif '</master>' in line and in_master:
                    buffer += line
                    in_master = False

                    # 处理master内容，使用连续的序号作为y_id
                    sequential_id = processed_count + 1
                    master_doc = process_master_content(buffer, sequential_id, db)
                    if master_doc:
                        # 查询数据库中是否存在该master
                        existing_master = masters_collection.find_one({'id': master_doc['id']})

                        if existing_master and '_id' in existing_master:
                            # 保留原始_id
                            master_doc['_id'] = existing_master['_id']

                        # 更新或插入到masters_new集合
                        masters_new_collection.update_one(
                            {'id': master_doc['id']},
                            {'$set': master_doc},
                            upsert=True
                        )

                        processed_count += 1
                        if master_doc['notes']:
                            notes_count += 1
                        # 显示进度
                        if processed_count % 10 == 0:
                            # title字段被注释，只显示基本信息
                            log_message = (f"处理记录 {processed_count}: y_id={master_doc['y_id']}, "
                                      f"id={master_doc['id']}")
                            # 只写入文件，不打印到控制台
                            write_output(log_message, False)
                            # 打印到控制台
                            print(f"已处理 {processed_count} 条记录...")

                        # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                        if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_master:
                    buffer += line
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"共处理了 {processed_count} 条记录",

            f"XML文件中共发现 {total_records_found} 条记录"
        ]

        if MAX_RECORDS < total_records_found:
            stats.append(f"由于设置了最大处理记录数限制 (MAX_RECORDS={MAX_RECORDS})，只处理了部分记录")

        stats.extend([
            f"处理时长: {processing_time:.2f} 秒",
            f"平均每条记录处理时间: {processing_time/processed_count:.4f} 秒" if processed_count > 0 else "平均每条记录处理时间: 0.0000 秒",
            "="*50
        ])

        # 只打印到控制台，不写入文件
        for stat in stats:
            print(stat)

        # 关闭数据库连接
        client.close()

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")

# 确保只在直接运行脚本时执行process_masters函数
if __name__ == "__main__":
    process_masters()
