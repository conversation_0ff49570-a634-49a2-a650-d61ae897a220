#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试版本的barcode提取脚本
用于诊断为什么没有找到barcode数据
"""

import os
import json
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def extract_barcode_from_searchrule(searchrule_data):
    """从searchRuleRelease数据中提取barcode值（调试版本）"""
    try:
        print(f"🔍 调试: 输入数据类型: {type(searchrule_data)}")
        
        if not searchrule_data:
            print("🔍 调试: 数据为空")
            return None
        
        # 如果是字符串，尝试解析为JSON
        if isinstance(searchrule_data, str):
            print(f"🔍 调试: 字符串数据，长度: {len(searchrule_data)}")
            try:
                searchrule_data = json.loads(searchrule_data)
                print("🔍 调试: JSON解析成功")
            except json.JSONDecodeError as e:
                print(f"🔍 调试: JSON解析失败: {e}")
                return None
        
        # 检查是否为字典
        if not isinstance(searchrule_data, dict):
            print(f"🔍 调试: 不是字典类型: {type(searchrule_data)}")
            return None
        
        print(f"🔍 调试: 字典键: {list(searchrule_data.keys())}")
        
        # 提取baseinfo.barcode
        baseinfo = searchrule_data.get('baseinfo', {})
        print(f"🔍 调试: baseinfo类型: {type(baseinfo)}")
        
        if isinstance(baseinfo, dict):
            print(f"🔍 调试: baseinfo键: {list(baseinfo.keys())}")
            barcode = baseinfo.get('barcode', '')
            print(f"🔍 调试: 原始barcode值: '{barcode}' (类型: {type(barcode)})")
            
            # 返回非空的barcode值（包括以点开头的和全零的有效barcode）
            if barcode and barcode.strip():
                # 过滤掉明显无效的barcode（只有空格或特殊字符）
                cleaned_barcode = barcode.strip()
                print(f"🔍 调试: 清理后barcode: '{cleaned_barcode}' (长度: {len(cleaned_barcode)})")
                if len(cleaned_barcode) > 0:
                    print(f"✅ 调试: 找到有效barcode: '{cleaned_barcode}'")
                    return cleaned_barcode
            print("🔍 调试: barcode为空或无效")
            return None
        else:
            print(f"🔍 调试: baseinfo不是字典: {baseinfo}")
        
        return None
        
    except Exception as e:
        print(f"⚠️ 调试: 提取barcode时出错: {e}")
        return None

def debug_barcode_extraction():
    """调试barcode提取"""
    print("🚀 开始调试barcode提取...")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    print("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取release集合
        release_collection = db['release']
        
        print("🔍 查找前10条包含searchRuleRelease字段的记录...")
        
        # 查询包含searchRuleRelease字段的记录
        query = {"searchRuleRelease": {"$exists": True}}
        projection = {"id": 1, "searchRuleRelease": 1}
        
        records = list(release_collection.find(query, projection).limit(10))
        
        print(f"✅ 找到 {len(records)} 条记录")
        
        barcode_found_count = 0
        
        for i, record in enumerate(records, 1):
            print(f"\n📋 处理记录 #{i} (ID: {record.get('id')})")
            print("-" * 50)
            
            searchrule_data = record.get('searchRuleRelease')
            barcode = extract_barcode_from_searchrule(searchrule_data)
            
            if barcode:
                barcode_found_count += 1
                print(f"🎯 记录 #{i} 找到barcode: '{barcode}'")
            else:
                print(f"❌ 记录 #{i} 未找到有效barcode")
        
        print(f"\n📊 总结: 在 {len(records)} 条记录中找到 {barcode_found_count} 个有效barcode")
        
        # 现在测试我们之前找到的特定记录
        print("\n🔍 测试已知包含barcode的特定记录...")
        
        known_ids = [27789522, 27482904, 26808293]  # 从之前的查找结果中获取
        
        for record_id in known_ids:
            print(f"\n📋 测试记录 ID: {record_id}")
            record = release_collection.find_one({"id": record_id}, {"id": 1, "searchRuleRelease": 1})
            
            if record:
                searchrule_data = record.get('searchRuleRelease')
                barcode = extract_barcode_from_searchrule(searchrule_data)
                
                if barcode:
                    print(f"✅ 记录 {record_id} 找到barcode: '{barcode}'")
                else:
                    print(f"❌ 记录 {record_id} 未找到barcode")
                    # 显示原始数据
                    print(f"原始searchRuleRelease: {searchrule_data}")
            else:
                print(f"❌ 未找到记录 {record_id}")
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        raise
    finally:
        client.close()
        print("\n🔒 已关闭MongoDB连接")

if __name__ == "__main__":
    try:
        debug_barcode_extraction()
        print("\n✅ 调试完成！")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        exit(1)
