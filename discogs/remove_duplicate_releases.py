#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs Release 重复数据清理脚本

功能：
1. 识别release_new表中的重复记录（约1600条）
2. 删除重复记录中最新插入的记录，保留最早插入的记录
3. 提供执行前的数据统计和执行后的确认信息
4. 支持预览模式和实际删除模式
5. 生成详细的删除日志和统计报告

安全策略：
- 对于每个重复组，保留 created_at 最早的记录
- 如果 created_at 相同，则保留 _id 最小的记录
- 默认预览模式，需要明确确认才执行删除
- 提供详细的删除前统计和删除后确认

作者：AI Assistant
创建时间：2025-07-18
"""

import os
import sys
import time
import csv
import argparse
import traceback
from collections import defaultdict
from datetime import datetime, timezone
from pymongo import MongoClient

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# 输出文件配置
OUTPUT_DIR = 'duplicate_cleanup_results'
LOG_FILE = os.path.join(OUTPUT_DIR, 'duplicate_cleanup.log')
DELETED_RECORDS_CSV = os.path.join(OUTPUT_DIR, 'deleted_records.csv')
SUMMARY_CSV = os.path.join(OUTPUT_DIR, 'cleanup_summary.csv')

# 删除配置
BATCH_SIZE = 100  # 批删除大小，避免内存问题

# 创建输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 清理旧的日志文件
if os.path.exists(LOG_FILE):
    os.remove(LOG_FILE)

def write_log(message, print_to_console=True, level="INFO"):
    """将消息写入日志文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] [{level}] {message}"
    
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_log(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_log(f"❌ MongoDB连接失败: {e}", level="ERROR")
        raise

def find_duplicate_records(db):
    """查找重复记录，基于id字段进行重复判断"""
    write_log("🔍 正在查找重复记录...")
    
    collection = db[COLLECTION_NAME]
    
    # 首先获取总记录数
    total_count = collection.count_documents({})
    write_log(f"📊 数据库中总记录数: {total_count:,}")
    
    # 使用聚合管道查找重复的id字段
    pipeline = [
        {
            '$group': {
                '_id': '$id',  # 按id字段分组
                'count': {'$sum': 1},
                'records': {'$push': {
                    'mongo_id': '$_id',
                    'y_id': '$y_id',
                    'title': '$title',
                    'created_at': '$created_at'
                }}
            }
        },
        {
            '$match': {
                'count': {'$gt': 1}  # 只保留重复的记录（count > 1）
            }
        },
        {
            '$sort': {'_id': 1}  # 按id排序
        }
    ]
    
    write_log("🔄 执行重复记录查询...")
    duplicate_groups = list(collection.aggregate(pipeline, allowDiskUse=True))
    
    # 统计重复记录信息
    total_duplicate_records = sum(group['count'] for group in duplicate_groups)
    write_log(f"📊 发现重复组数量: {len(duplicate_groups)}")
    write_log(f"📊 重复记录总数: {total_duplicate_records}")
    write_log(f"📊 预计需要删除的记录数: {total_duplicate_records - len(duplicate_groups)}")
    
    return duplicate_groups

def identify_records_to_delete(duplicate_groups):
    """识别需要删除的记录，保留最早插入的记录"""
    write_log("🎯 正在识别需要删除的记录...")
    
    records_to_delete = []
    records_to_keep = []
    
    for group in duplicate_groups:
        original_id = group['_id']
        records = group['records']
        
        # 按创建时间排序，如果创建时间相同则按MongoDB _id排序
        # 这确保了我们总是保留最早插入的记录
        sorted_records = sorted(records, key=lambda x: (
            x.get('created_at', datetime.min.replace(tzinfo=timezone.utc)),
            str(x.get('mongo_id', ''))
        ))
        
        # 保留第一个（最早的）记录
        keep_record = sorted_records[0]
        records_to_keep.append({
            'original_id': original_id,
            'mongo_id': keep_record['mongo_id'],
            'y_id': keep_record.get('y_id', ''),
            'title': keep_record.get('title', ''),
            'created_at': keep_record.get('created_at', '')
        })
        
        # 删除其余记录
        for record in sorted_records[1:]:
            records_to_delete.append({
                'original_id': original_id,
                'mongo_id': record['mongo_id'],
                'y_id': record.get('y_id', ''),
                'title': record.get('title', ''),
                'created_at': record.get('created_at', ''),
                'keep_record_y_id': keep_record.get('y_id', ''),
                'keep_record_created_at': keep_record.get('created_at', '')
            })
    
    write_log(f"📊 将保留的记录数: {len(records_to_keep)}")
    write_log(f"📊 将删除的记录数: {len(records_to_delete)}")
    
    return records_to_delete, records_to_keep

def preview_deletion(records_to_delete):
    """预览将要删除的记录"""
    write_log("👀 预览删除计划...")
    
    if not records_to_delete:
        write_log("✅ 没有发现需要删除的重复记录")
        return
    
    write_log("📋 删除预览（前10条记录）:")
    for i, record in enumerate(records_to_delete[:10]):
        title_short = record['title'][:30] if record['title'] else "无标题"
        write_log(f"   {i+1}. ID={record['original_id']}, "
                 f"y_id={record['y_id']}, "
                 f"title={title_short}...")
    
    if len(records_to_delete) > 10:
        write_log(f"   ... 还有 {len(records_to_delete) - 10} 条记录将被删除")
    
    # 按原始ID统计重复情况
    id_stats = defaultdict(int)
    for record in records_to_delete:
        id_stats[record['original_id']] += 1
    
    write_log(f"📊 涉及的重复ID数量: {len(id_stats)}")
    if len(id_stats) > 0:
        avg_duplicates = len(records_to_delete) / len(id_stats)
        write_log(f"📊 平均每个ID的重复数: {avg_duplicates:.1f}")

def export_deletion_plan(records_to_delete):
    """导出删除计划到CSV文件"""
    write_log("📄 导出删除计划到CSV文件...")
    
    with open(DELETED_RECORDS_CSV, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'original_id', 'mongo_id', 'y_id', 'title', 'created_at',
            'keep_record_y_id', 'keep_record_created_at', 'deletion_status'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for record in records_to_delete:
            record_copy = record.copy()
            record_copy['deletion_status'] = 'PLANNED'
            writer.writerow(record_copy)
    
    write_log(f"✅ 删除计划已导出到: {DELETED_RECORDS_CSV}")

def execute_deletion(db, records_to_delete, dry_run=True):
    """执行删除操作"""
    if dry_run:
        write_log("🔍 预览模式：不会实际删除记录")
        return 0, 0
    
    write_log("🗑️ 开始执行删除操作...")
    write_log(f"⚠️  警告：即将删除 {len(records_to_delete)} 条记录")
    
    collection = db[COLLECTION_NAME]
    deleted_count = 0
    error_count = 0
    
    # 分批删除，避免内存问题
    for i in range(0, len(records_to_delete), BATCH_SIZE):
        batch = records_to_delete[i:i + BATCH_SIZE]
        batch_ids = [record['mongo_id'] for record in batch]
        
        try:
            # 执行批量删除
            result = collection.delete_many({'_id': {'$in': batch_ids}})
            deleted_count += result.deleted_count
            
            batch_num = i // BATCH_SIZE + 1
            write_log(f"   批次 {batch_num}: 成功删除 {result.deleted_count} 条记录")
            
        except Exception as e:
            error_count += len(batch)
            batch_num = i // BATCH_SIZE + 1
            write_log(f"❌ 批次 {batch_num} 删除失败: {e}", level="ERROR")
    
    write_log(f"✅ 删除操作完成：成功删除 {deleted_count} 条记录")
    if error_count > 0:
        write_log(f"⚠️  删除错误：{error_count} 条记录删除失败")
    
    return deleted_count, error_count

def generate_summary_report(stats):
    """生成汇总报告"""
    write_log("📊 生成清理汇总报告...")
    
    summary_data = [
        ['统计项目', '数量', '说明'],
        ['处理前总记录数', stats['total_records_before'], '清理前数据库中的总记录数'],
        ['发现重复组数', stats['duplicate_groups'], '检测到的重复ID组数'],
        ['重复记录总数', stats['total_duplicates'], '所有重复记录的总数'],
        ['计划删除记录数', stats['planned_deletions'], '计划删除的记录数'],
        ['实际删除记录数', stats['actual_deletions'], '成功删除的记录数'],
        ['删除失败记录数', stats['deletion_errors'], '删除失败的记录数'],
        ['处理后预期记录数', stats['expected_records_after'], '清理后预期的记录数'],
        ['处理耗时（秒）', round(stats['processing_time'], 2), '总处理时间']
    ]
    
    # 写入CSV文件
    with open(SUMMARY_CSV, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(summary_data)
    
    # 打印汇总信息
    write_log("=" * 60)
    write_log("📊 重复数据清理汇总报告")
    write_log("=" * 60)
    
    for row in summary_data[1:]:  # 跳过表头
        write_log(f"{row[0]}: {row[1]:,} ({row[2]})")
    
    write_log("=" * 60)
    write_log(f"✅ 汇总报告已保存到: {SUMMARY_CSV}")
    
    return summary_data

def main():
    """主函数"""

    parser = argparse.ArgumentParser(description='Discogs Release 重复数据清理脚本')
    parser.add_argument('--execute', action='store_true',
                       help='执行实际删除操作（默认为预览模式）')
    parser.add_argument('--confirm', action='store_true',
                       help='跳过用户确认提示（仅在--execute模式下有效）')

    args = parser.parse_args()

    # 设置运行模式
    dry_run = not args.execute
    skip_confirmation = args.confirm

    write_log("🗑️ Discogs Release 重复数据清理工具")
    write_log("=" * 60)

    if dry_run:
        write_log("🔍 运行模式：预览模式（不会实际删除记录）")
        write_log("💡 提示：使用 --execute 参数执行实际删除")
    else:
        write_log("⚠️  运行模式：执行模式（将实际删除记录）")

    # 安全确认
    if not dry_run and not skip_confirmation:
        write_log("⚠️  警告：您即将执行实际的数据删除操作！")
        write_log("⚠️  强烈建议在执行前备份数据库！")
        write_log("⚠️  删除操作不可逆，请谨慎操作！")

        print("\n" + "="*50)
        print("🚨 重要安全提示 🚨")
        print("="*50)
        print("1. 此操作将永久删除重复的数据记录")
        print("2. 建议在执行前备份数据库")
        print("3. 删除操作不可逆转")
        print("4. 请确认您了解操作的后果")
        print("="*50)

        confirmation = input("\n确认要继续执行删除操作吗？输入 'YES' 继续，其他任何输入将取消: ")
        if confirmation != 'YES':
            write_log("❌ 操作已取消")
            return

        write_log("✅ 用户已确认，开始执行删除操作")

    # 初始化统计数据
    stats = {
        'total_records_before': 0,
        'duplicate_groups': 0,
        'total_duplicates': 0,
        'planned_deletions': 0,
        'actual_deletions': 0,
        'deletion_errors': 0,
        'expected_records_after': 0,
        'processing_time': 0
    }

    start_time = time.time()

    try:
        # 连接数据库
        client, db = connect_to_mongodb()

        # 1. 查找重复记录
        duplicate_groups = find_duplicate_records(db)
        stats['duplicate_groups'] = len(duplicate_groups)
        stats['total_duplicates'] = sum(group['count'] for group in duplicate_groups)

        # 获取处理前的总记录数
        stats['total_records_before'] = db[COLLECTION_NAME].count_documents({})

        if not duplicate_groups:
            write_log("✅ 没有发现重复记录，无需清理")
            client.close()
            return

        # 2. 识别需要删除的记录
        records_to_delete, records_to_keep = identify_records_to_delete(duplicate_groups)
        stats['planned_deletions'] = len(records_to_delete)

        # 3. 预览删除计划
        preview_deletion(records_to_delete)

        # 4. 导出删除计划
        export_deletion_plan(records_to_delete)

        # 5. 执行删除操作
        deleted_count, error_count = execute_deletion(db, records_to_delete, dry_run)
        stats['actual_deletions'] = deleted_count
        stats['deletion_errors'] = error_count

        # 6. 计算预期的处理后记录数
        stats['expected_records_after'] = stats['total_records_before'] - stats['actual_deletions']

        # 7. 验证删除结果（仅在实际删除模式下）
        if not dry_run:
            actual_records_after = db[COLLECTION_NAME].count_documents({})
            write_log(f"🔍 删除后实际记录数: {actual_records_after:,}")

            if actual_records_after == stats['expected_records_after']:
                write_log("✅ 删除结果验证通过")
            else:
                expected = stats['expected_records_after']
                write_log(f"⚠️  删除结果异常：预期 {expected:,}，实际 {actual_records_after:,}")

        # 8. 生成汇总报告
        stats['processing_time'] = time.time() - start_time
        generate_summary_report(stats)

        # 关闭数据库连接
        client.close()
        write_log("🔌 数据库连接已关闭")

        # 最终提示
        if dry_run:
            write_log("✅ 预览完成！查看日志文件了解详细信息")
            write_log(f"📁 日志文件: {LOG_FILE}")
            write_log(f"📁 删除计划: {DELETED_RECORDS_CSV}")
        else:
            write_log("✅ 重复数据清理完成！")
            write_log(f"📊 成功删除 {deleted_count} 条重复记录")
            if error_count > 0:
                write_log(f"⚠️  {error_count} 条记录删除失败，请检查日志")

    except KeyboardInterrupt:
        write_log("⚠️ 用户中断了程序执行", level="WARN")
    except Exception as e:
        write_log(f"❌ 程序执行失败: {e}", level="ERROR")
        write_log(f"错误详情: {traceback.format_exc()}", level="ERROR")
        sys.exit(1)

def print_usage():
    """打印使用说明"""
    usage_text = """
🗑️ Discogs Release 重复数据清理工具

功能说明：
1. 识别release_new表中的重复记录（基于id字段）
2. 删除重复记录中最新插入的记录，保留最早插入的记录
3. 提供执行前的数据统计和执行后的确认信息
4. 支持预览模式和实际删除模式
5. 生成详细的删除日志和统计报告

使用方法：
    # 预览模式（默认，不实际删除）
    python remove_duplicate_releases.py

    # 执行实际删除（需要用户确认）
    python remove_duplicate_releases.py --execute

    # 执行实际删除（跳过确认提示）
    python remove_duplicate_releases.py --execute --confirm

安全策略：
    - 对于每个重复组，保留 created_at 最早的记录
    - 如果 created_at 相同，则保留 _id 最小的记录
    - 默认预览模式，需要明确指定 --execute 才会实际删除
    - 执行删除前会要求用户确认（除非使用 --confirm 跳过）

输出文件：
    - duplicate_cleanup_results/duplicate_cleanup.log        # 详细日志
    - duplicate_cleanup_results/deleted_records.csv          # 删除记录详情
    - duplicate_cleanup_results/cleanup_summary.csv          # 清理汇总报告

重复判断标准：
    - 基于 id 字段进行重复判断
    - 相同 id 的记录被认为是重复记录

删除策略：
    - 保留最早插入的记录（created_at 最小）
    - 删除较新插入的重复记录
    - 如果 created_at 相同，保留 _id 最小的记录

注意事项：
    - 强烈建议在执行删除前备份数据库
    - 先使用预览模式检查删除计划
    - 删除操作不可逆，请谨慎操作
    - 脚本会自动创建输出目录和日志文件

回滚建议：
    - 在执行删除前，建议使用 mongodump 备份 release_new 集合
    - 备份命令示例：
      mongodump --uri="your_mongo_uri" --db=music_test --collection=release_new
    - 如需回滚，使用 mongorestore 恢复备份数据
    """
    print(usage_text)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
    else:
        main()
