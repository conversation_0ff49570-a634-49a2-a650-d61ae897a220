#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Release数据同步脚本

功能：
1. 连接到MongoDB数据库，分析release_new表与discogs_20250701_releases.xml.gz文件之间的数据差异
2. 统计release_new表中的当前记录数和XML文件中的总记录数
3. 识别XML文件中存在但数据库中缺失的release记录
4. 按照现有的process_releases.py模式处理缺失记录
5. 将缺失的记录批量插入到release_new表中
6. 生成详细的处理报告和统计信息

特点：
- 使用内存高效的流式处理方法
- 显示详细的进度信息和统计数据
- 确保数据一致性和完整性
- 使用批量插入提高效率
- 避免重复插入已存在的记录

作者：AI Assistant
创建时间：2025-07-28
"""

import gzip
import time
import re
import os
import glob
import sys
from datetime import datetime, timezone
from pymongo import MongoClient, InsertOne
from collections import defaultdict

# 导入枚举值
try:
    from release.enums import Permissions, Status, Source
except ImportError:
    # 如果相对导入失败，尝试直接导入
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), 'release'))
    from enums import Permissions, Status, Source

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '5000'))  # 批处理大小
SCAN_BATCH_SIZE = int(os.getenv('SCAN_BATCH_SIZE', '500000'))  # 扫描批次大小（提高效率）

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'data_consistency_analysis.txt')
REPORT_FILE = os.getenv('REPORT_FILE', 'data_consistency_report.txt')

# 确保输出文件不存在
for file_path in [OUTPUT_FILE, REPORT_FILE]:
    if os.path.exists(file_path):
        os.remove(file_path)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"
    
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def write_report(message):
    """写入报告文件"""
    with open(REPORT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

def find_xml_file(module_type):
    """
    查找指定模块的XML文件，优先搜索当前目录
    
    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')
    
    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 首先在当前目录搜索（支持.xml和.xml.gz格式）
    current_dir_patterns = [
        f'*_{module_type}.xml.gz',
        f'*_{module_type}.xml',
        f'discogs_*_{module_type}.xml.gz',
        f'discogs_*_{module_type}.xml'
    ]
    
    found_files = []
    for pattern in current_dir_patterns:
        found_files.extend(glob.glob(pattern))
    
    if found_files:
        # 如果找到多个文件，选择最新的（按文件名排序）
        if len(found_files) > 1:
            found_files.sort()
            selected_file = found_files[-1]
            write_output(f"🔍 在当前目录找到多个文件，选择最新的: {selected_file}")
        else:
            selected_file = found_files[0]
            write_output(f"✅ 在当前目录检测到文件: {selected_file}")
        return selected_file
    
    write_output(f"❌ 未找到 {module_type} 模块的XML文件")
    return None

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        write_output(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def get_existing_ids_from_db(db):
    """获取数据库中现有的release ID集合"""
    write_output("📊 正在获取数据库中现有的release ID...")
    start_time = time.time()
    
    try:
        # 获取release_new集合
        collection = db['release_new']
        
        # 统计总记录数
        total_count = collection.count_documents({})
        write_output(f"📈 release_new表总记录数: {total_count:,}")
        
        # 获取所有ID（使用投影只获取id字段）
        existing_ids = set()
        cursor = collection.find({}, {'id': 1, '_id': 0})
        
        processed_count = 0
        for doc in cursor:
            if 'id' in doc:
                # 确保ID为整数类型进行比较
                try:
                    id_value = int(doc['id']) if isinstance(doc['id'], str) else doc['id']
                    existing_ids.add(id_value)
                except (ValueError, TypeError):
                    write_output(f"⚠️ 跳过无效ID: {doc['id']}", False)
                    continue
            
            processed_count += 1
            if processed_count % 100000 == 0:
                write_output(f"🔄 已处理 {processed_count:,} 条记录...")
        
        elapsed_time = time.time() - start_time
        write_output(f"✅ 获取完成，有效ID数量: {len(existing_ids):,}，耗时: {elapsed_time:.2f} 秒")
        
        return existing_ids
        
    except Exception as e:
        write_output(f"❌ 获取数据库ID失败: {e}")
        raise

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []

    # 提取主要艺术家 (artists)
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)

    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extra_artists)"""
    extra_artists = []

    # 提取额外艺术家 (extraartists)
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)

    return extra_artists

def extract_labels(content):
    """提取标签字段 (labels)"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if labels_match:
        labels_content = labels_match.group(1)
        label_pattern = r'<label[^>]*>(.*?)</label>'
        label_matches = re.findall(label_pattern, labels_content, re.DOTALL)

        for label_content in label_matches:
            name = extract_field(label_content, 'name') or extract_field(label_content, 'n')
            catno = extract_field(label_content, 'catno')
            label_id = extract_attribute(label_content, 'label', 'id')

            if name:
                label_doc = {'name': name}
                if catno:
                    label_doc['catno'] = catno
                if label_id:
                    label_doc['id'] = label_id
                labels.append(label_doc)

    return labels

def extract_companies(content):
    """提取公司字段 (companies)"""
    companies = []
    companies_match = re.search(r'<companies>(.*?)</companies>', content, re.DOTALL)
    if companies_match:
        companies_content = companies_match.group(1)
        company_pattern = r'<company[^>]*>(.*?)</company>'
        company_matches = re.findall(company_pattern, companies_content, re.DOTALL)

        for company_content in company_matches:
            name = extract_field(company_content, 'name') or extract_field(company_content, 'n')
            if name:
                companies.append(name)

    return companies

def extract_formats(content):
    """提取格式字段 (formats)"""
    formats = []
    formats_match = re.search(r'<formats>(.*?)</formats>', content, re.DOTALL)
    if formats_match:
        formats_content = formats_match.group(1)
        format_pattern = r'<format[^>]*>(.*?)</format>'
        format_matches = re.findall(format_pattern, formats_content, re.DOTALL)

        for format_content in format_matches:
            name = extract_attribute(format_content, 'format', 'name')
            qty = extract_attribute(format_content, 'format', 'qty')
            text = extract_attribute(format_content, 'format', 'text')

            descriptions = []
            descriptions_match = re.search(r'<descriptions>(.*?)</descriptions>', format_content, re.DOTALL)
            if descriptions_match:
                desc_pattern = r'<description>(.*?)</description>'
                descriptions = re.findall(desc_pattern, descriptions_match.group(1))

            if name:
                format_doc = {'name': name}
                if qty:
                    format_doc['qty'] = qty
                if text:
                    format_doc['text'] = text
                if descriptions:
                    format_doc['descriptions'] = descriptions
                formats.append(format_doc)

    return formats

def extract_list_field(content, container_tag, item_tag):
    """提取列表字段（如genres、styles）"""
    items = []
    container_match = re.search(f'<{container_tag}>(.*?)</{container_tag}>', content, re.DOTALL)
    if container_match:
        container_content = container_match.group(1)
        item_pattern = f'<{item_tag}>(.*?)</{item_tag}>'
        items = re.findall(item_pattern, container_content)
        items = [item.strip() for item in items if item.strip()]
    return items

def extract_tracklist(content):
    """提取曲目列表字段 (tracklist)"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if tracklist_match:
        tracklist_content = tracklist_match.group(1)
        track_pattern = r'<track>(.*?)</track>'
        track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)

        for track_content in track_matches:
            position = extract_field(track_content, 'position')
            title = extract_field(track_content, 'title')
            duration = extract_field(track_content, 'duration')

            if position or title:
                track_doc = {}
                if position:
                    track_doc['position'] = position
                if title:
                    track_doc['title'] = title
                if duration:
                    track_doc['duration'] = duration
                tracklist.append(track_doc)

    return tracklist

def get_release_table_by_id(db, release_id):
    """从release表中获取notes、images、years、identifiers字段"""
    try:
        # 将release_id转换为string类型进行查询
        release_id_str = str(release_id)
        release_doc = db.release.find_one({'id': release_id_str})
        if release_doc:
            return {
                'notes': release_doc.get('notes', []),
                'images': release_doc.get('images', []),
                'years': release_doc.get('year', None),  # 注意：数据库中是year，返回时用years
                'identifiers': release_doc.get('identifiers', [])
            }
        return {
            'notes': [],
            'images': [],
            'years': None,
            'identifiers': []
        }
    except Exception as e:
        write_output(f"获取数据库字段失败 (release_id: {release_id}): {e}", False)
        return {
            'notes': [],
            'images': [],
            'years': None,
            'identifiers': []
        }

def scan_xml_for_missing_ids(xml_file, existing_ids):
    """扫描XML文件，找出数据库中缺失的release ID"""
    write_output(f"🔍 开始扫描XML文件: {xml_file}")
    start_time = time.time()

    missing_ids = set()
    total_xml_count = 0

    try:
        # 打开压缩文件
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False

            for line_num, line in enumerate(f, 1):
                if '<release id=' in line:
                    in_release = True
                    buffer = line
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 提取release ID
                    release_id = extract_release_id(buffer)
                    if release_id:
                        total_xml_count += 1

                        # 检查是否在数据库中缺失
                        if release_id not in existing_ids:
                            missing_ids.add(release_id)

                        # 显示进度
                        if total_xml_count % SCAN_BATCH_SIZE == 0:
                            elapsed = time.time() - start_time
                            speed = total_xml_count / elapsed if elapsed > 0 else 0
                            write_output(f"🔄 已扫描 {total_xml_count:,} 条记录，"
                                       f"发现缺失 {len(missing_ids):,} 条，"
                                       f"速度: {speed:.0f} 记录/秒")

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

        elapsed_time = time.time() - start_time
        write_output(f"✅ XML扫描完成！")
        write_output(f"📊 XML文件总记录数: {total_xml_count:,}")
        write_output(f"📊 数据库现有记录数: {len(existing_ids):,}")
        write_output(f"📊 缺失记录数: {len(missing_ids):,}")
        write_output(f"⏱️ 扫描耗时: {elapsed_time:.2f} 秒")

        return missing_ids, total_xml_count

    except Exception as e:
        write_output(f"❌ XML扫描失败: {e}")
        raise

def process_release_content(buffer, y_id, db):
    """处理单个release标签的内容，生成数据库文档"""
    # 提取release ID
    release_id = extract_release_id(buffer)
    if not release_id:
        return None

    # 提取 discogs_status
    discogs_status = extract_attribute(buffer, 'release', 'status') or 'unknown'

    # 从数据库获取额外字段
    db_fields = get_release_table_by_id(db, release_id)

    # 创建release文档（用于数据库插入）
    release_doc = {
        'y_id': y_id,
        'id': str(release_id),  # 确保ID为字符串类型
        'title': extract_field(buffer, 'title'),
        'artists': extract_artists(buffer),
        'extra_artists': extract_extra_artists(buffer),
        'labels': extract_labels(buffer),
        'companies': extract_companies(buffer),
        'country': extract_field(buffer, 'country'),
        'formats': extract_formats(buffer),
        'genres': extract_list_field(buffer, 'genres', 'genre'),
        'styles': extract_list_field(buffer, 'styles', 'style'),
        'identifiers': db_fields['identifiers'],  # 从数据库获取
        'tracklist': extract_tracklist(buffer),
        'master_id': extract_field(buffer, 'master_id'),
        'discogs_status': discogs_status,

        # 从数据库获取的字段
        'notes': db_fields['notes'],
        'images': db_fields['images'],
        'years': db_fields['years'],

        # 权限和状态字段
        'images_permissions': Permissions.ALL_VISIBLE.value,
        'permissions': Permissions.ALL_VISIBLE.value,
        'source': Source.DISCOGS.value,

        'created_at': datetime.now(timezone.utc),
        'updated_at': datetime.now(timezone.utc)
    }

    # 转换master_id为整数
    if release_doc['master_id']:
        try:
            release_doc['master_id'] = int(release_doc['master_id'])
        except ValueError:
            release_doc['master_id'] = None

    return release_doc

def batch_insert_releases(db, release_docs):
    """批量插入release记录到数据库"""
    if not release_docs:
        return 0, 0

    try:
        collection = db['release_new']

        # 构建批量插入操作
        operations = [InsertOne(doc) for doc in release_docs]

        # 执行批量插入
        result = collection.bulk_write(operations, ordered=False)

        success_count = result.inserted_count
        error_count = len(release_docs) - success_count

        return success_count, error_count

    except Exception as e:
        write_output(f"❌ 批量插入失败: {e}")
        return 0, len(release_docs)

def process_missing_releases(xml_file, missing_ids, db):
    """处理XML文件中缺失的release记录"""
    write_output(f"🔄 开始处理缺失的 {len(missing_ids):,} 条记录...")
    start_time = time.time()

    processed_count = 0
    inserted_count = 0
    error_count = 0
    batch_docs = []
    yid_counter = 1

    # 获取数据库中最大的y_id计数器
    try:
        collection = db['release_new']
        # 查找最大的YR编号
        pipeline = [
            {"$match": {"y_id": {"$regex": "^YR\\d+$"}}},
            {"$addFields": {
                "y_id_num": {
                    "$toInt": {"$substr": ["$y_id", 2, -1]}
                }
            }},
            {"$sort": {"y_id_num": -1}},
            {"$limit": 1}
        ]

        result = list(collection.aggregate(pipeline))
        if result:
            max_yid_num = result[0]['y_id_num']
            yid_counter = max_yid_num + 1
            write_output(f"📊 数据库中最大YR编号: YR{max_yid_num}，新记录从 YR{yid_counter} 开始")
        else:
            write_output(f"📊 数据库中无YR记录，从 YR1 开始")

    except Exception as e:
        write_output(f"⚠️ 获取最大y_id失败，从YR1开始: {e}")
        yid_counter = 1

    try:
        # 打开压缩文件
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False

            for line in f:
                if '<release id=' in line:
                    in_release = True
                    buffer = line
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 提取release ID
                    release_id = extract_release_id(buffer)
                    if release_id and release_id in missing_ids:
                        # 生成y_id
                        y_id = f"YR{yid_counter}"

                        # 处理release内容
                        release_doc = process_release_content(buffer, y_id, db)
                        if release_doc:
                            batch_docs.append(release_doc)
                            yid_counter += 1
                            processed_count += 1

                            # 批量插入
                            if len(batch_docs) >= BATCH_SIZE:
                                success, errors = batch_insert_releases(db, batch_docs)
                                inserted_count += success
                                error_count += errors

                                elapsed = time.time() - start_time
                                speed = processed_count / elapsed if elapsed > 0 else 0
                                write_output(f"📈 已处理 {processed_count:,} 条记录，"
                                           f"插入 {inserted_count:,} 条，"
                                           f"错误 {error_count:,} 条，"
                                           f"速度: {speed:.1f} 记录/秒")

                                batch_docs = []

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

            # 处理剩余的批量数据
            if batch_docs:
                success, errors = batch_insert_releases(db, batch_docs)
                inserted_count += success
                error_count += errors

        elapsed_time = time.time() - start_time
        write_output(f"✅ 缺失记录处理完成！")
        write_output(f"📊 处理记录数: {processed_count:,}")
        write_output(f"📊 成功插入: {inserted_count:,}")
        write_output(f"📊 插入错误: {error_count:,}")
        write_output(f"⏱️ 处理耗时: {elapsed_time:.2f} 秒")

        return processed_count, inserted_count, error_count

    except Exception as e:
        write_output(f"❌ 处理缺失记录失败: {e}")
        raise

def generate_final_report(total_xml_count, existing_count, missing_count,
                         processed_count, inserted_count, error_count,
                         total_elapsed_time):
    """生成最终的同步报告"""
    # 计算插入成功率，避免除零错误
    insert_success_rate = f"{(inserted_count / processed_count * 100):.2f}%" if processed_count > 0 else "N/A (无处理记录)"

    # 计算平均处理速度，避免除零错误
    avg_speed = f"{(processed_count / total_elapsed_time):.1f} 记录/秒" if processed_count > 0 else "N/A (无处理记录)"

    report_content = f"""
============================================================
Release数据一致性分析报告
============================================================
分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总处理时长: {total_elapsed_time:.2f} 秒

数据统计:
- XML文件总记录数: {total_xml_count:,}
- 数据库现有记录数: {existing_count:,}
- 识别缺失记录数: {missing_count:,}
- 实际处理记录数: {processed_count:,}
- 成功插入记录数: {inserted_count:,}
- 插入错误记录数: {error_count:,}

一致性分析:
- 数据覆盖率: {(existing_count / total_xml_count * 100):.2f}%
- 缺失数据率: {(missing_count / total_xml_count * 100):.2f}%
- 插入成功率: {insert_success_rate}

性能指标:
- 平均处理速度: {avg_speed}
- 数据库同步完成度: {((existing_count + inserted_count) / total_xml_count * 100):.2f}%

============================================================
"""

    write_report(report_content)
    write_output("📋 详细报告已保存到: " + REPORT_FILE)
    print(report_content)

def main():
    """主函数"""
    print("🔍 Release数据一致性分析工具")
    print("=" * 60)

    overall_start_time = time.time()

    try:
        # 1. 查找XML文件
        xml_file = find_xml_file('releases')
        if not xml_file:
            write_output("❌ 未找到releases XML文件，程序退出")
            return

        # 2. 连接数据库
        client, db = connect_to_mongodb()

        # 3. 获取数据库中现有的ID
        existing_ids = get_existing_ids_from_db(db)

        # 4. 扫描XML文件，找出缺失的ID
        missing_ids, total_xml_count = scan_xml_for_missing_ids(xml_file, existing_ids)

        if not missing_ids:
            write_output("✅ 数据库已包含所有XML记录，数据一致性良好")
            # 生成一致性报告
            total_elapsed_time = time.time() - overall_start_time
            generate_final_report(
                total_xml_count, len(existing_ids), 0,
                0, 0, 0, total_elapsed_time
            )
            return

        # 5. 用户确认是否执行数据同步
        write_output(f"⚠️ 发现数据不一致！")
        write_output(f"📊 XML文件记录数: {total_xml_count:,}")
        write_output(f"📊 数据库记录数: {len(existing_ids):,}")
        write_output(f"📊 缺失记录数: {len(missing_ids):,}")
        write_output(f"💡 建议执行数据同步以确保数据完整性")

        # 自动执行数据同步
        write_output("🚀 开始执行数据同步...")
        processed_count, inserted_count, error_count = process_missing_releases(
            xml_file, missing_ids, db
        )

        # 6. 生成最终报告
        total_elapsed_time = time.time() - overall_start_time
        generate_final_report(
            total_xml_count, len(existing_ids), len(missing_ids),
            processed_count, inserted_count, error_count, total_elapsed_time
        )

        write_output("🎉 数据同步完成！")

    except KeyboardInterrupt:
        write_output("⚠️ 用户中断了程序执行")
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        write_output(f"❌ 程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    finally:
        # 关闭数据库连接
        try:
            if 'client' in locals():
                client.close()
                write_output("🔌 数据库连接已关闭")
        except:
            pass

if __name__ == "__main__":
    main()
