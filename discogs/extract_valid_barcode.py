#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接查找并提取有效barcode的脚本
使用聚合管道直接查找包含非空barcode的记录并更新到release_new表
"""

import os
import json
import time
from pymongo import MongoClient, UpdateOne
from datetime import datetime, timezone

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '5000'))  # 批处理大小
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，0表示处理全部

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'valid_barcode_extraction_output.txt')

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    formatted_message = f"[{timestamp}] {message}"
    
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def process_valid_barcode_extraction():
    """使用聚合管道直接查找并处理有效barcode"""
    start_time = time.time()
    write_output("🚀 开始有效barcode提取和更新任务")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    write_output("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取集合
        release_collection = db['release']
        release_new_collection = db['release_new']
        
        # 检查集合是否存在
        if 'release' not in db.list_collection_names():
            write_output("❌ 错误: release集合不存在")
            return
        
        # 确保release_new集合存在
        if 'release_new' not in db.list_collection_names():
            db.create_collection('release_new')
            write_output("✅ 创建了release_new集合")
        
        write_output("🔍 使用聚合管道查找有效barcode记录...")
        
        # 使用聚合管道直接查找包含有效barcode的记录
        pipeline = [
            # 匹配包含searchRuleRelease字段的记录
            {"$match": {"searchRuleRelease": {"$exists": True}}},
            
            # 投影并提取barcode
            {"$project": {
                "id": 1,
                "barcode": {
                    "$cond": {
                        "if": {"$and": [
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", None]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", ""]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", " "]}
                        ]},
                        "then": "$searchRuleRelease.baseinfo.barcode",
                        "else": None
                    }
                }
            }},
            
            # 只保留有效barcode的记录
            {"$match": {"barcode": {"$ne": None}}},
            
            # 限制记录数（如果设置了MAX_RECORDS）
            *([{"$limit": MAX_RECORDS}] if MAX_RECORDS > 0 else [])
        ]
        
        write_output(f"🔄 开始处理，批处理大小: {BATCH_SIZE}")
        if MAX_RECORDS > 0:
            write_output(f"📝 限制处理记录数: {MAX_RECORDS:,}")
        
        # 处理统计
        processed_count = 0
        updated_count = 0
        error_count = 0
        
        batch_updates = []
        
        # 执行聚合查询
        cursor = release_collection.aggregate(pipeline, allowDiskUse=True)
        
        for record in cursor:
            try:
                record_id = record.get('id')
                barcode = record.get('barcode')
                
                if not record_id or not barcode:
                    continue
                
                # 准备更新操作
                update_doc = {
                    'barcode': barcode,
                    'updated_at': datetime.now(timezone.utc)
                }
                
                batch_updates.append(UpdateOne(
                    {'id': record_id},
                    {'$set': update_doc},
                    upsert=True
                ))
                
                processed_count += 1
                
                # 批量执行更新
                if len(batch_updates) >= BATCH_SIZE:
                    try:
                        result = release_new_collection.bulk_write(batch_updates)
                        updated_count += result.upserted_count + result.modified_count
                        
                        write_output(f"📈 已处理 {processed_count:,} 条记录，更新 {updated_count:,} 条")
                        
                        # 显示一些样本数据
                        if processed_count <= 10:
                            write_output(f"   样本: ID={record_id}, barcode='{barcode}'")
                        
                    except Exception as e:
                        write_output(f"❌ 批量更新失败: {e}")
                        error_count += len(batch_updates)
                    
                    batch_updates = []
                
                # 显示进度
                if processed_count % 1000 == 0:
                    write_output(f"🔄 进度: 已处理 {processed_count:,} 条有效barcode记录...")
                
            except Exception as e:
                write_output(f"❌ 处理记录时出错: {e}", False)
                error_count += 1
                continue
        
        # 处理剩余的批量更新
        if batch_updates:
            try:
                result = release_new_collection.bulk_write(batch_updates)
                updated_count += result.upserted_count + result.modified_count
                
            except Exception as e:
                write_output(f"❌ 最终批量更新失败: {e}")
                error_count += len(batch_updates)
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 输出最终统计
        write_output("\n" + "="*60)
        write_output("📈 有效Barcode提取任务完成统计")
        write_output("="*60)
        write_output(f"✅ 总处理记录数: {processed_count:,}")
        write_output(f"📝 成功更新记录数: {updated_count:,}")
        write_output(f"❌ 错误记录数: {error_count:,}")
        write_output(f"⏱️ 总耗时: {processing_time:.2f} 秒")
        write_output(f"⚡ 平均处理速度: {processed_count/processing_time:.1f} 记录/秒" if processing_time > 0 else "N/A")
        write_output("="*60)
        
        # 验证结果
        if updated_count > 0:
            write_output("\n🔍 验证更新结果...")
            
            # 查询release_new中的样本数据
            sample_results = list(release_new_collection.find(
                {"barcode": {"$exists": True}},
                {"id": 1, "barcode": 1, "updated_at": 1}
            ).limit(5))
            
            if sample_results:
                write_output("✅ release_new表中的样本数据:")
                for i, result in enumerate(sample_results, 1):
                    write_output(f"   {i}. ID: {result.get('id')}, barcode: '{result.get('barcode')}', 更新时间: {result.get('updated_at')}")
            
            # 统计release_new中的barcode记录数
            total_barcode_count = release_new_collection.count_documents({"barcode": {"$exists": True}})
            write_output(f"📊 release_new表中总barcode记录数: {total_barcode_count:,}")
        
    except Exception as e:
        write_output(f"❌ 处理过程中出错: {e}")
        raise
    finally:
        # 关闭数据库连接
        client.close()
        write_output("🔒 已关闭MongoDB连接")
        write_output(f"\n📄 详细日志已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    try:
        process_valid_barcode_extraction()
        print(f"\n✅ 有效Barcode提取任务完成！详细日志请查看: {OUTPUT_FILE}")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        exit(1)
