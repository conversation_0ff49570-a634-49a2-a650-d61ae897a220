#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版 Discogs API 增量获取器

这是一个独立的、可直接运行的版本，解决了之前的依赖问题。

功能：
1. 从MongoDB数据库获取最大release ID
2. 从最大ID+1开始调用Discogs API
3. 处理API响应并保存到CSV
4. 智能的429错误处理（5秒→15秒→60秒）
5. 支持命令行参数控制

使用方法：
python simple_api_fetcher.py --help
python simple_api_fetcher.py --max-records 10
python simple_api_fetcher.py --fresh
"""

import os
import sys
import time
import json
import csv
import logging
import argparse
from datetime import datetime
from pymongo import MongoClient

# 尝试导入requests，如果失败则提示安装
try:
    import requests
except ImportError:
    print("❌ 缺少requests模块，请安装：pip install requests")
    sys.exit(1)

# 配置
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'
API_BASE_URL = 'https://api.discogs.com/releases'
API_RATE_LIMIT = 1.0  # 1秒1次请求
MAX_CONSECUTIVE_404 = int(os.getenv('MAX_CONSECUTIVE_404', '20'))
MAX_RETRIES = 3

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('simple_api_fetcher.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class SimpleAPIClient:
    """简化的API客户端"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'DiscogsFetcher/1.0 +http://example.com'
        })
        self.last_request_time = 0
    
    def get_release(self, release_id):
        """获取单个release数据"""
        # 频率控制
        elapsed = time.time() - self.last_request_time
        if elapsed < API_RATE_LIMIT:
            time.sleep(API_RATE_LIMIT - elapsed)
        
        url = f"{API_BASE_URL}/{release_id}"
        
        for attempt in range(MAX_RETRIES):
            try:
                self.last_request_time = time.time()
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 404:
                    return None
                elif response.status_code == 429:
                    # 智能等待时间
                    wait_times = [5, 15, 60]
                    wait_time = wait_times[min(attempt, len(wait_times) - 1)]
                    logger.warning(f"⏳ API频率限制 (429)，等待 {wait_time} 秒... (尝试 {attempt + 1}/{MAX_RETRIES})")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.warning(f"⚠️ API返回状态码 {response.status_code} for ID {release_id}")
                    return None
                    
            except requests.exceptions.Timeout:
                logger.warning(f"⏰ 请求超时 for ID {release_id} (尝试 {attempt + 1}/{MAX_RETRIES})")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                logger.error(f"❌ 请求异常 for ID {release_id}: {e}")
                return None
        
        logger.error(f"❌ 达到最大重试次数 for ID {release_id}")
        return None

class SimpleFetcher:
    """简化的增量获取器"""
    
    def __init__(self, db):
        self.db = db
        self.collection = db[COLLECTION_NAME]
        self.api_client = SimpleAPIClient()
        self.stats = {
            'processed': 0,
            'successful': 0,
            'not_found': 0,
            'errors': 0,
            'consecutive_404': 0
        }
        self.csv_data = []
    
    def get_max_id(self):
        """获取数据库中最大的ID"""
        try:
            result = self.collection.find().sort("id", -1).limit(1)
            max_doc = list(result)
            if max_doc:
                max_id = max_doc[0]['id']
                logger.info(f"📊 数据库最大ID: {max_id}")
                return max_id
            else:
                logger.info("📊 数据库为空，从ID 1开始")
                return 0
        except Exception as e:
            logger.error(f"❌ 获取最大ID失败: {e}")
            return 0
    
    def process_api_response(self, data):
        """处理API响应数据"""
        try:
            # 简化的数据提取
            processed = {
                'id': data.get('id'),
                'title': data.get('title', ''),
                'year': data.get('year', ''),
                'country': data.get('country', ''),
                'released': data.get('released', ''),
                'notes': data.get('notes', ''),
                'genres': '|'.join(data.get('genres', [])),
                'styles': '|'.join(data.get('styles', [])),
                'artists': '|'.join([artist.get('name', '') for artist in data.get('artists', [])]),
                'labels': '|'.join([label.get('name', '') for label in data.get('labels', [])]),
                'formats': '|'.join([fmt.get('name', '') for fmt in data.get('formats', [])]),
                'created_at': datetime.now().isoformat()
            }
            return processed
        except Exception as e:
            logger.error(f"❌ 数据处理失败: {e}")
            return None
    
    def save_to_csv(self, filename):
        """保存数据到CSV"""
        if not self.csv_data:
            logger.info("📝 没有数据需要保存")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if self.csv_data:
                    fieldnames = self.csv_data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(self.csv_data)
            
            logger.info(f"💾 已保存 {len(self.csv_data)} 条记录到 {filename}")
        except Exception as e:
            logger.error(f"❌ 保存CSV失败: {e}")
    
    def run(self, start_id, max_records=0):
        """运行增量获取"""
        logger.info(f"🚀 开始从ID {start_id} 获取数据")
        if max_records > 0:
            logger.info(f"📊 最大记录数限制: {max_records}")
        
        current_id = start_id
        
        try:
            while True:
                # 检查记录数限制
                if max_records > 0 and self.stats['processed'] >= max_records:
                    logger.info(f"✅ 已达到最大记录数限制: {max_records}")
                    break
                
                # 检查连续404限制
                if self.stats['consecutive_404'] >= MAX_CONSECUTIVE_404:
                    logger.info(f"🛑 连续404达到限制 ({MAX_CONSECUTIVE_404})，停止处理")
                    break
                
                # 获取数据
                logger.info(f"🔍 正在获取ID: {current_id}")
                data = self.api_client.get_release(current_id)
                
                if data:
                    # 成功获取
                    processed = self.process_api_response(data)
                    if processed:
                        self.csv_data.append(processed)
                        self.stats['successful'] += 1
                        self.stats['consecutive_404'] = 0  # 重置连续404计数
                        logger.info(f"✅ 成功获取ID {current_id}: {processed['title']}")
                    else:
                        self.stats['errors'] += 1
                elif data is None:
                    # 404错误
                    self.stats['not_found'] += 1
                    self.stats['consecutive_404'] += 1
                    logger.info(f"⏭️ ID {current_id} 不存在 (404)")
                else:
                    # 其他错误
                    self.stats['errors'] += 1
                
                self.stats['processed'] += 1
                current_id += 1
                
                # 定期显示进度
                if self.stats['processed'] % 10 == 0:
                    logger.info(f"📊 进度: 已处理 {self.stats['processed']}, 成功 {self.stats['successful']}, 404: {self.stats['not_found']}, 连续404: {self.stats['consecutive_404']}")
        
        except KeyboardInterrupt:
            logger.info("🛑 用户中断")
        except Exception as e:
            logger.error(f"❌ 运行错误: {e}")
        
        # 保存结果
        if self.csv_data:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"simple_api_fetcher_{timestamp}.csv"
            self.save_to_csv(filename)
        
        # 显示最终统计
        logger.info("📊 最终统计:")
        logger.info(f"   已处理: {self.stats['processed']}")
        logger.info(f"   成功获取: {self.stats['successful']}")
        logger.info(f"   404跳过: {self.stats['not_found']}")
        logger.info(f"   错误: {self.stats['errors']}")

def connect_to_mongodb():
    """连接MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        # 测试连接
        db.command('ping')
        logger.info(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ MongoDB连接失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版 Discogs API 增量获取器')
    parser.add_argument('--max-records', '-m', type=int, default=0,
                       help='最大处理记录数（0表示无限制）')
    parser.add_argument('--start-id', '-s', type=int, default=0,
                       help='起始ID（0表示从数据库最大ID+1开始）')
    parser.add_argument('--fresh', '-f', action='store_true',
                       help='强制从数据库最大ID开始')
    
    args = parser.parse_args()
    
    logger.info("🚀 简化版 Discogs API 增量获取器")
    logger.info("=" * 50)
    
    # 连接数据库
    client, db = connect_to_mongodb()
    
    try:
        # 创建获取器
        fetcher = SimpleFetcher(db)
        
        # 确定起始ID
        if args.start_id > 0:
            start_id = args.start_id
            logger.info(f"🎯 使用指定起始ID: {start_id}")
        else:
            start_id = fetcher.get_max_id() + 1
            logger.info(f"🎯 从数据库最大ID+1开始: {start_id}")
        
        # 运行获取
        fetcher.run(start_id, args.max_records)
        
    finally:
        client.close()
        logger.info("📊 数据库连接已关闭")
    
    logger.info("🎉 程序执行完成")

if __name__ == "__main__":
    main()
