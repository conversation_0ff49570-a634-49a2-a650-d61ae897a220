#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础CSV解析测试脚本
测试CSV文件中的换行符问题
"""

import csv
import os

def test_standard_csv_reader():
    """测试标准CSV读取器的问题"""
    print("🧪 测试标准CSV读取器...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            row_count = 0
            problem_rows = []
            
            for row_num, row in enumerate(reader, 1):
                row_count += 1
                
                # 检查是否有缺失的必要字段
                if not row.get('id') or not row.get('y_id'):
                    problem_rows.append(row_num)
                    print(f"❌ 行 {row_num} 缺失必要字段: id={row.get('id')}, y_id={row.get('y_id')}")
                    if len(problem_rows) >= 5:  # 只显示前5个问题行
                        break
                
                if row_count >= 20:  # 只测试前20行
                    break
            
            print(f"📊 测试结果: 读取了 {row_count} 行")
            print(f"🚨 发现 {len(problem_rows)} 个问题行")
            
            return len(problem_rows) == 0
            
    except Exception as e:
        print(f"❌ 标准CSV读取失败: {e}")
        return False

def analyze_csv_structure():
    """分析CSV文件结构"""
    print("🔍 分析CSV文件结构...")
    
    csv_file = 'api_releases_补全_20250729_153950.csv'
    if not os.path.exists(csv_file):
        print(f"❌ CSV文件不存在: {csv_file}")
        return False
    
    try:
        # 读取前几行原始内容
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = []
            for i, line in enumerate(f):
                lines.append(line)
                if i >= 10:  # 读取前11行（包括标题）
                    break
        
        print(f"📋 文件前 {len(lines)} 行分析:")
        for i, line in enumerate(lines):
            line_clean = line.replace('\n', '\\n').replace('\r', '\\r')
            print(f"行 {i}: {line_clean[:100]}...")
            
            # 计算逗号数量
            comma_count = line.count(',')
            print(f"     逗号数量: {comma_count}")
            
            if i == 0:
                expected_commas = comma_count
                print(f"     期望逗号数量: {expected_commas}")
            elif comma_count != expected_commas:
                print(f"     ⚠️ 逗号数量不匹配！期望 {expected_commas}，实际 {comma_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析CSV结构失败: {e}")
        return False

def test_problematic_notes():
    """测试包含换行符的notes字段"""
    print("🧪 测试notes字段换行符问题...")
    
    # 模拟问题数据
    test_data = '''id,y_id,title,notes
1,YRD1,Test1,"Simple note"
2,YRD2,Test2,"Note with
newline"
3,YRD3,Test3,"Another simple note"'''
    
    # 写入临时文件
    temp_file = 'temp_test.csv'
    with open(temp_file, 'w', encoding='utf-8') as f:
        f.write(test_data)
    
    try:
        # 测试标准CSV读取
        with open(temp_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        print(f"📊 读取到 {len(rows)} 行")
        for i, row in enumerate(rows):
            print(f"行 {i+1}: id={row.get('id')}, y_id={row.get('y_id')}, notes={repr(row.get('notes'))}")
        
        # 清理临时文件
        os.remove(temp_file)
        
        # 如果读取的行数不是3，说明有问题
        if len(rows) != 3:
            print("❌ 换行符导致行数解析错误")
            return False
        else:
            print("✅ 换行符处理正常")
            return True
            
    except Exception as e:
        print(f"❌ 测试notes字段失败: {e}")
        if os.path.exists(temp_file):
            os.remove(temp_file)
        return False

def main():
    """主测试函数"""
    print("🚀 开始基础CSV解析测试")
    print("=" * 60)
    
    tests = [
        ("CSV文件结构分析", analyze_csv_structure),
        ("notes字段换行符测试", test_problematic_notes),
        ("标准CSV读取器测试", test_standard_csv_reader),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试总结: {passed}/{total} 个测试通过")
    
    return passed == total

if __name__ == '__main__':
    main()
