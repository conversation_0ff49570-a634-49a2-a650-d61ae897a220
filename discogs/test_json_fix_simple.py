#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import re

def test_json_fix():
    """测试JSON修复逻辑"""
    
    # 测试具体的问题JSON
    test_json = '[{"artist_id": 3394051, "name": "The California Ramblers", "role": ", "anv": "}]'
    print('原始:', test_json)
    
    # 步骤1: 处理双引号转义
    fixed = test_json.replace('""', '"')
    print('步骤1:', fixed)
    
    # 步骤2: 修复不完整的空字符串
    fixed = re.sub(r':\s*"\s*(?=[,}])', r': ""', fixed)
    print('步骤2:', fixed)
    
    try:
        parsed = json.loads(fixed)
        print('✅ 成功:', parsed[0])
        return True
    except Exception as e:
        print('❌ 失败:', e)
        return False

if __name__ == '__main__':
    test_json_fix()
