#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试脚本 - 验证基本功能
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        import pymongo
        print("✅ pymongo 导入成功")
    except ImportError as e:
        print(f"❌ pymongo 导入失败: {e}")
        return False
    
    try:
        from api_release_补全器 import DiscogsAPIClient
        print("✅ DiscogsAPIClient 导入成功")
    except ImportError as e:
        print(f"❌ DiscogsAPIClient 导入失败: {e}")
        return False
    
    return True

def test_api_basic():
    """测试基本API功能"""
    print("\n🧪 测试基本API功能...")
    
    try:
        import requests
        
        # 简单的API测试
        url = "https://api.discogs.com/releases/49"
        headers = {
            'User-Agent': 'TestScript/1.0 +https://example.com/contact'
        }
        
        print(f"📡 测试API调用: {url}")
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API调用成功")
            print(f"   标题: {data.get('title', 'N/A')}")
            print(f"   ID: {data.get('id', 'N/A')}")
            return True
        else:
            print(f"❌ API调用失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_database_basic():
    """测试基本数据库功能"""
    print("\n🧪 测试基本数据库功能...")
    
    try:
        from pymongo import MongoClient
        
        # 数据库连接测试
        MONGO_URI = '**********************************************************'
        
        print(f"🔗 测试数据库连接...")
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.admin.command('ping')
        print("✅ 数据库连接成功")
        
        # 测试集合访问
        db = client['music_test']
        collection = db['release_new']
        
        count = collection.count_documents({}, limit=1)
        print(f"✅ 集合访问成功，记录数: {count:,}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 简单测试脚本启动")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 3
    
    # 测试1: 导入
    if test_imports():
        tests_passed += 1
    
    # 测试2: API
    if test_api_basic():
        tests_passed += 1
    
    # 测试3: 数据库
    if test_database_basic():
        tests_passed += 1
    
    print(f"\n📊 测试结果: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有基础测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
    
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
