#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CSV到MongoDB批量导入工具

功能：
1. 读取CSV文件中的release数据
2. 将CSV数据转换为适合MongoDB的文档格式
3. 批量插入到MongoDB的release_new集合中
4. 处理JSON字段、数据类型转换和错误处理
5. 提供进度显示和详细的统计报告

特性：
- 内存效率处理，分批读取和插入
- 完善的错误处理和重试机制
- 支持重复数据检测和处理
- 详细的日志记录和进度显示
- 支持测试模式和配置选项

作者：AI Assistant
创建时间：2025-07-29
"""

import os
import sys
import csv
import json
import time
import logging
import argparse
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from pymongo import MongoClient
from pymongo.errors import BulkWriteError, DuplicateKeyError
import pandas as pd
import numpy as np

# 导入项目枚举
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from release.enums import Source, Permissions, Status

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
COLLECTION_NAME = 'release_new'

# 默认配置
DEFAULT_BATCH_SIZE = 100
DEFAULT_CSV_FILE = 'api_releases_补全_20250729_153950.csv'
DEFAULT_LOG_FILE = 'csv_import_log.txt'

# 日志配置
logging.basicConfig(
    level=logging.INFO,  # 恢复INFO级别
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(DEFAULT_LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CSVToMongoImporter:
    """CSV到MongoDB导入器"""
    
    def __init__(self, csv_file_path: str, batch_size: int = DEFAULT_BATCH_SIZE, 
                 test_mode: bool = False, skip_duplicates: bool = True):
        self.csv_file_path = csv_file_path
        self.batch_size = batch_size
        self.test_mode = test_mode
        self.skip_duplicates = skip_duplicates
        
        # 统计信息
        self.stats = {
            'total_rows': 0,
            'processed_rows': 0,
            'successful_inserts': 0,
            'failed_inserts': 0,
            'skipped_duplicates': 0,
            'validation_errors': 0,
            'start_time': None,
            'end_time': None,
            # 详细错误分类
            'error_details': {
                'field_conversion_errors': 0,      # 字段转换错误
                'json_parse_errors': 0,            # JSON解析错误
                'required_field_missing': 0,       # 必填字段缺失
                'data_type_errors': 0,             # 数据类型错误
                'format_validation_errors': 0,     # 格式验证错误
                'database_insert_errors': 0        # 数据库插入错误
            },
            'failed_rows': []  # 存储失败的行数据，用于重写CSV文件
        }
        
        # MongoDB连接
        self.client = None
        self.db = None
        self.collection = None
        
    def connect_to_mongodb(self) -> bool:
        """连接到MongoDB并返回连接状态"""
        try:
            self.client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[DB_NAME]
            self.collection = self.db[COLLECTION_NAME]
            
            # 确保集合存在
            if COLLECTION_NAME not in self.db.list_collection_names():
                self.db.create_collection(COLLECTION_NAME)
                logger.info(f"✅ 创建集合: {COLLECTION_NAME}")
            
            logger.info(f"✅ 成功连接到MongoDB: {DB_NAME}.{COLLECTION_NAME}")
            return True
            
        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            return False
    
    def parse_json_field(self, field_value: str) -> Any:
        """解析JSON字段，处理CSV中的JSON字符串"""
        if not field_value or field_value.strip() == '':
            return []

        original_value = field_value
        try:
            # 处理CSV中的双引号转义
            # CSV格式：字段被双引号包围，内部的双引号被转义为""
            if field_value.startswith('"') and field_value.endswith('"'):
                field_value = field_value[1:-1]

            # 简化的CSV转义处理：将所有的""替换为"
            field_value = field_value.replace('""', '"')

            # 调试输出
            logger.debug(f"🔍 解析JSON字段，原始值: {original_value[:200]}...")
            logger.debug(f"🔍 处理后的值: {field_value[:200]}...")

            # 直接尝试解析JSON
            parsed = json.loads(field_value)
            return parsed if parsed is not None else []

        except (json.JSONDecodeError, ValueError) as e:
            # 详细的错误信息
            error_msg = f"JSON解析失败: {str(e)}"
            logger.warning(f"⚠️ {error_msg}")
            logger.debug(f"🔍 失败的JSON字符串: {field_value}")

            # 尝试修复常见的JSON问题
            try:
                fixed_value = self.fix_json_issues(field_value)
                if fixed_value != field_value:
                    logger.debug(f"🔧 尝试修复JSON: {fixed_value[:200]}...")
                    parsed = json.loads(fixed_value)
                    logger.info(f"✅ JSON修复成功")
                    return parsed if parsed is not None else []
            except Exception as fix_error:
                logger.debug(f"🔧 JSON修复失败: {fix_error}")

            return None  # 返回None表示解析失败，区别于空数组[]

    def fix_json_issues(self, json_str: str) -> str:
        """尝试修复常见的JSON格式问题"""
        import re

        fixed = json_str

        # 1. 修复空字符串值的问题
        # 模式: ": ", -> ": "",
        fixed = re.sub(r':\s*"\s*,', r': "",', fixed)
        fixed = re.sub(r':\s*"\s*}', r': ""}', fixed)
        fixed = re.sub(r':\s*"\s*]', r': ""]', fixed)

        # 2. 修复尺寸字符串中的转义问题
        # 例如: ["7\" -> ["7\""（正确的JSON转义）
        # 这种情况出现在CSV中存储了不完整的转义，特别是在数组中
        fixed = re.sub(r'\["(\d+)\\"', r'["\1\\""', fixed)  # ["7\" -> ["7\""

        return fixed

    def clean_text_field(self, text: str) -> str:
        """清理文本字段中的换行符和特殊字符"""
        if not text or pd.isna(text):
            return ""

        # 规范化换行符
        text = str(text).replace('\r\n', '\n').replace('\r', '\n')
        # 保留换行符，但确保它们不会破坏CSV结构
        return text.strip()

    def convert_csv_row_to_document(self, row: Dict[str, str]) -> tuple[Optional[Dict[str, Any]], str]:
        """
        将CSV行转换为MongoDB文档
        返回: (文档对象, 错误信息)
        """
        try:
            # 基础字段转换，增加详细的错误处理
            doc = {}

            # ID字段转换
            try:
                if not row.get('id') or not row['id'].strip():
                    self.stats['error_details']['required_field_missing'] += 1
                    return None, "ID字段为空"
                doc['id'] = int(row['id'].strip())
            except (ValueError, TypeError) as e:
                self.stats['error_details']['field_conversion_errors'] += 1
                return None, f"ID字段转换失败: {row.get('id')} - {str(e)}"

            # y_id字段转换
            if not row.get('y_id') or not row['y_id'].strip():
                self.stats['error_details']['required_field_missing'] += 1
                return None, "y_id字段为空"
            doc['y_id'] = row['y_id'].strip()

            # 字符串字段转换
            try:
                doc['title'] = row['title'].strip() if row.get('title') else None
                doc['country'] = row['country'].strip() if row.get('country') else None
                doc['notes'] = self.clean_text_field(row['notes']) if row.get('notes') else None
                doc['discogs_status'] = row['discogs_status'].strip() if row.get('discogs_status') else None
            except Exception as e:
                self.stats['error_details']['field_conversion_errors'] += 1
                return None, f"字符串字段转换失败: {str(e)}"

            # 年份字段转换
            try:
                if row.get('year') and row['year'].strip() and row['year'].isdigit():
                    year_val = int(row['year'])
                    if 1900 <= year_val <= 2030:
                        doc['year'] = year_val
                    else:
                        doc['year'] = None
                        logger.warning(f"⚠️ 年份超出合理范围: {year_val}")
                else:
                    doc['year'] = None
            except (ValueError, TypeError) as e:
                self.stats['error_details']['field_conversion_errors'] += 1
                return None, f"年份字段转换失败: {row.get('year')} - {str(e)}"

            # JSON数组字段转换
            json_fields = ['artists', 'extra_artists', 'labels', 'companies',
                          'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']

            for field in json_fields:
                if field in row:
                    try:
                        parsed_json = self.parse_json_field(row[field])
                        if parsed_json is None:  # parse_json_field返回None表示解析失败
                            self.stats['error_details']['json_parse_errors'] += 1
                            return None, f"JSON字段 {field} 解析失败: {row[field][:100]}..."
                        doc[field] = parsed_json
                    except Exception as e:
                        self.stats['error_details']['json_parse_errors'] += 1
                        return None, f"JSON字段 {field} 处理异常: {str(e)}"

            # master_id字段转换
            try:
                if row.get('master_id') and row['master_id'].strip():
                    if row['master_id'].isdigit():
                        doc['master_id'] = int(row['master_id'])
                    else:
                        doc['master_id'] = None
                        logger.warning(f"⚠️ master_id格式无效: {row['master_id']}")
                else:
                    doc['master_id'] = None
            except Exception as e:
                self.stats['error_details']['field_conversion_errors'] += 1
                return None, f"master_id字段转换失败: {str(e)}"

            # 权限和状态字段转换
            try:
                doc['images_permissions'] = int(row['images_permissions']) if row.get('images_permissions') and row['images_permissions'].isdigit() else Permissions.ALL_VISIBLE.value
                doc['permissions'] = int(row['permissions']) if row.get('permissions') and row['permissions'].isdigit() else Permissions.ALL_VISIBLE.value
                doc['source'] = int(row['source']) if row.get('source') and row['source'].isdigit() else Source.DISCOGS.value
            except Exception as e:
                self.stats['error_details']['field_conversion_errors'] += 1
                return None, f"权限字段转换失败: {str(e)}"

            # 时间戳字段转换
            try:
                if row.get('created_at') and row['created_at'].strip():
                    try:
                        doc['created_at'] = datetime.fromisoformat(row['created_at'].replace('Z', '+00:00'))
                    except ValueError as e:
                        logger.warning(f"⚠️ created_at时间格式无效: {row['created_at']}")
                        doc['created_at'] = datetime.now(timezone.utc)
                else:
                    doc['created_at'] = datetime.now(timezone.utc)

                if row.get('updated_at') and row['updated_at'].strip():
                    try:
                        doc['updated_at'] = datetime.fromisoformat(row['updated_at'].replace('Z', '+00:00'))
                    except ValueError as e:
                        logger.warning(f"⚠️ updated_at时间格式无效: {row['updated_at']}")
                        doc['updated_at'] = datetime.now(timezone.utc)
                else:
                    doc['updated_at'] = datetime.now(timezone.utc)
            except Exception as e:
                self.stats['error_details']['field_conversion_errors'] += 1
                return None, f"时间戳字段转换失败: {str(e)}"

            return doc, ""

        except Exception as e:
            self.stats['error_details']['field_conversion_errors'] += 1
            error_msg = f"行转换过程中发生未预期错误: {str(e)}"
            logger.error(f"❌ {error_msg}, 行数据: {row}")
            return None, error_msg
    
    def validate_document(self, doc: Dict[str, Any]) -> tuple[bool, str]:
        """
        验证文档是否包含必要字段并进行完整的数据验证
        返回: (是否有效, 错误信息)
        """
        try:
            # 1. 验证必填字段
            required_fields = ['id', 'y_id']
            for field in required_fields:
                if not doc.get(field):
                    self.stats['error_details']['required_field_missing'] += 1
                    error_msg = f"缺少必要字段 {field}"
                    logger.warning(f"⚠️ {error_msg}: {doc.get('id', 'unknown')}")
                    return False, error_msg

            # 2. 验证数据类型
            # ID字段必须是整数
            if not isinstance(doc.get('id'), int) or doc['id'] <= 0:
                self.stats['error_details']['data_type_errors'] += 1
                error_msg = f"ID字段必须是正整数: {doc.get('id')}"
                logger.warning(f"⚠️ {error_msg}")
                return False, error_msg

            # y_id字段必须是非空字符串
            if not isinstance(doc.get('y_id'), str) or not doc['y_id'].strip():
                self.stats['error_details']['data_type_errors'] += 1
                error_msg = f"y_id字段必须是非空字符串: {doc.get('y_id')}"
                logger.warning(f"⚠️ {error_msg}")
                return False, error_msg

            # 3. 验证年份字段
            if doc.get('year') is not None:
                if not isinstance(doc['year'], int) or doc['year'] < 1900 or doc['year'] > 2030:
                    self.stats['error_details']['format_validation_errors'] += 1
                    error_msg = f"年份字段格式无效: {doc.get('year')}"
                    logger.warning(f"⚠️ {error_msg}")
                    return False, error_msg

            # 4. 验证JSON数组字段
            json_array_fields = ['artists', 'extra_artists', 'labels', 'companies',
                               'formats', 'genres', 'styles', 'identifiers', 'tracklist', 'images']
            for field in json_array_fields:
                if field in doc and doc[field] is not None:
                    if not isinstance(doc[field], list):
                        self.stats['error_details']['data_type_errors'] += 1
                        error_msg = f"字段 {field} 必须是数组类型: {type(doc[field])}"
                        logger.warning(f"⚠️ {error_msg}")
                        return False, error_msg

            # 5. 验证枚举字段
            enum_fields = ['images_permissions', 'permissions', 'source']
            for field in enum_fields:
                if doc.get(field) is not None:
                    if not isinstance(doc[field], int) or doc[field] < 0:
                        self.stats['error_details']['format_validation_errors'] += 1
                        error_msg = f"枚举字段 {field} 必须是非负整数: {doc.get(field)}"
                        logger.warning(f"⚠️ {error_msg}")
                        return False, error_msg

            # 6. 验证时间戳字段
            timestamp_fields = ['created_at', 'updated_at']
            for field in timestamp_fields:
                if doc.get(field) is not None:
                    if not isinstance(doc[field], datetime):
                        self.stats['error_details']['data_type_errors'] += 1
                        error_msg = f"时间戳字段 {field} 必须是datetime类型: {type(doc[field])}"
                        logger.warning(f"⚠️ {error_msg}")
                        return False, error_msg

            return True, ""

        except Exception as e:
            self.stats['error_details']['format_validation_errors'] += 1
            error_msg = f"文档验证过程中发生异常: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return False, error_msg
    
    def batch_insert_documents(self, documents: List[Dict[str, Any]]) -> tuple:
        """批量插入文档，返回(成功数量, 失败数量)"""
        if not documents:
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            if self.skip_duplicates:
                # 使用insert_many with ordered=False来跳过重复项
                result = self.collection.insert_many(documents, ordered=False)
                success_count = len(result.inserted_ids)
                
            else:
                # 逐个插入以处理重复项
                for doc in documents:
                    try:
                        self.collection.insert_one(doc)
                        success_count += 1
                    except DuplicateKeyError:
                        self.stats['skipped_duplicates'] += 1
                        logger.debug(f"跳过重复记录: ID={doc.get('id')}")
                    except Exception as e:
                        error_count += 1
                        logger.error(f"❌ 单条插入失败: {e}")
                        
        except BulkWriteError as e:
            # 处理批量写入错误
            success_count = e.details.get('nInserted', 0)
            error_count = len(documents) - success_count
            
            # 统计重复键错误
            for error in e.details.get('writeErrors', []):
                if error.get('code') == 11000:  # 重复键错误
                    self.stats['skipped_duplicates'] += 1
                    error_count -= 1  # 重复不算作错误
                    
            logger.info(f"批量插入部分成功: 成功 {success_count}, 重复 {self.stats['skipped_duplicates']}")
            
        except Exception as e:
            logger.error(f"❌ 批量插入失败: {e}")
            error_count = len(documents)
        
        return success_count, error_count

    def process_batch_with_error_handling(self, batch_data: List[tuple]) -> tuple:
        """
        处理批量数据并进行错误处理
        batch_data: [(doc, row_dict, row_num), ...]
        返回: (成功数量, 失败数量)
        """
        if not batch_data:
            return 0, 0

        # 提取文档用于批量插入
        documents = [item[0] for item in batch_data]

        try:
            # 测试模式下模拟插入
            if self.test_mode:
                # 模拟成功插入所有文档
                success_count = len(documents)
                error_count = 0
                logger.debug(f"🧪 测试模式：模拟插入 {success_count} 条文档")

            else:
                # 尝试批量插入
                if self.skip_duplicates:
                    result = self.collection.insert_many(documents, ordered=False)
                    success_count = len(result.inserted_ids)
                    error_count = 0

                    # 如果成功插入的数量少于总数，说明有重复或其他错误
                    if success_count < len(documents):
                        self.stats['skipped_duplicates'] += (len(documents) - success_count)

                else:
                    # 逐个插入以处理重复项和其他错误
                    success_count = 0
                    error_count = 0

                    for doc, row_dict, row_num in batch_data:
                        try:
                            self.collection.insert_one(doc)
                            success_count += 1
                        except DuplicateKeyError:
                            self.stats['skipped_duplicates'] += 1
                            logger.debug(f"跳过重复记录: ID={doc.get('id')}")
                        except Exception as e:
                            error_count += 1
                            self.stats['error_details']['database_insert_errors'] += 1
                            error_msg = f"数据库插入失败: {str(e)}"
                            self.collect_failed_row(row_dict, error_msg, row_num)
                            logger.error(f"❌ 单条插入失败: {error_msg}")

        except BulkWriteError as e:
            # 处理批量写入错误
            success_count = e.details.get('nInserted', 0)

            # 处理写入错误的文档
            write_errors = e.details.get('writeErrors', [])
            for error in write_errors:
                error_index = error.get('index', 0)
                if error_index < len(batch_data):
                    _, row_dict, row_num = batch_data[error_index]

                    if error.get('code') == 11000:  # 重复键错误
                        self.stats['skipped_duplicates'] += 1
                        logger.debug(f"跳过重复记录: 行 {row_num}")
                    else:
                        self.stats['error_details']['database_insert_errors'] += 1
                        error_msg = f"数据库批量插入错误: {error.get('errmsg', '未知错误')}"
                        self.collect_failed_row(row_dict, error_msg, row_num)
                        logger.error(f"❌ 批量插入错误: {error_msg}")

            error_count = len(write_errors) - self.stats['skipped_duplicates']

        except Exception as e:
            # 其他异常，所有文档都失败
            error_count = len(batch_data)
            success_count = 0
            self.stats['error_details']['database_insert_errors'] += error_count

            for _, row_dict, row_num in batch_data:
                error_msg = f"批量插入异常: {str(e)}"
                self.collect_failed_row(row_dict, error_msg, row_num)

            logger.error(f"❌ 批量插入异常: {e}")

        return success_count, error_count

    def collect_failed_row(self, row_dict: Dict[str, str], error_msg: str, row_num: int):
        """收集失败的行数据"""
        failed_row_info = {
            'row_number': row_num,
            'data': row_dict,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }
        self.stats['failed_rows'].append(failed_row_info)
        logger.debug(f"收集失败行 {row_num}: {error_msg}")

    def save_failed_rows_to_csv(self, original_csv_path: str) -> bool:
        """将失败的行保存回CSV文件，覆盖原文件"""
        try:
            if not self.stats['failed_rows']:
                # 如果没有失败的行，创建一个空的CSV文件（只有标题行）
                logger.info("✅ 所有行都处理成功，创建空的CSV文件")

                # 读取原始文件的标题行
                with open(original_csv_path, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f, quoting=csv.QUOTE_ALL)
                    headers = next(reader)

                # 写入只有标题行的空文件
                with open(original_csv_path, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f, quoting=csv.QUOTE_ALL)
                    writer.writerow(headers)

                return True

            # 提取失败行的数据
            failed_data = [row_info['data'] for row_info in self.stats['failed_rows']]

            # 获取字段名（从第一行失败数据中获取）
            if failed_data:
                fieldnames = list(failed_data[0].keys())

                # 写入CSV文件
                with open(original_csv_path, 'w', encoding='utf-8', newline='') as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
                    writer.writeheader()
                    writer.writerows(failed_data)

                logger.info(f"✅ 已将 {len(failed_data)} 行失败数据保存回CSV文件: {original_csv_path}")
                return True

        except Exception as e:
            logger.error(f"❌ 保存失败行到CSV文件时发生错误: {e}")
            return False

    def get_csv_row_count(self) -> int:
        """获取CSV文件的总行数（不包括标题行）"""
        try:
            # 使用pandas快速获取行数
            df = pd.read_csv(
                self.csv_file_path,
                encoding='utf-8',
                quoting=csv.QUOTE_ALL,
                na_filter=False,
                dtype=str
            )
            return len(df)
        except Exception as e:
            logger.error(f"❌ 无法读取CSV文件行数: {e}")
            return 0

    def process_csv_file(self) -> bool:
        """处理CSV文件的主函数"""
        logger.info("🚀 开始CSV到MongoDB导入过程")
        logger.info("=" * 60)

        # 检查CSV文件是否存在
        if not os.path.exists(self.csv_file_path):
            logger.error(f"❌ CSV文件不存在: {self.csv_file_path}")
            return False

        # 连接数据库（测试模式下可以跳过）
        if not self.test_mode:
            if not self.connect_to_mongodb():
                return False
        else:
            logger.info("🧪 测试模式：跳过MongoDB连接，仅测试数据验证逻辑")

        if self.test_mode:
            logger.info("🧪 测试模式已启用，将只处理前100行")

        # 开始处理
        self.stats['start_time'] = datetime.now()
        batch_documents = []

        try:
            # 使用pandas读取CSV文件，能更好地处理包含换行符的字段
            logger.info("📖 使用pandas读取CSV文件...")
            df = pd.read_csv(
                self.csv_file_path,
                encoding='utf-8',
                quoting=csv.QUOTE_ALL,  # 处理包含换行符的字段
                na_filter=False,  # 防止空字符串被转换为NaN
                dtype=str  # 所有字段先读取为字符串
            )

            # 更新总行数
            self.stats['total_rows'] = len(df)
            logger.info(f"📊 使用pandas读取CSV文件，总行数: {self.stats['total_rows']:,}")

            for row_num, (_, row) in enumerate(df.iterrows(), 1):
                # 测试模式限制
                if self.test_mode and row_num > 100:
                    break

                # 将pandas Series转换为字典
                row_dict = row.to_dict()

                # 转换行数据
                doc, conversion_error = self.convert_csv_row_to_document(row_dict)

                if doc is None:
                    # 转换失败，收集失败行
                    self.collect_failed_row(row_dict, conversion_error, row_num)
                    self.stats['validation_errors'] += 1
                    continue

                # 验证文档
                is_valid, validation_error = self.validate_document(doc)
                if not is_valid:
                    # 验证失败，收集失败行
                    self.collect_failed_row(row_dict, validation_error, row_num)
                    self.stats['validation_errors'] += 1
                    continue

                # 文档有效，添加到批量插入队列
                batch_documents.append((doc, row_dict, row_num))  # 保存原始行数据用于错误处理
                self.stats['processed_rows'] += 1

                # 批量插入
                if len(batch_documents) >= self.batch_size:
                    success, error = self.process_batch_with_error_handling(batch_documents)
                    self.stats['successful_inserts'] += success
                    self.stats['failed_inserts'] += error

                    # 显示进度
                    total_processed = self.stats['processed_rows'] + self.stats['validation_errors']
                    progress = (total_processed / self.stats['total_rows']) * 100
                    logger.info(f"📈 进度: {total_processed:,}/"
                              f"{self.stats['total_rows']:,} ({progress:.1f}%) - "
                              f"成功: {self.stats['successful_inserts']:,}, "
                              f"失败: {len(self.stats['failed_rows']):,}")

                    batch_documents = []

            # 处理剩余的文档
            if batch_documents:
                success, error = self.process_batch_with_error_handling(batch_documents)
                self.stats['successful_inserts'] += success
                self.stats['failed_inserts'] += error

        except Exception as e:
            logger.error(f"❌ 处理CSV文件时发生错误: {e}")
            return False

        finally:
            self.stats['end_time'] = datetime.now()
            if self.client:
                self.client.close()

        # 保存失败的行回CSV文件
        logger.info("💾 正在保存失败的行到CSV文件...")
        if not self.save_failed_rows_to_csv(self.csv_file_path):
            logger.error("❌ 保存失败行到CSV文件失败")
            return False

        # 显示最终统计
        self.print_final_stats()
        return True

    def print_final_stats(self):
        """打印最终统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time']
        total_failed = len(self.stats['failed_rows'])

        logger.info("\n" + "=" * 80)
        logger.info("📊 CSV到MongoDB导入完成 - 详细统计报告")
        logger.info("=" * 80)

        # 基本信息
        logger.info(f"📁 CSV文件: {self.csv_file_path}")
        logger.info(f"⏱️  处理时间: {duration}")
        logger.info(f"🧪 测试模式: {'是' if self.test_mode else '否'}")

        # 数据处理统计
        logger.info("\n📊 数据处理统计:")
        logger.info(f"  总行数: {self.stats['total_rows']:,}")
        logger.info(f"  成功处理: {self.stats['successful_inserts']:,}")
        logger.info(f"  处理失败: {total_failed:,}")
        logger.info(f"  跳过重复: {self.stats['skipped_duplicates']:,}")

        # 成功率计算
        total_processed = self.stats['successful_inserts'] + total_failed
        if total_processed > 0:
            success_rate = (self.stats['successful_inserts'] / total_processed) * 100
            logger.info(f"  成功率: {success_rate:.1f}%")

        # 详细错误分析
        if total_failed > 0:
            logger.info("\n❌ 详细错误分析:")
            error_details = self.stats['error_details']
            logger.info(f"  字段转换错误: {error_details['field_conversion_errors']:,}")
            logger.info(f"  JSON解析错误: {error_details['json_parse_errors']:,}")
            logger.info(f"  必填字段缺失: {error_details['required_field_missing']:,}")
            logger.info(f"  数据类型错误: {error_details['data_type_errors']:,}")
            logger.info(f"  格式验证错误: {error_details['format_validation_errors']:,}")
            logger.info(f"  数据库插入错误: {error_details['database_insert_errors']:,}")

            # 错误分布分析
            total_errors = sum(error_details.values())
            if total_errors > 0:
                logger.info("\n📈 错误类型分布:")
                for error_type, count in error_details.items():
                    if count > 0:
                        percentage = (count / total_errors) * 100
                        error_name = {
                            'field_conversion_errors': '字段转换错误',
                            'json_parse_errors': 'JSON解析错误',
                            'required_field_missing': '必填字段缺失',
                            'data_type_errors': '数据类型错误',
                            'format_validation_errors': '格式验证错误',
                            'database_insert_errors': '数据库插入错误'
                        }.get(error_type, error_type)
                        logger.info(f"  {error_name}: {count:,} ({percentage:.1f}%)")

        # 文件处理结果
        logger.info("\n📄 文件处理结果:")
        if total_failed == 0:
            logger.info("  ✅ 所有数据处理成功，CSV文件已清空（仅保留标题行）")
        else:
            logger.info(f"  ⚠️  CSV文件已更新，保留了 {total_failed:,} 行失败数据")
            logger.info("  💡 建议检查失败数据并修复后重新运行导入")

        # 性能统计
        if duration.total_seconds() > 0:
            rows_per_second = self.stats['total_rows'] / duration.total_seconds()
            logger.info(f"\n⚡ 性能统计:")
            logger.info(f"  处理速度: {rows_per_second:.1f} 行/秒")

        logger.info("=" * 80)

        # 生成处理报告文件
        self.generate_processing_report()

    def generate_processing_report(self):
        """生成详细的处理报告文件"""
        try:
            report_filename = f"csv_import_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            duration = self.stats['end_time'] - self.stats['start_time']
            total_failed = len(self.stats['failed_rows'])

            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write("CSV到MongoDB导入处理报告\n")
                f.write("=" * 50 + "\n\n")

                # 基本信息
                f.write(f"处理时间: {self.stats['start_time']} - {self.stats['end_time']}\n")
                f.write(f"处理耗时: {duration}\n")
                f.write(f"CSV文件: {self.csv_file_path}\n")
                f.write(f"测试模式: {'是' if self.test_mode else '否'}\n\n")

                # 统计信息
                f.write("处理统计:\n")
                f.write(f"  总行数: {self.stats['total_rows']:,}\n")
                f.write(f"  成功插入: {self.stats['successful_inserts']:,}\n")
                f.write(f"  处理失败: {total_failed:,}\n")
                f.write(f"  跳过重复: {self.stats['skipped_duplicates']:,}\n\n")

                # 错误详情
                if total_failed > 0:
                    f.write("错误详情:\n")
                    error_details = self.stats['error_details']
                    for error_type, count in error_details.items():
                        if count > 0:
                            error_name = {
                                'field_conversion_errors': '字段转换错误',
                                'json_parse_errors': 'JSON解析错误',
                                'required_field_missing': '必填字段缺失',
                                'data_type_errors': '数据类型错误',
                                'format_validation_errors': '格式验证错误',
                                'database_insert_errors': '数据库插入错误'
                            }.get(error_type, error_type)
                            f.write(f"  {error_name}: {count:,}\n")

                    f.write("\n失败行详情:\n")
                    for i, failed_row in enumerate(self.stats['failed_rows'][:10], 1):  # 只显示前10个
                        f.write(f"  {i}. 行号 {failed_row['row_number']}: {failed_row['error']}\n")

                    if len(self.stats['failed_rows']) > 10:
                        f.write(f"  ... 还有 {len(self.stats['failed_rows']) - 10} 行失败数据\n")

                f.write(f"\n报告生成时间: {datetime.now()}\n")

            logger.info(f"📄 处理报告已生成: {report_filename}")

        except Exception as e:
            logger.error(f"❌ 生成处理报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CSV到MongoDB批量导入工具')
    parser.add_argument('--csv-file', '-f', default=DEFAULT_CSV_FILE,
                       help=f'CSV文件路径 (默认: {DEFAULT_CSV_FILE})')
    parser.add_argument('--batch-size', '-b', type=int, default=DEFAULT_BATCH_SIZE,
                       help=f'批量插入大小 (默认: {DEFAULT_BATCH_SIZE})')
    parser.add_argument('--test-mode', '-t', action='store_true',
                       help='测试模式，只处理前100行')
    parser.add_argument('--allow-duplicates', '-d', action='store_true',
                       help='允许重复数据（默认跳过重复）')
    parser.add_argument('--log-file', '-l', default=DEFAULT_LOG_FILE,
                       help=f'日志文件路径 (默认: {DEFAULT_LOG_FILE})')

    args = parser.parse_args()

    # 更新日志文件配置
    if args.log_file != DEFAULT_LOG_FILE:
        # 重新配置日志处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(args.log_file, encoding='utf-8'),
                logging.StreamHandler()
            ],
            force=True
        )

    # 创建导入器实例
    importer = CSVToMongoImporter(
        csv_file_path=args.csv_file,
        batch_size=args.batch_size,
        test_mode=args.test_mode,
        skip_duplicates=not args.allow_duplicates
    )

    # 执行导入
    success = importer.process_csv_file()

    if success:
        logger.info("🎉 CSV导入完成！")
        sys.exit(0)
    else:
        logger.error("❌ CSV导入失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
